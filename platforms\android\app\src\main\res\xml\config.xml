<?xml version='1.0' encoding='utf-8'?>
<widget id="com.yingjiang.app" version="3.34" xmlns="http://www.w3.org/ns/widgets" xmlns:cdv="http://cordova.apache.org/ns/1.0">
    <feature name="MiPushPlugin">
        <param name="android-package" value="com.ct.cordova.mipush.MiPushPlugin" />
    </feature>
    <feature name="HotCodePush">
        <param name="android-package" value="com.nordnetab.chcp.main.HotCodePushPlugin" />
        <param name="onload" value="true" />
    </feature>
    <feature name="BaiduGeolocation">
        <param name="android-package" value="com.lai.geolocation.baidu.GeolocationPlugin" />
    </feature>
    <feature name="Camera">
        <param name="android-package" value="org.apache.cordova.camera.CameraLauncher" />
    </feature>
    <feature name="Device">
        <param name="android-package" value="org.apache.cordova.device.Device" />
    </feature>
    <feature name="Whitelist">
        <param name="android-package" value="org.apache.cordova.whitelist.WhitelistPlugin" />
        <param name="onload" value="true" />
    </feature>
    <feature name="Canvas2ImagePlugin">
        <param name="android-package" value="com.rodrigograca.canvas2image.Canvas2ImagePlugin" />
    </feature>
    <feature name="StatusBar">
        <param name="android-package" value="org.apache.cordova.statusbar.StatusBar" />
        <param name="onload" value="true" />
    </feature>
    <feature name="BarcodeScanner">
        <param name="android-package" value="com.phonegap.plugins.barcodescanner.BarcodeScanner" />
    </feature>
    <feature name="SplashScreen">
        <param name="android-package" value="org.apache.cordova.splashscreen.SplashScreen" />
        <param name="onload" value="true" />
    </feature>
    <feature name="Wechat">
        <param name="android-package" value="xu.li.cordova.wechat.Wechat" />
    </feature>
    <feature name="scanplugin">
        <param name="android-package" value="com.olc.scan.scanplugin" />
        <param name="onload" value="true" />
    </feature>
    <feature name="broadcaster">
        <param name="android-package" value="org.bsc.cordova.CDVBroadcaster" />
    </feature>
    <feature name="cordova-plugin-mlkit-barcode-scanner">
        <param name="android-package" value="com.mobisys.cordova.plugins.mlkit.barcode.scanner.MLKitBarcodeScanner" />
    </feature>
    <feature name="InAppBrowser">
        <param name="android-package" value="org.apache.cordova.inappbrowser.InAppBrowser" />
    </feature>
    <feature name="CordovaMqTTPlugin">
        <param name="android-package" value="com.arcoirislabs.plugin.mqtt.CordovaMqTTPlugin" />
    </feature>
    <feature name="BluetoothSerial">
        <param name="android-package" value="com.megster.cordova.BluetoothSerial" />
    </feature>
    <feature name="BLE">
        <param name="android-package" value="com.megster.cordova.ble.central.BLECentralPlugin" />
    </feature>
    <name>营匠</name>
    <description>
        懂行的ERP
    </description>
    <author email="<EMAIL>" href="http://cordova.io">
        Apache Cordova Team
    </author>
    <content src="index.html" />
    <access origin="*" />
    <allow-intent href="http://*/*" />
    <allow-intent href="https://*/*" />
    <allow-intent href="tel:*" />
    <allow-intent href="sms:*" />
    <allow-intent href="androidamap://*/*" />
    <allow-intent href="iosamap://*/*" />
    <allow-intent href="bdapp://*/*" />
    <allow-intent href="baidumap://*/*" />
    <allow-intent href="mailto:*" />
    <allow-intent href="geo:*" />
    <chcp>
        <auto-download enabled="false" />
        <auto-install enabled="true" />
        <native-interface version="4" />
        <config-file url="https://www.yingjiang168.com/download/YingJiangApp/www/chcp.json" />
    </chcp>
    <allow-intent href="market:*" />
    <preference name="loglevel" value="DEBUG" />
    <preference name="loadUrlTimeoutValue" value="70000" />
    <preference name="webView" value="org.jeremyup.cordova.x5engine.X5WebViewEngine" />
    <preference name="WECHATAPPID" value="wx65acc367d9bcfbb3" />
    <preference name="orientation" value="portrait" />
    <preference name="StatusBarOverlaysWebView" value="false" />
    <preference name="StatusBarBackgroundColor" value="#ffffff" />
    <preference name="StatusBarStyle" value="darkcontent" />
    <preference name="SplashScreenDelay" value="2000" />
</widget>
