<template>
  <div class="class_boxs" id="class_boxs">
    <div ref="scrollTop"></div>
    <ul class="class_uls">

      <SelectItems_Classes_Row
        ref="treeRoot"
        @onClassSelected="onClassSelected"
        :classDatas="classDataAttr"
        :onloadClassData="classDataAttr"
        :sheetFather = "sheet"

      />
    </ul>
    <div :style="{height: heightLeft + 'px'}"></div>

  </div>
</template>
<script>
import { GetItemClass } from "../../api/api";
import SelectItems_Classes_Row from "./SelectItems_Classes_Row";
export default {
  data() {
    return {
      forceUpdateCount: 5,
      classDataAttr: [],
      hScrollInterval: 0,
      lastScrollTime: null,
      destroyTime:new Date(),
      heightLeft: 0
    };
  },
  inject:['queryCondition'],
  props: {
    sheetType: '',
    sheet:{
      type: Object
    },
    isOftenItemList: {
      type: Array
    },

  },
  watch:{
    'sheetType':{
      handler(obj){
        this.sheetTypeX = obj
      }
    },
    'sheet':{
      handler(val,oldVal){
       this.calcSelectNum(this.sheet.sheetRows, this.classDataAttr,this.isOftenItemList)
      },
      deep:true
    },
    'isOftenItemList': {
      handler(val,oldVal) {
        this.calcSelectNum(this.sheet.sheetRows, this.classDataAttr,this.isOftenItemList)
      },
      deep: true
    }
  },
  components: {
    SelectItems_Classes_Row: SelectItems_Classes_Row,
  },
  activated () {

  },
  mounted() {
    window.addEventListener(
      "scroll",
      () => {
        this.lastScrollTime = new Date();
        const destroyInterval=new Date().getTime()-this.destroyTime.getTime()
        if (this.hScrollInterval===0&&destroyInterval>300){
          this.hScrollInterval = setInterval(this.scrollTopListen, 100);
        }
      },
      true
    );

    this.handleHeightLeft();
    this.$store.commit('classId','')
    setTimeout(()=>{
      this.onloadClass()
    },100) // setTimeout不能删,否则queryCondition可能加载不出来

    //this.$refs.treeRoot.computeItemCount();
  },

  computed:{
    noStockAttrSplitShow() {
      return window.getSettingValue('noStockAttrSplitShow').toLowerCase() == 'true'
    },
  },
  methods: {
    scrollTopListen() {
      var now = new Date();
      const ms = now.getTime() - this.lastScrollTime.getTime();
      if (ms > 100) {
        clearInterval(this.hScrollInterval)
        this.classDataAttr.map((item) => {
          item = this.recurCheat(item.subNodes);
        });
        this.destroyTime=new Date()
        this.$forceUpdate();

        this.hScrollInterval=0
      }

    },
    handleHeightLeft() {
      this.$nextTick(() => {
          let el = document.getElementById('class_boxs')
          this.heightLeft =  el.parentNode.getBoundingClientRect().height * 0.4
      })
    },
    recurCheat(subNodes) {
      if (subNodes === undefined) {
        return [];
      }
      if (subNodes.length > 0) {
        subNodes.forEach((node) => {
          node.cheatVue = !node.cheatVue;
          if (node.subNodes) {
            this.recurCheat(node.subNodes);
          }
        });
      }
    },
    clearSelection() {
      this.$refs.treeRoot.clearSelection();
    },
    cancelSelect() {
      cancelSelectFunction(this.classDataAttr)
      function cancelSelectFunction(tree) {
          tree.forEach(node => {
          node.isSelected = false
          cancelSelectFunction(node.subNodes)
        })
      }

    },
    onloadClass() {
      
      console.log('on onloadClass, queryCondition is:', this.queryCondition)
      console.log('on onloadClass, queryCondition.branchID is:', this.queryCondition.branchID)
      let params = {
       brandIDs: this.$store.state.operInfo.brands_id,
       bGetClassStock:true,
       branchID: this.queryCondition?.branchID ?? ''
      };
      let that = this
      GetItemClass(params).then((res) => {
        if (res.result === "OK") {
          // let res = {
          //   data:[]
          // };
          // console.log(data.data[0])
          // data.data[0].subNodes.map(item=>{
          //   res.data.push(item)
          // })
          // console.log("res.data",JSON.parse(JSON.stringify(res.data)))
          let objs = {
            id:"00",
            name:"常用分类",
            subNodes:[]
          }
          function classObject(obj){
            obj.map(item=>{
              if (item.subNodes&&item.subNodes.length>0){
                item.iSopen = false;
                item.sumBerLens = 0;
                item.isSelected = false;
                classObject(item.subNodes);
              } else {
                item.isSelected = false;
                item.sumBerLens = 0;
              }
              item.selectNum = 0;
              item.cheatVue = false;
            });
          }
          classObject(res.data);
          // eslint-disable-next-line no-inner-declarations
          //if(this.sheetTypeX=='X'||this.sheetTypeX=='XD'||this.sheetTypeX=='T'||this.sheetTypeX=='TD') {
          if(this.sheetTypeX=='X'||this.sheetTypeX=='XD'||this.sheetTypeX=='T'||this.sheetTypeX=='TD' || this.sheetTypeX=='CG' || this.sheetTypeX=='CD'|| this.sheet.sheetType=='SS'|| this.sheet.sheetType=='BQ') {  
            if(this.sheetTypeX =='X'||this.sheetTypeX =='XD')
            {
                 let promotionObj = {
              company_Id: res.data[0].company_Id,
              id: -2,
              iSopen: false,
              isSelected: false,
              mother_Id: res.data[0].id,
              name: "促销",
              subNodes: [{
                company_Id: res.data[0].company_Id,
                id: -3,
                isSelected: false,
                mother_Id: -2,
                name: "促销组合",
                subNodes: [],
                sumBerLens: 0,
                selectNum: 0
              },{
                company_Id: res.data[0].company_Id,
                id: -4,
                isSelected: false,
                mother_Id: -2,
                name: "限时特价",
                subNodes: [],
                sumBerLens: 0,
                selectNum: 0
              },{
                company_Id: res.data[0].company_Id,
                id: -5,
                isSelected: false,
                mother_Id: -2,
                name: "满减满赠",
                subNodes: [],
                sumBerLens: 0,
                selectNum: 0
              },{
                company_Id: res.data[0].company_Id,
                id: -6,
                isSelected: false,
                mother_Id: -2,
                name: "兑奖换购",
                subNodes: [],
                sumBerLens: 0,
                selectNum: 0
              }],
              sumBerLens: 0,
              selectNum : 0
            }
            res.data[0].subNodes.unshift(promotionObj)
            }
           

              /* let allCls = {
              company_Id:  res.data[0].company_Id,
              id: 0,
              isSelected: false,
              mother_Id: res.data[0].id,
              name: "全部 ",
              subNodes: [],
              sumBerLens: 0,
              selectNum : 0
            }

            res.data[0].subNodes.unshift(allCls) */
            let oftenObj = {
              company_Id:  res.data[0].company_Id,
              id: -1,
              isSelected: true,
              mother_Id: res.data[0].id,
              name: "常用",
              subNodes: [],
              sumBerLens: 0,
              selectNum : 0
            }
            res.data[0].subNodes.unshift(oftenObj)

          }
          let allCls = {
              company_Id:  res.data[0].company_Id,
              id: 0,
              isSelected: false,
              mother_Id: res.data[0].id,
              name: "全部 ",
              subNodes: [],
              sumBerLens: 0,
              selectNum : 0
          }
          res.data[0].subNodes.unshift(allCls)
          
          res.data[0].isSelected = true
          res.data[0].iSopen = true
          this.classDataAttr = res.data
          console.log("classDataAttr",this.classDataAttr)
          this.calcSelectNum(that.sheet.sheetRows, this.classDataAttr)
          this.$emit("getItemClassFinish",true)
        } else {
          console.warn(res)
        }
      });

    },
    onClassSelected(classID) {
      this.$emit("onClassSelected", classID);
    },
    calcSelectNum(sheetRows,classDataAttr,isOftenItemList) { // SelectItems_Classes_Row也有这个函数，需要同步修改
      var obj = {};
      const that = this
      var result = sheetRows.reduce(function(item, next) {
        if(that.noStockAttrSplitShow && next.attr_qty && next.attr_qty !== '') {
          let attrQty = typeof next.attr_qty === 'string' ? JSON.parse(next.attr_qty) : next.attr_qty
          const optIDNumbers = Object.keys(attrQty[0]).filter(key => key.startsWith('optID_')).map(key => attrQty[0][key])
          obj[next.item_id+ '_' + optIDNumbers.join('_')] ? '' : obj[next.item_id+ '_' + optIDNumbers.join('_')] = true && item.push(next);
        } else {
          obj[next.item_id] ? '' : obj[next.item_id] = true && item.push(next);
        }
            return item;
        }, []);
      cleansubNodes(classDataAttr)
      showsubNodes(result,classDataAttr)
      calOftenClass(sheetRows,isOftenItemList,classDataAttr)
      calTotal(classDataAttr)
      function calTotal (tree) {
        tree.forEach(node => {
          if (node.subNodes && node.subNodes.length) {
            calTotal(node.subNodes)
            node.selectNum += node.subNodes.reduce((sum, item) => (sum += item.selectNum, sum), 0)
            }
        })
      }
      function showsubNodes(sheetRows,tree) {
        tree.forEach(node => {
          sheetRows.forEach(sheetRow => {
            if(Number(sheetRow.classId) === node.id && sheetRow.item_id !== true && sheetRow.item_id !== false) {
              node.selectNum += 1
              }
          })
          if (node.subNodes && node.subNodes.length) {
              showsubNodes(sheetRows,node.subNodes)
          }
        })
      }
        function calOftenClass(sheetRows,isOftenItemList,classDataAttr) {
          let tempArr = [].concat(sheetRows)
          let obj = {}
          tempArr = tempArr.reduce(function(item, next) {
            if(that.noStockAttrSplitShow && next.attr_qty && next.attr_qty !== '') {
              let attrQty = typeof next.attr_qty === 'string' ? JSON.parse(next.attr_qty) : next.attr_qty
              const optIDNumbers = Object.keys(attrQty[0]).filter(key => key.startsWith('optID_')).map(key => attrQty[0][key])
              obj[next.item_id+ '_' + optIDNumbers.join('_')] ? '' : obj[next.item_id+ '_' + optIDNumbers.join('_')] = true && item.push(next);
            } else {
              obj[next.item_id] ? '' : obj[next.item_id] = true && item.push(next);
            }
            return item;
          }, []);
         if(classDataAttr.length > 0 && isOftenItemList) {
            classDataAttr[0].subNodes[0].selectNum = 0
            tempArr.forEach(tempArrItem => {
              isOftenItemList.forEach(oftenItem => {
                if(that.noStockAttrSplitShow && tempArrItem.attr_qty && tempArrItem.attr_qty !== '' && oftenItem.avail_attr_combine_item) {
                  let attrQty = typeof tempArrItem.attr_qty === 'string' ? JSON.parse(tempArrItem.attr_qty) : tempArrItem.attr_qty
                  const optIDNumbers = Object.keys(attrQty[0]).filter(key => key.startsWith('optID_')).map(key => attrQty[0][key])
                  const setA = new Set(optIDNumbers.map(item => item.toString()));
                  const setB = new Set(oftenItem.avail_attr_combine_item.combine.map(item => item.toString()));
                  if (tempArrItem.item_id === oftenItem.item_id && optIDNumbers.length === oftenItem.avail_attr_combine_item.combine.length && Array.from(setA).every((val) => setB.has(val))) {
                    classDataAttr[0].subNodes[0].selectNum += 1
                  }
                } else {
                  if(tempArrItem.item_id === oftenItem.item_id) {
                    classDataAttr[0].subNodes[0].selectNum += 1
                  }
                }


              })
            })
          }
        }
      function cleansubNodes(tree) {
        tree.forEach(node => {
          node.selectNum = 0
          if (node.subNodes && node.subNodes.length) {
              cleansubNodes(node.subNodes)
          }
        })
      }
    },
  },
};
</script>
<style lang="less" >
.class_boxs {
  height: auto;
  overflow: hidden;
}
.chommodity_li {
  h4 {
    padding-left: 12px;
    position: relative;
  }
  ul {
    .chommodity_li {
      h4 {
        padding-left: 18px;
        position: relative;
        i {
          left: 5px;
        }
      }
      ul {
        .chommodity_li {
          h4 {
            padding-left: 24px;
            position: relative;
            i {
              left: 10px;
            }
          }
          ul {
            .chommodity_li {
              h4 {
                padding-left: 40px;
                position: relative;
                i {
                  left: 15px;
                }
              }
              ul {
                .chommodity_li {
                  h4 {
                    padding-left: 50px;
                    position: relative;
                    i {
                      left: 20px;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
