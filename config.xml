<?xml version='1.0' encoding='utf-8'?>
<widget id="com.yingjiang.app" version="3.34" xmlns="http://www.w3.org/ns/widgets" xmlns:cdv="http://cordova.apache.org/ns/1.0">
    <name>营匠</name>
    <description>
        懂行的ERP
    </description>
    <author email="<EMAIL>" href="http://cordova.io">
        Apache Cordova Team
    </author>
    <content src="index.html" />
    <access origin="*" />
    <!--解决 The connection to the server wasunsuccessful.(file:///android_asset/www/index.html 问题-->
    <preference name="loadUrlTimeoutValue" value="70000" />
    <preference name="orientation" value="portrait" />
    <preference name="StatusBarOverlaysWebView" value="false" />
    <preference name="StatusBarBackgroundColor" value="#ffffff" />
    <preference name="StatusBarStyle" value="darkcontent" />
   <!-- <preference name="AndroidWebViewTarget" value="latest" />-->
    <allow-intent href="http://*/*" />
    <allow-intent href="https://*/*" />
    <allow-intent href="tel:*" />
    <allow-intent href="sms:*" />
    <allow-intent href="androidamap://*/*" />
    <allow-intent href="iosamap://*/*" />
    <allow-intent href="bdapp://*/*" />
    <allow-intent href="baidumap://*/*" />
    <allow-intent href="mailto:*" />
    <allow-intent href="geo:*" />
    <!--解决在极少数机器上打开APP时直接报错: the connection to the server was unsuccessful 问题-->
    <!-- <preference name="loadUrlTimeoutValue" value="2000" /> -->
    <platform name="android">
        <preference name="SplashScreenDelay" value="2000" /> 
        <allow-intent href="market:*" />
        <plugin name="cordova-plugin-bd-geolocation" spec="^8.5.0">
            <variable name="API_KEY" value="uinN8rdNWfpZEtEFudy6GPOgd5UdZoZs" />
        </plugin>

    </platform>
    <platform name="ios">
        <plugin name="cordova-plugin-geolocation" spec="^4.0.1" />
    </platform>
    <platform name="ios">
        <allow-intent href="itms:*" />
        <allow-intent href="itms-apps:*" />
        <allow-intent href="iosamap://*/*" />
    </platform>
    <chcp>
        <auto-download enabled="false" />
        <auto-install enabled="true" />
        <native-interface version="4" />
        <config-file url="https://www.yingjiang168.com/download/YingJiangApp/www/chcp.json" />
    </chcp>
</widget>
