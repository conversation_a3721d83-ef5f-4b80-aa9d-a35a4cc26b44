import Vue from "vue"
import VueRouter from "vue-router"
import Home from "../views/Home.vue"
// 登录
import Login from "../views/Login/Login"
import Register from "../views/Login/Register"
// 拜访门店
import Visit from "../views/Visit/Visit"
import VisitUser from "../views/Visit/VisitUser"
import SupcustRadar from "../views/SupcustRadar/SupcustRadar"
import VisitDay from "../views/VisitDay/VisitDay"
import VisitPlan from "../views/VisitPlan/VisitPlan"
// 拜访整理
import VisitAbout from "../views/VisitAbout/VisitAbout"

// 轨迹
import journey from "../views/Visit/journey"
//考勤签到
import Attendance from "../views/Attendance/Attendance"
import AttenceLeave from "../views/AttenceLeave/AttenceLeave"
// 拜访记录
import VisitRecord from "../views/VisitRecord/VisitRecord"
import VisitRecordDetail from "../views/VisitRecord/VisitRecordDetail"
import VisitRecordSummary from "../views/VisitRecord/VisitRecordSummary"
import VisitRecordAnalysis from "../views/VisitRecord/VisitRecordAnalysis"
import VisitSummaryBySeller from "../views/VisitRecord/VisitSummaryBySeller"
import SaleSumByClientItem from "../views/SaleSumByClientItem/SaleSumByClientItem"

// 工作台
import Workbench from "../views/Workbench"
import WorkbenchEdit from "../views/WorkbenchEdit"
// 全部分类
import Whole from "../views/Whole/Whole"
import store from "../store/store"

// 我的
import My from "../views/My"
import Printer from "../views/PrinterManage/Printer"
import PrintersView from "../views/PrinterManage/PrintersView"
import UpdatePassword from "../views/UpdatePassword/UpdatePassword"
import AboutUs from "../views/AboutUs/AboutUs"
import Service from "../views/AboutUs/Service"
import SwitchAccount from "../views/SwitchAccount/SwitchAccount"
import FontSizeSetting from "../views/FontSizeSetting/FontSizeSetting"
import AICustomer from "../views/AICustomer/AICustomer.vue"
// 销售单
import SaleSheet from "../views/SaleSheet/SaleSheet"
import LabelPrintSheet from "../views/SaleSheet/LabelPrintSheet.vue"
// 借还单
import BorrowItemSheet from "../views/BorrowItemSheet/BorrowItemSheet"
import BorrowItemSelectItems from "../views/BorrowItemSheet/BorrowItemSelectItems"
import BorrowItemSelectItemsHistory from "../views/BorrowItemSheet/BorrowItemSelectItemsHistory"
// D:\ying-jiang-app\app\src\views\BorrowItemSheet\BorrowItemSheet.vue
// 应收款
import Receivables from "../views/Receivables/Receivables"
// 预收款余额
import PrepayBalance from "../views/PrepayBalance/PrepayBalance"
// 客户流失预警
import ClientLossWarning from "../views/ClientLossWarning/ClientLossWarning"
// 客户往来账
import AccountHistory from "../views/AccountHistory/AccountHistory"
// 银行支付对账表
import PayBillCheckTrades from "../views/PayBill/PayBillCheckTrades"
//铺市率
import BrandsOccupyReport from "../views/OccupyReport/BrandsOccupyReport"
import ItemsOccupyReport from "../views/OccupyReport/ItemsOccupyReport"

// 定货会余额
import ItemOrderedSumByItem from "../views/ItemOrderedSumByItem/ItemOrderedSumByItem"
import SaleDetails from "../views/SaleDetails/SaleDetails"
//其他收入
import OtherInCome from "../views/OtherInCome/OtherInCome"
// 调拨汇总表
import MoveSummary from "../views/MoveSummary/MoveSummary"
// 客户排行
import CustomerRanking from "../views/ReportForm/CustomerRanking"
// 库存查询
import Stock from "../views/Stock/Stock"
// 库存详情
import StockIndex from "../views/Stock/StockDetail/StockIndex"
// 分类
import SelectItems from "../views/SaleSheet/SelectItems"
// 商品历史记录
import SelectItemsHistory from "../views/SaleSheet/SelectItemsHistory"
// 收款对账
import CheckAccount from "../views/CheckAccount/CheckAccount"
import ShowAccount from "../views/CheckAccount/ShowAccount"
import AccountSummary from "../views/CheckAccount/AccountSummary"
import AccountDetail from "../views/CheckAccount/AccountDetail"
import AccountItem from "../views/CheckAccount/AccountItem"
import CheckAccountHistory from "../views/CheckAccount/CheckAccountHistory"
// 预收款单
import PrepaySheet from "../views/PrepaySheet/PrepaySheet"

// 费用支出
import FeeOut from "../views/FeeOut/FeeOut"
import AddFeeOut from "../views/FeeOut/AddFeeOut"
// 收款单
import GetArrearsSheet from "../views/GetArrearsSheet/GetArrearsSheet"
// 查看单据
import ViewSheets from "../views/ViewSheets/ViewSheets"
// 客户档案
import CustomerArchives from "../views/CustomerArchives/CustomerArchives"
import CustomerArchivesSon from "../views/CustomerArchives/CustomerArchivesSon"
// 商品档案
import GoodsArchives from "../views/ItemArchives/GoodsArchives"
import GoodsArchivesSon from "../views/ItemArchives/GoodsArchivesSon"
// 供应商档案
import SupplierArchives from "../views/SupplierArchives/SupplierArchives"
import SupplierArchivesSon from "../views/SupplierArchives/SupplierArchivesSon"
//新客分析
import CreateSupcustAnalysis from "../views/CreateSupcustAnalysis/CreateSupcustAnalysis"
//经营利润表
import BusinessProfit from '../views/BusinessProfit/BusinessProfit'
//合计提成
import Commission from '../views/Commission/Commission'
//提成明细
import CommissionDetails from '../views/Commission/CommissionDetails'
//商品品项
import CashInOut from '../views/CashInOut/CashInOut'
import Infos from '../views/Commission/Infos'
//抢单
import GrabOrderSheets from "../views/GrabOrderSheets/GrabOrderSheets"
//送货签收
import DeliveryReceipt from "../views/DeliveryReceipt/DeliveryReceipt"
import AllOrderSheets from "../views/OrderManage/AllOrderSheets/AllOrderSheets"
import ApproveOrderSheets from "../views/OrderManage/ApproveOrderSheets/ApproveOrderSheets"
import ReviewOrderSheets from "../views/OrderManage/ReviewOrderSheets/ReviewOrderSheets"
import PrintOrderSheets from "../views/OrderManage/PrintOrderSheets/PrintOrderSheets"
import AssignVanOrderSheets from "../views/OrderManage/AssignVanOrderSheets/AssignVanOrderSheets"
import ApproveAssignVan from "../views/OrderManage/ApproveAssignVan/ApproveAssignVan"
import ApproveBackBranch from "../views/OrderManage/ApproveBackBranch/ApproveBackBranch"

import BackBranchOrderSheets from "../views/OrderManage/BackBranchOrderSheets/BackBranchOrderSheets"
import ShowAssignVanInfo from "../views/OrderManage/AssignVanInfo/ShowAssignVanInfo"
import ShowBackBranchInfo from "../views/OrderManage/BackBranchInfo/ShowBackBranchInfo"
import BackBranchSheet from "../views/OrderManage/BackBranch/BackBranchSheet"
// 调拨单
import MoveSheet from "../views/MoveSheet/MoveSheet"
// 新增调拨单
import SelectMoveItems from "../views/MoveSheet/SelectMoveItems"
// 盘点单
import InventoryList from "../views/InventoryList/InventoryList"
// 盘点单主页面
import InventoryOrder from "../views/InventoryList/InventoryOrder"
// 盘点单分类页面
import ClassIficationinventory from "../views/components/ClassIficationinventory"
import AddSheetRow from "../views/SaleSheet/AddSheetRow"
import SupcustDistribution from "../views/SupcustDistribution/SupcustDistribution"
//特价审批单
import SpecialPriceSheet from "../views/SpecialItemPrice/SpecialPriceSheet"
import SpecialPriceSelectItems from "../views/SpecialItemPrice/SpecialPriceSelectItems"
// 盘点单
import InventorySheet from "../views/Inventory/InventorySheet"
import InventoryChangeSheet from "../views/InventoryChange/InventoryChangeSheet"
// 门店库存上报单
import StoreStockSheet from "../views/StoreStock/StoreStockSheet"
import StoreStockSelectItems from "../views/StoreStock/StoreStockSelectItems"
import $ from "jquery"
// 盘点单选择商品
import InventorySelectItems from "../views/Inventory/InventorySelectItems"
import InventoryChangeSelectItems from "../views/InventoryChange/InventoryChangeSelectItems"
//import { component } from 'vue/types/umd'
import VisitStandard from "../views/VisitStandard/VistiStandard"
import VisitSchedule from "../views/VisitSchedule/VisitSchedule";
import PromotionView from "../views/Promotion/PromotionView"
import RedPacketView from "../views/RedPacket/RedPacketView"

import SupcustSummary from "../views/SupcustSummary/SupcustSummary"
import DisplayAgreementSheet from "../views/DisplayAgreementSheet/DisplayAgreementSheet"
import DisplaySelectItem from "../views/DisplayAgreementSheet/DisplaySelectItem"
// 消息订阅
import MsgSubscribeCompany from "../views/Message/MsgSubscribeCompany"
import Message from "../views/Message/Message"
// 消息相关页面
import MessageTodoList from "../views/Message/MessageList/MessageTodoList"
import MessageProofPolishList from "../views/Message/MessageList/MessageProofPolishList"
import MessageNoticeList from "../views/Message/MessageList/MessageNoticeList"
// 查看单据
import SaleSheetsView from "../views/ViewSheet/ViewSaleSheets/SaleSheetsView.vue"
import ViewSheetAll from "../views/ViewSheet/ViewSheetAll.vue"
// 供应商往来账
import AccountSupplierHistory from "../views/AccountHistory/AccountSupplierHistory/AccountSupplierHistory.vue"
// 付款单
import GetAndPayArrearsSheet from "../views/GetAndPayArrearsSheet/GetAndPayArrearsSheet.vue"
import DisplayAgreementItem from "@/views/Visit/DisplayAgreementItem.vue";
//商品名称查询

//商城用户
import MallUser from "../views/MallUser/MallUserView.vue"

const routerPush = VueRouter.prototype.push

VueRouter.prototype.push = function push(location) {
  window.g_bPushing = true
  if (!window.g_maxPageID) window.g_maxPageID = 0
  window.g_maxPageID++
  window.g_prePage = window.g_curPage

  var path = location
  if (location.path) path = location.path
  var name = ""
  if (location.name) name = location.name
  else {
    name = path
    if (name.indexOf("/") == 0) name = name.substr(1, name.length - 1)
    var n = name.indexOf("?")
    if (n > 0) name = name.substr(0, n)
    var l = name.substr(name.length - 1, 1)
    if (l == "/") name = name.substr(0, name.length - 1)
  }

  var query = null
  if (location.query) query = location.query
  var myParams = null
  if (location.myParams) myParams = location.myParams
  window.g_curPage = {
    path: path,
    query: query,
    name: name,
    id: window.g_maxPageID,
    myParams: myParams,
  }
  if (location.myParams) delete location.myParams
  //return VueRouter.prototype.replace.call(this, location).catch(error=> error)
  return routerPush.call(this, location).catch((error) =>{
    console.log(`Navigation error: ${error.message}`);
    console.log(error);
    return error;
  })
}

Vue.use(VueRouter)
const routes = [
    {
        path: '/',
        name: 'Home',
        component: Home,
        meta: { index: 0, keepAlive: true },
        redirect: '/Workbench',
        children: [
            {
                path: '/Workbench',
                name: 'Workbench',
                meta: { index: 1, keepAlive: true },//requiresAuth: true,
                component: Workbench
            },
            {
                path: '/Whole',
                name: 'Whole',
                meta: { index: 1, keepAlive: true },
                component: Whole
            },

            {
                path: '/My',
                name: 'My',
                meta: { index: 1, keepAlive: true },
                component: My
            },
            {
                path: '/Message',
                name: 'Message',
                meta: { index: 1, keepAlive: true },//requiresAuth: true,
                component: Message
            },
        ]
    },
    // 自定义桌面
    {
        path: '/WorkbenchEdit',
        name: 'WorkbenchEdit',
        meta: { index: 2, keepAlive: true },//requiresAuth: true,
        component: WorkbenchEdit
    },

    {
        // 登录
        path: '/Login',
        name: 'Login',
        meta: { index: 2, keepAlive: false },
        component: Login
    },
    {
        // 注册
        path: '/Register',
        name: 'Register',
        meta: { index: 3, keepAlive: false },
        component: Register
    },

    {
        // 行驶轨迹
        path: '/journey',
        name: 'journey',
        meta: { index: 2, keepAlive: true },
        component: journey
    },
    {
        // 拜访记录
        path: '/VisitRecord',
        name: 'VisitRecord',
        meta: { index: 2, keepAlive: true },
        component: VisitRecord
    },
    {
        path: '/VisitRecordDetail',
        name: 'VisitRecordDetail',
        meta: { index: 2, keepAlive: true },
        component: VisitRecordDetail
    },
    {
        path: '/VisitRecordSummary',
        name: 'VisitRecordSummary',
        meta: { index: 2, keepAlive: true },
        component: VisitRecordSummary
    },
    {
        path: '/SaleSumByClientItem',
        name: 'SaleSumByClientItem',
        meta: { index: 2, keepAlive: true },
        component: SaleSumByClientItem
    },
    {
        path: '/CreateSupcustAnalysis',
        name: 'CreateSupcustAnalysis',
        meta: { index: 2, keepAlive: true },
        component: CreateSupcustAnalysis
    },
    {
        path:'/BusinessProfit',
        name:'BusinessProfit',
        meta: { index: 2, keepAlive: true },
        component: BusinessProfit
       
    },
    {
      path:'/CashInOut',
      name:'CashInOut',
      meta: { index: 2, keepAlive: true },
      component: CashInOut
     
  },
    {
        path:'/Commission',
        name:'Commission',
        meta: { index: 2, keepAlive: true },
        component: Commission
       
    },
    {
        path:'/CommissionDetails',
        name:'CommissionDetails',
        meta: { index: 3, keepAlive: true },
        component: CommissionDetails
       
    },
    {
        path:'/Infos',
        name:'Infos',
        meta: { index: 4, keepAlive: true },
        component: Infos
       
    },
    {
        path: '/SaleDetails',
        name: 'SaleDetails',
        meta: { index: 5, keepAlive:true },
        component: SaleDetails
    },
    {
        path: '/ItemOrderedSumByItem',
        name: 'ItemOrderedSumByItem',
        meta: { index: 2, keepAlive: true },
        component: ItemOrderedSumByItem
    },
    {
        path: '/VisitRecordAnalysis',
        name: 'VisitRecordAnalysis',
        meta: { index: 2, keepAlive: true },
        component: VisitRecordAnalysis
    },
    {
        path: '/VisitSummaryBySeller',
        name: 'VisitSummaryBySeller',
        meta: { index: 2, keepAlive: true },
        component: VisitSummaryBySeller
    },
    {
        path: '/SupcustSummary',
        name: 'SupcustSummary',
        meta: { index: 2, keepAlive: true },
        component: SupcustSummary
    },
    {
        // 客户应收款
        path: '/Receivables',
        name: 'Receivables',
        meta: { index: 2, keepAlive: true },
        component: Receivables
    },
    {
        // 预收款余额报表
        path: '/PrepayBalance',
        name: 'PrepayBalance',
        meta: { index: 2, keepAlive: true },
        component: PrepayBalance
    },
    {
        // 客户流失预警·
        path: '/ClientLossWarning',
        name: 'ClientLossWarning',
        meta: { index: 2, keepAlive: true },
        component: ClientLossWarning
    },
    {
        // 客户往来账
        path: '/AccountHistory',
        name: 'AccountHistory',
        meta: { index: 2, keepAlive: true },
        component: AccountHistory
    },
    {
        // 其他收入·
        path: '/OtherInCome',
        name: 'OtherInCome',
        meta: { index: 2, keepAlive: true },
        component: OtherInCome
    },

    {
        // 调拨汇总表
        path: '/MoveSummary',
        name: 'MoveSummary',
        meta: { index: 2, keepAlive: true },
        component: MoveSummary
    },
    {
        // 销售单
        path: '/SaleSheet',
        name: 'SaleSheet',
        component: SaleSheet,
        meta: {
            index: 6, keepAlive: true,
            key: route => `${route.query.sheetID || 'new'}`
        }
    },
    {
      // 标签打印单
      path: "/LabelPrintSheet",
      name: "LabelPrintSheet",
      component: LabelPrintSheet, 
      meta: {
        index: 6,
        keepAlive: true,
        key: (route) => `${route.query.sheetID || "new"}`,
      },
    },
    {
        // 分类  注意，这里的index不可以随便动，后打开的页面的index必须大于前一个页面的index,否则，push到后一个页面时，前一个页面的缓存就被清理掉了
        path: '/SelectItems/:data',
        name: 'SelectItems',
        component: SelectItems,
        meta: { index: 6, keepAlive: true }
    },
    {
        path: '/SelectItemsHistory',
        name: 'SelectItemsHistory',
        component: SelectItemsHistory,
        meta: { index: 7, keepAlive: true }

    },
    {
      path: '/BorrowItemSheet',
      name: 'BorrowItemSheet',
      component: BorrowItemSheet,
      meta: {
          index: 6, keepAlive: true,
          key: route => `${route.query.sheetID || 'new'}`
      }
    },
    {
      // 分类  注意，这里的index不可以随便动，后打开的页面的index必须大于前一个页面的index,否则，push到后一个页面时，前一个页面的缓存就被清理掉了
      path: '/BorrowItemSelectItems/:data',
      name: 'BorrowItemSelectItems',
      component: BorrowItemSelectItems,
      meta: { index: 6, keepAlive: true }
  },
  {
      path: '/BorrowItemSelectItemsHistory',
      name: 'BorrowItemSelectItemsHistory',
      component: BorrowItemSelectItemsHistory,
      meta: { index: 7, keepAlive: true }

  },
    {
        // 报表
        path: '/CustomerRanking',
        name: 'CustomerRanking',
        meta: { index: 3, keepAlive: true },
        component: CustomerRanking
    },
    {
        // 铺市率
        path: '/ItemsOccupyReport',
        name: 'ItemsOccupyReport',
        meta: { index: 3, keepAlive: true },
        component: ItemsOccupyReport
    },
    {
        // 铺市率
        path: '/BrandsOccupyReport',
        name: 'BrandsOccupyReport',
        meta: { index: 3, keepAlive: true },
        component: BrandsOccupyReport
    },
    {
        // 库存查询
        path: '/Stock',
        name: 'Stock',
        meta: { index: 3, keepAlive: true },
        component: Stock,
    },

    {
        // 收款对账
        path: '/CheckAccount',
        name: 'CheckAccount',
        meta: { index: 3, keepAlive: true },
        component: CheckAccount
    },
    {
        // 历史交账单
        path: '/CheckAccountHistory',
        name: 'CheckAccountHistory',
        meta: { index: 3, keepAlive: true },
        component: CheckAccountHistory
    },
    // {
    //     // 库存详情
    //     path: '/StockIndex/:itemId/:branchId/:branchName',
    //     name:'StockIndex',
    //     meta: { index: 3, keepAlive: true },
    //     component: StockIndex,
    // },
    {
        // 库存详情
        path: '/StockIndex',
        name:'StockIndex',
        meta: { index: 3, keepAlive: true },
        component: StockIndex,
    },
    {
        // 展示账户
        path: '/ShowAccount',
        name: 'ShowAccount',
        meta: { index: 4, keepAlive: true },
        component: ShowAccount
    },
    {
        path: '/AccountSummary',
        name: 'AccountSummary',
        meta: { index: 4 },
        component: AccountSummary
    },
    {
        path: '/AccountDetail',
        name: 'AccountDetail',
        meta: { index: 4 },
        component: AccountDetail
    },
    {
        path: '/AccountItem',
        name: 'AccountItem',
        meta: { index: 4 },
        component: AccountItem
    },
    {
        // 预收款单
        path: '/PrepaySheet',
        name: 'PrepaySheet',
        meta: { index: 5, keepAlive: true },
        component: PrepaySheet
    },

    {
        // 费用支出
        path: '/FeeOut',
        name: 'FeeOut',
        meta: { index: 5, keepAlive: true },
        component: FeeOut
    },
    {
        path: '/AddFeeOut',
        name: 'AddFeeOut',
        meta: { index: 6, keepAlive: true },
        component: AddFeeOut
    },
    {
        path: '/GetArrearsSheet',
        name: 'GetArrearsSheet',
        meta: { index: 5, keepAlive: true },
        component: GetArrearsSheet
    },
    {
        path: '/DeliveryReceipt',
        name: 'DeliveryReceipt',
        meta: { index: 2, keepAlive: true },
        component: DeliveryReceipt
    },
    {
      path: '/GrabOrderSheets',
      name: 'GrabOrderSheets',
      meta: { index: 2, keepAlive: true },
      component: GrabOrderSheets
  },
    {
        path: '/AllOrderSheets',
        name: 'AllOrderSheets',
        meta: { index: 2, keepAlive: true },
        component: AllOrderSheets
    },
    {
        path: '/ReviewOrderSheets',
        name: 'ReviewOrderSheets',
        meta: { index: 2, keepAlive: true },
        component: ReviewOrderSheets
    },
    {
        path: '/ApproveOrderSheets',
        name: 'ApproveOrderSheets',
        meta: { index: 2, keepAlive: true },
        component: ApproveOrderSheets
    },


    {
        path: '/PrintOrderSheets',
        name: 'PrintOrderSheets',
        meta: { index: 2, keepAlive: true },
        component: PrintOrderSheets
    },
    {
        path: '/AssignVanOrderSheets',
        name: 'AssignVanOrderSheets',
        meta: { index: 2, keepAlive: true },
        component: AssignVanOrderSheets
    },
    {
        path: '/ApproveAssignVan',
        name: 'ApproveAssignVan',
        meta: { index: 2, keepAlive: true },
        component: ApproveAssignVan
    },
    {
        path: '/ApproveBackBranch',
        name: 'ApproveBackBranch',
        meta: { index: 2, keepAlive: true },
        component: ApproveBackBranch
    },
    {
        path: '/BackBranchOrderSheets',
        name: 'BackBranchOrderSheets',
        meta: { index: 2, keepAlive: true },
        component: BackBranchOrderSheets
    },
    {
        path: '/ShowAssignVanInfo',
        name: 'ShowAssignVanInfo',
        meta: { index: 2, keepAlive: true },
        component: ShowAssignVanInfo
    },
    {
        path: '/ShowBackBranchInfo',
        name: 'ShowBackBranchInfo',
        meta: { index: 2, keepAlive: true },
        component: ShowBackBranchInfo
    },
    {
      path: '/BackBranchSheet',
      name: 'BackBranchSheet',
      meta: { index: 2, keepAlive: true },
      component: BackBranchSheet
    },
    {
        path: '/ViewSheets',
        name: 'ViewSheets',
        meta: { index: 2, keepAlive: true },
        component: ViewSheets
    },


    {
        path: '/CustomerArchives',
        name: 'CustomerArchives',
        meta: { index: 3, keepAlive: true },
        component: CustomerArchives
    },
    {
        path: '/CustomerArchivesSon',
        name: 'CustomerArchivesSon',
        meta: { index: 6 },
        component: CustomerArchivesSon
    },



    {
        path: '/SupplierArchives',
        name: 'SupplierArchives',
        meta: { index: 3, keepAlive: true },
        component: SupplierArchives
    },
    {
        path: '/SupplierArchivesSon',
        name: 'SupplierArchivesSon',
        meta: { index: 6 },
        component: SupplierArchivesSon
    },
    {
      //商城用户
      path: '/MallUser',
      name: 'MallUser',
      meta: { index: 2, keepAlive: true },
      component: MallUser
    },
    {
        path: '/GoodsArchives',
        name: 'GoodsArchives',
        meta: { index: 3, keepAlive: true },
        component: GoodsArchives
    },
    {
        path: '/GoodsArchivesSon',
        name: 'GoodsArchivesSon',
        meta: { index: 4 },
        component: GoodsArchivesSon
    },
    {
        path: '/MoveSheet',
        name: 'MoveSheet',
        component: MoveSheet,
        meta: { index: 5, keepAlive: true }
    },
    {
        path: '/SelectMoveItems',
        name: 'SelectMoveItems',
        meta: { index: 6 },
        component: SelectMoveItems
    },
    {
        path: '/SpecialPriceSheet',
        name: 'SpecialPriceSheet',
        meta: { index: 3, keepAlive: true },
        component: SpecialPriceSheet
    },
    {
        path: '/SpecialPriceSelectItems',
        name: 'SpecialPriceSelectItems',
        meta: { index: 3, keepAlive: true },
        component: SpecialPriceSelectItems
    },
    {
        path: '/InventorySheet',
        name: 'InventorySheet',
        meta: { index: 3, keepAlive: true },
        component: InventorySheet
    },
    {
      path: '/StoreStockSheet',
      name: 'StoreStockSheet',
      meta: { index: 3, keepAlive: true },
      component: StoreStockSheet
  },
    {
        path: '/InventoryChangeSheet',
        name: 'InventoryChangeSheet',
        meta: { index: 3, keepAlive: true },
        component: InventoryChangeSheet
    },
    {
        path: '/InventorySelectItems',
        name: 'InventorySelectItems',
        meta: { index: 4 },
        component: InventorySelectItems
    },
    {
      path: '/StoreStockSelectItems',
      name: 'StoreStockSelectItems',
      meta: { index: 4 },
      component: StoreStockSelectItems
  },
    {
        path: '/InventoryChangeSelectItems',
        name: 'InventoryChangeSelectItems',
        meta: { index: 4 },
        component: InventoryChangeSelectItems
    },
    {
        path: '/InventoryOrder',
        name: 'InventoryOrder',
        component: InventoryOrder,
        meta: { index: 3, keepAlive: true }
    },
    {
      path: '/PrintersView',
      name: 'PrintersView',
      meta: { index: 3 },
      component: PrintersView
    },
    {
      path: '/Printer/:data',
      name: 'Printer',
      meta: { index: 3 },
      component: Printer
    },
    {
        path: '/UpdatePassword',
        name: 'UpdatePassword',
        meta: { index: 3 },
        component: UpdatePassword
    },
    {
        path: '/AboutUs',
        name: 'AboutUs',
        meta: { index: 3 },
        component: AboutUs
    },
    {
      path: '/AICustomer',
      name: 'AICustomer',
      meta: { index: 3 },
      component: AICustomer
  },
    {
        path: '/Service',
        name: 'Service',
        meta: { index: 3 },
        component: Service
    },
    {
        path: '/SwitchAccount',
        name: 'SwitchAccount',
        meta: { index: 3 },
        component: SwitchAccount
    },
    {
        path: '/ClassIficationinventory',
        name: 'ClassIficationinventory',
        meta: { index: 3 },
        component: ClassIficationinventory
    },
    {
        path: '/AddSheetRow',
        name: 'AddSheetRow',
        meta: { index: 4 },
        component: AddSheetRow
    },
    {
        path: '/Visit',
        name: 'Visit',
        meta: { index: 4, keepAlive: true },
        component: Visit
    },
    {
      path: '/VisitAbout',
      name: 'VisitAbout',
      meta: { index: 4, keepAlive: true },
      component: VisitAbout
    },
    {
        path: '/SupcustRadar',
        name: 'SupcustRadar',
        meta: { index: 4, keepAlive: true },
        component: SupcustRadar
    },
    {
        path: '/VisitUser',
        name: 'VisitUser',
        meta: { index: 4, keepAlive: true },
        component: VisitUser
    },
    {
        path: '/VisitDay',
        name: 'VisitDay',
        meta: { index: 4, keepAlive: true },
        component: VisitDay
    },
    {
      path: '/VisitPlan',
      name: 'VisitPlan',
      meta: { index: 4, keepAlive: true },
      component: VisitPlan
    },
  {
    path: "/",
    name: "Home",
    component: Home,
    meta: { index: 0, keepAlive: true },
    redirect: "/Workbench",
    children: [
      {
        path: "/Workbench",
        name: "Workbench",
        meta: { index: 1, keepAlive: true }, //requiresAuth: true,
        component: Workbench,
      },
      {
        path: "/Whole",
        name: "Whole",
        meta: { index: 1, keepAlive: true },
        component: Whole,
      },

      {
        path: "/My",
        name: "My",
        meta: { index: 1, keepAlive: true },
        component: My,
      },
      {
        path: "/Message",
        name: "Message",
        meta: { index: 1, keepAlive: true }, //requiresAuth: true,
        component: Message,
      },
    ],
  },
  // 自定义桌面
  {
    path: "/WorkbenchEdit",
    name: "WorkbenchEdit",
    meta: { index: 2, keepAlive: true }, //requiresAuth: true,
    component: WorkbenchEdit,
  },

  {
    // 登录
    path: "/Login",
    name: "Login",
    meta: { index: 2, keepAlive: false },
    component: Login,
  },
  {
    // 注册
    path: "/Register",
    name: "Register",
    meta: { index: 3, keepAlive: false },
    component: Register,
  },

  {
    // 行驶轨迹
    path: "/journey",
    name: "journey",
    meta: { index: 2, keepAlive: true },
    component: journey,
  },
  {
    // 拜访记录
    path: "/VisitRecord",
    name: "VisitRecord",
    meta: { index: 2, keepAlive: true },
    component: VisitRecord,
  },
  {
    path: "/VisitRecordDetail",
    name: "VisitRecordDetail",
    meta: { index: 2, keepAlive: true },
    component: VisitRecordDetail,
  },
  {
    path: "/VisitRecordSummary",
    name: "VisitRecordSummary",
    meta: { index: 2, keepAlive: true },
    component: VisitRecordSummary,
  },
  {
    path: "/SaleSumByClientItem",
    name: "SaleSumByClientItem",
    meta: { index: 2, keepAlive: true },
    component: SaleSumByClientItem,
  },
  {
    path: "/CreateSupcustAnalysis",
    name: "CreateSupcustAnalysis",
    meta: { index: 2, keepAlive: true },
    component: CreateSupcustAnalysis,
  },
  {
    path: "/BusinessProfit",
    name: "BusinessProfit",
    meta: { index: 2, keepAlive: true },
    component: BusinessProfit,
  },
  {
    path:'/CashInOut',
    name:'CashInOut',
    meta: { index: 2, keepAlive: true },
    component: CashInOut
},
  {
    path: "/SaleDetails",
    name: "SaleDetails",
    meta: { index: 2, keepAlive: true },
    component: SaleDetails,
  },
  {
    path: "/ItemOrderedSumByItem",
    name: "ItemOrderedSumByItem",
    meta: { index: 2, keepAlive: true },
    component: ItemOrderedSumByItem,
  },
  {
    path: "/VisitRecordAnalysis",
    name: "VisitRecordAnalysis",
    meta: { index: 2, keepAlive: true },
    component: VisitRecordAnalysis,
  },
  {
    path: "/VisitSummaryBySeller",
    name: "VisitSummaryBySeller",
    meta: { index: 2, keepAlive: true },
    component: VisitSummaryBySeller,
  },
  {
    path: "/SupcustSummary",
    name: "SupcustSummary",
    meta: { index: 2, keepAlive: true },
    component: SupcustSummary,
  },

  {
    // 客户应收款
    path: "/Receivables",
    name: "Receivables",
    meta: { index: 2, keepAlive: true },
    component: Receivables,
  },
  {
    // 预收款余额报表
    path: "/PrepayBalance",
    name: "PrepayBalance",
    meta: { index: 2, keepAlive: true },
    component: PrepayBalance,
  },
  {
    // 客户流失预警·
    path: "/ClientLossWarning",
    name: "ClientLossWarning",
    meta: { index: 2, keepAlive: true },
    component: ClientLossWarning,
  },
  {
    // 客户往来账
    path: "/AccountHistory",
    name: "AccountHistory",
    meta: { index: 2, keepAlive: true },
    component: AccountHistory,
  },
  {
    // 银行支付对账表
    path: "/PayBillCheckTrades",
    name: "PayBillCheckTrades",
    meta: { index: 2, keepAlive: true },
    component: PayBillCheckTrades,
  },
  {
    // 其他收入·
    path: "/OtherInCome",
    name: "OtherInCome",
    meta: { index: 2, keepAlive: true },
    component: OtherInCome,
  },

  {
    // 调拨汇总表
    path: "/MoveSummary",
    name: "MoveSummary",
    meta: { index: 2, keepAlive: true },
    component: MoveSummary,
  },
  {
    // 销售单
    path: "/SaleSheet",
    name: "SaleSheet",
    component: SaleSheet,
    meta: {
      index: 6,
      keepAlive: true,
      key: (route) => `${route.query.sheetID || "new"}`,
    },
  },
  {
    // 分类  注意，这里的index不可以随便动，后打开的页面的index必须大于前一个页面的index,否则，push到后一个页面时，前一个页面的缓存就被清理掉了
    path: "/SelectItems/:data",
    name: "SelectItems",
    component: SelectItems,
    meta: { index: 6, keepAlive: true },
  },
  {
    path: "/SelectItemsHistory",
    name: "SelectItemsHistory",
    component: SelectItemsHistory,
    meta: { index: 7, keepAlive: true },
  },
  {
    // 报表
    path: "/CustomerRanking",
    name: "CustomerRanking",
    meta: { index: 3, keepAlive: true },
    component: CustomerRanking,
  },
  {
    // 铺市率
    path: "/ItemsOccupyReport",
    name: "ItemsOccupyReport",
    meta: { index: 3, keepAlive: true },
    component: ItemsOccupyReport,
  },
  {
    // 铺市率
    path: "/BrandsOccupyReport",
    name: "BrandsOccupyReport",
    meta: { index: 3, keepAlive: true },
    component: BrandsOccupyReport,
  },
  {
    // 库存查询
    path: "/Stock",
    name: "Stock",
    meta: { index: 3, keepAlive: true },
    component: Stock,
  },

  {
    // 收款对账
    path: "/CheckAccount",
    name: "CheckAccount",
    meta: { index: 3, keepAlive: true },
    component: CheckAccount,
  },
  {
    // 历史交账单
    path: "/CheckAccountHistory",
    name: "CheckAccountHistory",
    meta: { index: 3, keepAlive: true },
    component: CheckAccountHistory,
  },
  // {
  //     // 库存详情
  //     path: '/StockIndex/:itemId/:branchId/:branchName',
  //     name:'StockIndex',
  //     meta: { index: 3, keepAlive: true },
  //     component: StockIndex,
  // },
  {
    // 库存详情
    path: "/StockIndex",
    name: "StockIndex",
    meta: { index: 3, keepAlive: true },
    component: StockIndex,
  },
  {
    // 展示账户
    path: "/ShowAccount",
    name: "ShowAccount",
    meta: { index: 4, keepAlive: true },
    component: ShowAccount,
  },
  {
    path: "/AccountSummary",
    name: "AccountSummary",
    meta: { index: 4 },
    component: AccountSummary,
  },
  {
    path: "/AccountDetail",
    name: "AccountDetail",
    meta: { index: 4 },
    component: AccountDetail,
  },
  {
    path: "/AccountItem",
    name: "AccountItem",
    meta: { index: 4 },
    component: AccountItem,
  },
  {
    // 预收款单
    path: "/PrepaySheet",
    name: "PrepaySheet",
    meta: { index: 5, keepAlive: true },
    component: PrepaySheet,
  },

  {
    // 费用支出
    path: "/FeeOut",
    name: "FeeOut",
    meta: { index: 5, keepAlive: true },
    component: FeeOut,
  },
  {
    path: "/AddFeeOut",
    name: "AddFeeOut",
    meta: { index: 6, keepAlive: true },
    component: AddFeeOut,
  },
  {
    path: "/GetArrearsSheet",
    name: "GetArrearsSheet",
    meta: { index: 5, keepAlive: true },
    component: GetArrearsSheet,
  },
  {
    path: "/DeliveryReceipt",
    name: "DeliveryReceipt",
    meta: { index: 2, keepAlive: true },
    component: DeliveryReceipt,
  },
  {
    path: "/AllOrderSheets",
    name: "AllOrderSheets",
    meta: { index: 2, keepAlive: true },
    component: AllOrderSheets,
  },
  {
    path: "/ReviewOrderSheets",
    name: "ReviewOrderSheets",
    meta: { index: 2, keepAlive: true },
    component: ReviewOrderSheets,
  },
  {
    path: "/ApproveOrderSheets",
    name: "ApproveOrderSheets",
    meta: { index: 2, keepAlive: true },
    component: ApproveOrderSheets,
  },

  {
    path: "/PrintOrderSheets",
    name: "PrintOrderSheets",
    meta: { index: 2, keepAlive: true },
    component: PrintOrderSheets,
  },
  {
    path: "/AssignVanOrderSheets",
    name: "AssignVanOrderSheets",
    meta: { index: 2, keepAlive: true },
    component: AssignVanOrderSheets,
  },
  {
    path: "/ApproveAssignVan",
    name: "ApproveAssignVan",
    meta: { index: 2, keepAlive: true },
    component: ApproveAssignVan,
  },
  {
    path: "/ApproveBackBranch",
    name: "ApproveBackBranch",
    meta: { index: 2, keepAlive: true },
    component: ApproveBackBranch,
  },
  {
    path: "/BackBranchOrderSheets",
    name: "BackBranchOrderSheets",
    meta: { index: 2, keepAlive: true },
    component: BackBranchOrderSheets,
  },
  {
    path: "/ShowAssignVanInfo",
    name: "ShowAssignVanInfo",
    meta: { index: 2, keepAlive: true },
    component: ShowAssignVanInfo,
  },
  {
    path: "/ShowBackBranchInfo",
    name: "ShowBackBranchInfo",
    meta: { index: 2, keepAlive: true },
    component: ShowBackBranchInfo,
  },
  {
    path: "/BackBranchSheet",
    name: "BackBranchSheet",
    meta: { index: 2, keepAlive: true },
    component: BackBranchSheet,
  },
  {
    path: "/ViewSheets",
    name: "ViewSheets",
    meta: { index: 2, keepAlive: true },
    component: ViewSheets,
  },

  {
    path: "/CustomerArchives",
    name: "CustomerArchives",
    meta: { index: 3, keepAlive: true },
    component: CustomerArchives,
  },
  {
    path: "/CustomerArchivesSon",
    name: "CustomerArchivesSon",
    meta: { index: 6 },
    component: CustomerArchivesSon,
  },

  {
    path: "/SupplierArchives",
    name: "SupplierArchives",
    meta: { index: 3, keepAlive: true },
    component: SupplierArchives,
  },
  {
    path: "/SupplierArchivesSon",
    name: "SupplierArchivesSon",
    meta: { index: 6 },
    component: SupplierArchivesSon,
  },

  {
    path: "/GoodsArchives",
    name: "GoodsArchives",
    meta: { index: 3, keepAlive: true },
    component: GoodsArchives,
  },
  {
    path: "/GoodsArchivesSon",
    name: "GoodsArchivesSon",
    meta: { index: 4 },
    component: GoodsArchivesSon,
  },
  {
    path: "/MoveSheet",
    name: "MoveSheet",
    component: MoveSheet,
    meta: { index: 5, keepAlive: true },
  },
  {
    path: "/SelectMoveItems",
    name: "SelectMoveItems",
    meta: { index: 6 },
    component: SelectMoveItems,
  },
  {
    path: "/SpecialPriceSheet",
    name: "SpecialPriceSheet",
    meta: { index: 3, keepAlive: true },
    component: SpecialPriceSheet,
  },
  {
    path: "/SpecialPriceSelectItems",
    name: "SpecialPriceSelectItems",
    meta: { index: 3, keepAlive: true },
    component: SpecialPriceSelectItems,
  },
  {
    path: "/InventorySheet",
    name: "InventorySheet",
    meta: { index: 3, keepAlive: true },
    component: InventorySheet,
  },
  {
    path: "/InventoryChangeSheet",
    name: "InventoryChangeSheet",
    meta: { index: 3, keepAlive: true },
    component: InventoryChangeSheet,
  },
  {
    path: "/InventorySelectItems",
    name: "InventorySelectItems",
    meta: { index: 4 },
    component: InventorySelectItems,
  },
  {
    path: "/InventoryChangeSelectItems",
    name: "InventoryChangeSelectItems",
    meta: { index: 4 },
    component: InventoryChangeSelectItems,
  },
  {
    path: "/InventoryOrder",
    name: "InventoryOrder",
    component: InventoryOrder,
    meta: { index: 3, keepAlive: true },
  },
  {
    path: "/PrintersView",
    name: "PrintersView",
    meta: { index: 3 },
    component: PrintersView,
  },
  {
    path: "/Printer/:data",
    name: "Printer",
    meta: { index: 3 },
    component: Printer,
  },
  {
    path: "/UpdatePassword",
    name: "UpdatePassword",
    meta: { index: 3 },
    component: UpdatePassword,
  },
  {
    path: "/AboutUs",
    name: "AboutUs",
    meta: { index: 3 },
    component: AboutUs,
  },
  {
    path: "/Service",
    name: "Service",
    meta: { index: 3 },
    component: Service,
  },
  {
    path: "/SwitchAccount",
    name: "SwitchAccount",
    meta: { index: 3 },
    component: SwitchAccount,
  },
  {
    path: "/FontSizeSetting",
    name: "FontSizeSetting",
    meta: { index: 3 },
    component: FontSizeSetting,
  },
  {
    path: "/ClassIficationinventory",
    name: "ClassIficationinventory",
    meta: { index: 3 },
    component: ClassIficationinventory,
  },
  {
    path: "/AddSheetRow",
    name: "AddSheetRow",
    meta: { index: 4 },
    component: AddSheetRow,
  },
  {
    path: "/Visit",
    name: "Visit",
    meta: { index: 4, keepAlive: true },
    component: Visit,
  },
  {
    path: "/SupcustRadar",
    name: "SupcustRadar",
    meta: { index: 4, keepAlive: true },
    component: SupcustRadar,
  },
  {
    path: "/VisitUser",
    name: "VisitUser",
    meta: { index: 3, keepAlive: true },
    component: VisitUser,
  },
  {
    path: "/VisitDay",
    name: "VisitDay",
    meta: { index: 3, keepAlive: true },
    component: VisitDay,
  },
  {
    path: '/VisitPlan',
    name: 'VisitPlan',
    meta: { index: 3, keepAlive: true },
    component: VisitPlan
  },
  {
    path: "/visitStandard",
    name: "VisitStandard",
    meta: { index: 3, keepAlive: true },
    component: VisitStandard,
  },
  {
    path: "/visitSchedule",
    name: "VisitSchedule",
    meta: {index: 3, keepAlive: true},
    component: VisitSchedule,
  },
  {
    path: "/promotionView",
    name: "PromotionView",
    meta: { index: 3, keepAlive: true },
    component: PromotionView,
  },
  {
    path: "/redPacketView",
    name: "RedPacketView",
    meta: { index: 3, keepAlive: true },
    component: RedPacketView,
  },
  {
    path: "/displayAgreementSheet",
    name: "displayAgreementSheet",
    meta: { index: 3, keepAlive: true },
    component: DisplayAgreementSheet,
  },
  {
    path: "/displaySelectItem",
    name: "displaySelectItem",
    meta: { index: 4, keepAlive: true },
    component: DisplaySelectItem,
  },
  {
    path: "/DeliveryReceipt",
    name: "DeliveryReceipt",
    meta: { index: 3, keepAlive: true },
    component: DeliveryReceipt,
  },
  {
    path: "/Attendance",
    name: "Attendance",
    meta: { index: 3, keepAlive: true },
    component: Attendance,
  },
  {
    path: "/AttenceLeave",
    name: "AttenceLeave",
    meta: { index: 3, keepAlive: true },
    component: AttenceLeave,
  },
  {
    path: "/SupcustDistribution",
    name: "SupcustDistribution",
    meta: { index: 3, keepAlive: true },
    component: SupcustDistribution,
  },
  {
    path: "/MsgSubscribeCompany",
    name: "MsgSubscribeCompany",
    meta: { index: 2, keepAlive: true },
    component: MsgSubscribeCompany,
  },
  {
    path: "/messageTodoList",
    name: "messageTodoList",
    meta: { index: 2, keepAlive: true },
    component: MessageTodoList,
  },
  {
    path: "/MessageProofPolishList",
    name: "MessageProofPolishList",
    meta: { index: 2, keepAlive: true },
    component: MessageProofPolishList,
  },
  {
    path: "/MessageNoticeList",
    name: "MessageNoticeList",
    meta: { index: 2, keepAlive: true },
    component: MessageNoticeList,
  },
  {
    path: "/ViewSheetAll",
    name: "ViewSheetAll",
    meta: { index: 2, keepAlive: true },
    component: ViewSheetAll,
  },
  {
    path: "/AccountSupplierHistory",
    name: "AccountSupplierHistory",
    meta: { index: 2, keepAlive: true },
    component: AccountSupplierHistory,
  },
  {
    path: "/GetAndPayArrearsSheet",
    name: "GetAndPayArrearsSheet",
    meta: { index: 2, keepAlive: true },
    component: GetAndPayArrearsSheet,
  },
  {
    path: "/DisplayAgreementItem",
    name: "DisplayAgreementItem",
    meta: { index: 2, keepAlive: true },
    component: DisplayAgreementItem,
  },
  {
    // 会匹配所有路径
    path: "*",
    redirect: "/Home",
    meta: { index: 0, keepAlive: true },
  },
]
const router = new VueRouter({
  mode: "hash",
  routes,
  scrollBehavior: function(to, from, savedPosition) {
    // console.log(to) // to：要进入的目标路由对象，到哪里去
    // console.log(from) // from：离开的路由对象，哪里来
    // console.log(savedPosition) // savePosition：会记录滚动条的坐标，点击前进/后退的时候记录值{x:?,y:?}
    // 从第二页返回首页时savedPosition为undefined
    /*var backFromViewSheets=false;
        if( from.name == 'ViewSheets' && (to.name=="Whole" || to.name=="Workbench")){
            backFromViewSheets=true;
        }*/
    var bBack = false
    //savedPosition为null时，是router.push,或replace触发的，非空时，是go(-1)触发
    var bCausedByRealGoBack =
      savedPosition || typeof savedPosition === "undefined"

    if (true || window.g_bUseMyGoBack) {
      if (window.g_bGoBack) {
        window.g_bGoBack = false
        bBack = true
      }
    }
    //if (!bCausedByRealGoBack) //后退|| from.name == 'ViewSheets'
    {
      //if(window.g_bUseMyGoBack && !bBack){
      if (!bBack) {
        if (window.g_bReplacingPage) {
          window.g_bReplacingPage = false
        } else {
          if (window.g_prePage && window.g_prePage.name == from.name) {
            window.g_routerArray.push(window.g_prePage)
          } else {
            window.g_routerArray.push({
              name: from.name,
              path: from.path,
              id: "",
            })
          }
        }
      }
    }

    window.g_bPushing=false
    
    if (
      (window.g_bUseMyGoBack && bBack) ||
      bCausedByRealGoBack /*||backFromViewSheets*/
    ) {
      //后退|| from.name == 'ViewSheets'
      //判断是否是相邻得tab（tab跳转一定要replace跳转），如果是，那么每次打开都是获取最新页面
      if (typeof from.meta.tabs !== "undefined" && from.meta.tabs.length > 0) {
        //是tabs

        //清空tabs之间切换缓存，tabs直接跳转是不需要缓存的，因为大家都是同级页面
        from.meta.tabs.map(function(vo, index) {
          store.commit("delcachePageName", vo)
        })
      } else {
        //不是tabs
        var queuedSamePageCount = 0 //判断之前是否打开了同一个页面，如果打开了，当前页面关闭时不删除缓存
        if (window.g_routerArray.length > 0) {
          window.g_routerArray.forEach((page) => {
            if (page.name == from.name) {
              queuedSamePageCount++
            }
          })
        }
        //console.log('queuedSamePageCount',queuedSamePageCount)
        //后退到缓存页面，那么这里需要删除上一个页面的缓存，因为你要是再次打开，肯定要获取最新的页面数据 A-B-C 如果从C返回B肯定要把C的缓存删除，不然你从B-C，那就显示C的缓存了
        if (from.name != "SelectItems" && queuedSamePageCount == 0) {
          store.commit("delcachePageName", from.name)
        }
        if (from.name == "SaleSheet" && queuedSamePageCount == 0) {
          console.log("SelectItems")
          store.commit("delcachePageName", "SelectItems")
        }
        if (
          "SaleSheet,SalesOrder,ReturnSlip,ReturnOrder,MoveSheet,GetArrearsSheet,InventoryList,LabelPrintSheet".indexOf(
            from.name
          ) >= 0
        ) {
          //      store.commit('classActive', []);
          store.commit("currentSheet", [])
        }
        
        if (
          to.matched &&
          to.matched.length > 0 &&
          to.matched[0].instances &&
          to.matched[0].instances.default
        ) {
          var toPage = to.matched[0].instances.default
          // 使用 nextTick 确保组件完全初始化后再调用方法
          if (toPage && typeof toPage.onBackToThisPage === 'function') {
            // 立即尝试调用
            try {
              toPage.onBackToThisPage(from.name, window.g_backParams)
            } catch (error) {
              console.log('立即调用 onBackToThisPage 失败:', error)
              // 如果立即调用失败，延迟调用
              setTimeout(() => {
                try {
                  if (toPage && typeof toPage.onBackToThisPage === 'function') {
                    toPage.onBackToThisPage(from.name, window.g_backParams)
                  }
                } catch (delayedError) {
                  console.log('延迟调用 onBackToThisPage 也失败:', delayedError)
                }
              }, 100)
            }
          } else {
            if(toPage.active!==undefined){
              if(toPage.$children){
                toPage.$children.forEach(function(child) {
                  if (child.tabIndex === toPage.active) {
                    child.onBackToThisPage(from.name, window.g_backParams) 
                  }
                })
              }
            }
            //console.log('onBackToThisPage 方法不存在或不是函数:', typeof toPage?.onBackToThisPage)
          }
        }
      }

      if (to.name == "ViewSheets") {
        
        if (
          to.matched &&
          to.matched.length > 0 &&
          to.matched[0].instances &&
          to.matched[0].instances.default
        ) {
          var tabIndex = to.matched[0].instances.default.active
          //  var $tabRoot = $('.van-tabs__content');
          //var $box = $tabRoot.find('.van-list').parent().parent();
          var $box = $(".van-tab__pane:visible")
          if ($box.length > 0) {
            var box = $($box[0]).find(".van-pull-refresh")[0]
            box.scrollTop = window["scrollTop_" + to.name + "_tab_" + tabIndex]
          }
        }
      } else {
        var clsName = window["scrollBoxClass_" + to.name]
        var scrollTop = window["scrollTop_" + to.name]
        if (clsName && scrollTop > 0) {
          console.log(scrollTop)
          // var tmStart=new Date()
          //var timer=0
          function setScroll() {
            var $box = $("." + clsName + ":visible")
            if ($box.length > 0) {
              $box[$box.length - 1].scrollTop = scrollTop
              //clearInterval(timer)
              return
            }
            //var now=new Date()
            // var span=now.getTime()-tmStart.getTime()
            // if(span>400)  clearInterval(timer)
          }
          //timer=setInterval(()=>{
             setScroll()
          //},200)
          /*
                         setTimeout(()=>{
                             var $box = $('.' + clsName+':visible');
                             if ($box.length > 0) {
                             $box[0].scrollTop = scrollTop;
                             }
                         },350)*/

          //savedPosition={x:0,y:scrollTop}
        }
        //window['scrollTop_' + to.name] = 0;
      }

      /*
            var $s = $('#viewBox_' + to.name);
            if ($s.length > 0) {
                var s = $s[0];
                if (window['scrollTop_' + to.name])
                    s.scrollTop = window['scrollTop_' + to.name];
            }*/
      //后退滚动到上一次位置
      if (savedPosition) {
        return savedPosition
      }
    } else {
      //前进

      // window.g_prePage=from.name

      //前进把前进的页面加入到要缓存的组件中
      if (to.meta.keepAlive) {
        if (to.name != from.name) {
          if (store.state.cachePageName === "" && to.name === "Workbench") {
            store.commit("addcachePageName", "Home")
          } else {
            store.commit("addcachePageName", to.name)
          }
        } else if (to.name == "Workbench") {
          store.commit("addcachePageName", "Home")
        }
      }
      
      console.log(from.name)
      // ViewSheetAll页面内部组件自行完成滚动条处理， 取消下面神奇的全局滚动 -_- !!!
      if (from.name === "ViewSheetAll") {
        return
      }
      if (from.name == "ViewSheets") {
        if (
          from.matched &&
          from.matched.length > 0 &&
          from.matched[0].instances &&
          from.matched[0].instances.default
        ) {
          var tabIndex = from.matched[0].instances.default.active
          //var $tabRoot = $('.van-tabs__content');
          // var $box = $tabRoot.find('.van-list').parent().parent();
          var $box = $(".van-tab__pane:visible")

          if ($box.length > 0) {
            var box = $($box[0]).find(".van-pull-refresh")
            if (box.length > 0) {
              box = box[0]
              window["scrollTop_" + from.name + "_tab_" + tabIndex] =
                box.scrollTop
            }
          }
        }
      } else {
        var $box = $("div")
        if ($box.length > 0) {
          for (var i = 0; i < $box.length; i++) {
            var item = $box[i]
            if (item.scrollTop > 0) {
              window["scrollTop_" + from.name] = item.scrollTop
              window["scrollBoxClass_" + from.name] = (
                item.className || ""
              ).split(" ")[0]
              break
            } else if (window["scrollTop_" + from.name]) {
              window["scrollTop_" + from.name] = ""
            }
          }
        }
      }

      //前进滚动到顶部
      return { x: 0, y: 0 }
    }
  },
})
router.beforeEach((to, from, next) => {
 
  if ( window.g_bGoBack ) {
    
    //判断是否是相邻得tab（tab跳转一定要replace跳转），如果是，那么每次打开都是获取最新页面
    if (typeof from.meta.tabs !== "undefined" && from.meta.tabs.length > 0) {
      //是tabs 
    } else {
   
      if (
        from.matched && 
        from.matched.length > 0 &&
        from.matched[0].instances &&
        from.matched[0].instances.default
      ) {
        var fromPage = from.matched[0].instances.default
       
        if (fromPage.onThisPageClose){
          if(!window.g_backParams) window.g_backParams={}
          fromPage.onThisPageClose(window.g_backParams)
        }
      } 
    }
  }

    if (to.params.data === 'Workbench') {
        // 如果 data 参数为 'Workbench'，将其视为无效并重定向
        next('/Workbench');  // 重定向到首页或其他页面
    } else {
        next();  // 正常继续路由
    }
});
window.g_router = router
export default router
