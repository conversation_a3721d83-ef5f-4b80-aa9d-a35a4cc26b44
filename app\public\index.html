<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <meta name="viewport"
    content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover" />
    <meta name="viewport" content="width=device-width,maximum-scale=1.0,initial-scale=1,user-scalable=no">
  <!-- 开启 safe-area-inset-bottom 属性 -->
  <!--<van-number-keyboard safe-area-inset-bottom />
  <link rel="icon" href=" BASE_URL favicon.ico">-->
  <!-- <link
  rel="stylesheet"
  href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"
/> -->
  <script type="text/javascript" src="cordova.js"></script>

  <!-- JsBarcode 库 - 用于标签打印中的条码生成 -->
  <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
  <!-- 本地备份，当CDN加载失败时使用 -->
  <script>
    // 检查JsBarcode是否已加载
    if (typeof JsBarcode === 'undefined') {
      // 加载本地备份
      document.write('<script src="./js/JsBarcode.all.min.js"><\/script>');
    }
  </script>

  <!--<title><%= htmlWebpackPlugin.options.title %></title>  -->
    <script type="text/javascript" src="js/index.js"></script>
    <!--zxk modify 2021/3/16-->
    <!-- <script type="text/javascript" src="http://api.map.baidu.com/api?v=2.0&ak=p9QBVLhfTeBAUnP43RDzEm1acmYPn8Bu"></script>
    <script type="text/javascript" src="http://api.map.baidu.com/api?type=webgl&v=1.0&ak=p9QBVLhfTeBAUnP43RDzEm1acmYPn8Bu"></script>
    <script type="text/javascript" src="http://bj.bcebos.com/v1/mapopen/github/BMapGLLib/Lushu/src/Lushu.min.js"></script>   -->
    <!--<title><%= htmlWebpackPlugin.options.title %></title>  -->
    <!-- <script src="static/common.js"></script> -->
    <!-- <script src="https://mapv.baidu.com/build/mapv.js"></script>
    <script src="https://code.bdstatic.com/npm/mapvgl@1.0.0-beta.159/dist/mapvgl.min.js"></script>
    <script src="https://code.bdstatic.com/npm/mapvgl@1.0.0-beta.159/dist/mapvgl.threelayers.min.js"></script> -->
    <!-- <link href="//mapopen.bj.bcebos.com/github/BMapGLLib/RichMarker/src/RichMarker.min.js" rel="stylesheet">
  <script src="//mapopen.bj.bcebos.com/github/BMapGLLib/RichMarker/src/RichMarker.min.js"></script>    <script type="text/javascript" src="js/yj_markercluster.js"></script> -->
    <!-- <script type="text/javascript" src="js/yj_markerclusterGL.js"></script> -->

    <script type="text/javascript">
      scanPlug.initialize();
    </script>
</head>
<body>
  <noscript>
    <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled.
      Please enable it to continue.001</strong>
  </noscript>

  <div id="app" >
    <style>
      html[data-theme='dark'] .app-loading {
        background-color: #2c344a;
      }

      html[data-theme='dark'] .app-loading .app-loading-title {
        color: rgb(255 255 255 / 85%);
      }

      .app-loading {
        display: flex;
        width: 100%;
        height: 100%;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        background-color: #f4f7f9;
      }

      .app-loading .app-loading-wrap {
        position: absolute;
        top: 50%;
        left: 50%;
        display: flex;
        transform: translate3d(-50%, -50%, 0);
        justify-content: center;
        align-items: center;
        flex-direction: column;
      }

      .app-loading .dots {
        display: flex;
        padding: 98px;
        justify-content: center;
        align-items: center;
      }

      .app-loading .app-loading-title {
        display: flex;
        margin-top: 30px;
        font-size: 30px;
        color: rgb(0 0 0 / 85%);
        justify-content: center;
        align-items: center;
      }

      .app-loading .app-loading-logo {
        display: block;
        width: 90px;
        margin: 0 auto;
        margin-bottom: 20px;
      }

      .dot {
        position: relative;
        display: inline-block;
        width: 48px;
        height: 48px;
        margin-top: 30px;
        font-size: 32px;
        transform: rotate(45deg);
        box-sizing: border-box;
        animation: antRotate 1.2s infinite linear;
      }

      .dot i {
        position: absolute;
        display: block;
        width: 20px;
        height: 20px;
        background-color: #cc0000;
        border-radius: 100%;
        opacity: 30%;
        transform: scale(0.75);
        animation: antSpinMove 1s infinite linear alternate;
        transform-origin: 50% 50%;
      }

      .dot i:nth-child(1) {
        top: 0;
        left: 0;
      }

      .dot i:nth-child(2) {
        top: 0;
        right: 0;
        animation-delay: 0.4s;
      }

      .dot i:nth-child(3) {
        right: 0;
        bottom: 0;
        animation-delay: 0.8s;
      }

      .dot i:nth-child(4) {
        bottom: 0;
        left: 0;
        animation-delay: 1.2s;
      }
      @keyframes antRotate {
        to {
          transform: rotate(405deg);
        }
      }
      @keyframes antRotate {
        to {
          transform: rotate(405deg);
        }
      }
      @keyframes antSpinMove {
        to {
          opacity: 100%;
        }
      }
      @keyframes antSpinMove {
        to {
          opacity: 100%;
        }
      }
    </style>
    <div class="app-loading">
      <div class="app-loading-wrap">
        <img src="./logo.svg" class="app-loading-logo" alt="Logo"/>
        <div class="app-loading-dots">
          <span class="dot dot-spin"><i></i><i></i><i></i><i></i></span>
        </div>
        <div class="app-loading-title">请稍候...</div>
      </div>
    </div>
  </div>
  <!-- built files will be auto injected -->
</body>

</html>
