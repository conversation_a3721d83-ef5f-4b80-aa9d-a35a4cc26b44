<template>
  <div class="public_query">
    <div class="public_query_title">
      <van-form>
        <div class="public_query_titleSrc">
          <div class="public_query_titleSrc_item">
            <input type="text" v-model="queryCondiValues.searchStr" placeholder="客户名/手机" @input="onClientNameInput" />
            <van-icon name="user-o" />
          </div>
          <div class="public_query_titleSrc_item">
            <YjSelectTree
            ref="selectTreeRef"
            :target="target"
            :title="title"
            :confirmColor="confirmColor"
            :rangeKey="rangeKey"
            :rootNode="rootNode"
		        :idKey="idKey"
            :sonNode="sonNode"
            :multipleCheck="multipleCheck"
            :parentSelectable="parentSelectable"
            :popupHeight="'90%'"
            @getRootNode="getRootNode"
            @handleConfirm="onRegionSelected"
            >
              <template #select-tree-content>
                <input type="text" v-model="queryCondiLabels.regionName" placeholder="选择片区" readonly />
                <van-icon name="wap-home-o" />
              </template>
            </YjSelectTree>
          </div>
        </div>
      </van-form>
      <van-cell-group class="cellgroup" style="margin-top:2px;display: flex;">
        <van-field v-model="queryCondiLabels.dateTimeInfo" style="color:#ccc;" readonly label="" placeholder="日期范围" @click="showDate = true" />
        <div style="display:flex;margin-right: 13px;">
          <!-- <van-checkbox v-model="isChecked" v-if="!queryCondiValues.isDone" @click="selectAll(isChecked)" icon-size="18px">
          </van-checkbox> -->
          <van-checkbox v-model="isChecked"  @click="selectAll(isChecked)" icon-size="18px">
          </van-checkbox>
        </div>
        <!-- <van-button plain type="default" v-if="!queryCondiValues.isDone" @click="allChecked" style="padding:unset">全选</van-button> -->
      </van-cell-group>

    </div>
    <van-pull-refresh style="height: calc(100% - 138px);background:#eee;overflow-y:auto;" v-model="pullRefreshLoading" @refresh="onRefresh">

      <van-list v-model="loading" :finished="finished" finished-text="到底了~" @load="onListLoad" :immediate-check="false">
        <ul class="sales_ul" ref="sales_ul">
          <!--隐藏顶部 下拉距离参照-->
          <div ref="deliveryScroll"></div>
          <li v-for="(item, index) in deliveryList" @click.stop="onClientRowClick(item,$event)" :key="index" :ref="'sales_li' + item.order_sheet_no">
            <van-swipe-cell style="width:100%">
              <div class="sheet_wrapper" :style="{background:item.state=='submited'?'#eee':'#fff'}">
                <div class="sup_info">
                  <div class="sup_name">
                    {{ item.sup_name }}
                    <div v-if="item.order_source" class="order-source-wrapper">
                      {{item.order_source === 'xcx' ? '商城' : ''}}
                    </div>
                  </div>
                  <div class="sheet_no_tag" v-if="queryCondiValues.isDone">
                    <div class="sheet_no">￥{{item.total_amount}}</div>

                  </div>
                </div>
                <div style="color:#999;text-align:left;padding-left:8px;">
                  <div style="display:flex;">
                    <div style="width:100px;">
                      {{getShortTime(item.happen_time)}}
                    </div>
                    <div style="width:150px;">
                      {{ item.order_sheet_no}}
                    </div>
                    <!-- <div style="width:calc(100% - 250px);display:flex;justify-content:flex-end;" v-if="!queryCondiValues.isDone">
                      <van-checkbox style="width:40px;" v-model="item.checked" @click="handleCheckBox(index)" id="check" />
                    </div> -->
                    <div style="width:calc(100% - 250px);display:flex;justify-content:flex-end;" >
                      <van-checkbox style="width:40px;" v-model="item.checked" @click="handleCheckBox(index)" id="check" />
                    </div>
                  </div>
                  <div v-if="queryCondiValues.isDone" style="display:flex;">
                    <div style="color: #1989fa;display:flex;justify-content:space-between;" @click.stop="onMoveInfoClick(item)">
                      {{getShortTime(item.move_time)}}
                      {{item.op_no }}

                    </div>

                    <div style="white-space: nowrap; max-height: 35px; color:#f88;border: 1px solid #f88;padding: 1px 5px;border-radius: 4px;margin-left: 10px;">
                      {{item.approve_time ? '已审' : '未审'}}
                    </div>

                  </div>
                </div>
                <div style="padding-top: 2px;width: 100%;color:#999;padding-left: 5px;text-align:left;">
                  {{item.sup_addr}}
                </div>
                <div style="margin:4px 5px 0;display:flex;justify-content:space-between;margin-right:20px;color:#999;">
                  <div v-if="item.seller_name">业务: {{item.seller_name}}</div>
                  <div v-if="queryCondiValues.isDone&&item.van_name">车辆:{{item.van_name}}</div>
                  <div v-if="(!queryCondiValues.isDone)&&item.branch_name">仓库:{{item.branch_name}}</div>
                  <div v-if="item.senders_name">送货: {{item.senders_name}}</div>
                  <div v-if="item.weight">重量: {{item.weight}}kg</div>
                </div>
                <div class="mark-brief" v-if="item.make_brief">
                  备注: {{item.make_brief}}
                </div>
              </div>
            </van-swipe-cell>
          </li>
        </ul>
      </van-list>

    </van-pull-refresh>
    <div class="sales_list_boxs_no" v-if="deliveryList.length === 0">
      <div class="whole_box_no_icon iconfont">&#xe664;</div>
      <p>没有相关订单</p>
    </div>

    <van-calendar v-model="showDate" type="range" @confirm="onConfirm" title="请选择起止日期" :allow-same-day="true" :min-date="minDate" />
    <!--<div class="wrapper" v-show="!popRegions">
      <div class="content">
        <span class="record">{{ total }}</span>单
      </div>
      <div v-if="checked_qty" class="content">
        已选<span class="record">{{ checked_qty }}</span>
      </div>
      <div v-else class="content">
        共<span class="record"></span>
      </div>
      <van-button square type="default" v-if="!queryCondiValues.isDone" @click="assignVan" class="button_assign">装车</van-button>

    </div>-->
    <!-- <van-popup style="overflow: hidden !important" v-model="popRegions" duration="0.4" position="bottom" :style="{ height: '90%', width: '100%' }">
      <RegionSelection @onRegionSelected="onRegionSelected"></RegionSelection>
    </van-popup> -->
    <!-- <van-popup v-model="showChooseNavigationApp" position="bottom" :style="{ height: '30%' }">
      <div class="navi-select-item" @click="onNaviSelectItem(item)" v-for="(item, index) in navigationAppList" :key="index">
        {{ item.name }}
      </div>
    </van-popup> -->
    <div class="wrapper" v-show="!popRegions">
      <div class="content">
        <span>{{ total }}</span>单
      </div>
      <div v-if="selectedSheetsCount && !queryCondiValues.isDone" style="padding-left:'5px'">
        已选<span class="record">{{selectedItemsCount}}</span><span class="record">  {{ selectedSheetsCount }}({{ selectedItemsWeightSum }}kg)</span>
      </div>
      <div v-else-if="!queryCondiValues.isDone" class="content">
        共<span class="record">{{ total_qty }}</span>
      </div>
      <van-button square type="default" v-if="!queryCondiValues.isDone" @click="assignVan" class="button_assign">装车</van-button>
      <van-button square type="default" v-if="queryCondiValues.isDone" @click="changeVan" class="button_assign">换车</van-button>
      <van-button square type="default" v-if="queryCondiValues.isDone&&canRetreatVan" @click="retreatVan" class="button_assign">回撤</van-button>

    </div>
  </div>
</template>
<script>
import { SwipeCell, Cell, CellGroup, Button, Toast, PullRefresh, Tag, Form, Icon, List, Popup, Field, Calendar, Dialog, Checkbox, CheckboxGroup } from "vant";
import {
  GetOrdersForAssignVan,
  SaleOrderSheetReject,
  SaleOrderSheetRecover,
  TransformToGaodePositionRequest,
  GetAssignedOrders,
  SaleOrderSheetRetreat
} from "../../../api/api";
import Navi from '../../service/Navi'
import YjSelectTree from "../../components/yjTree/YjSelectTree.vue";
// import RegionSelection from "../../components/RegionSelection";
import Position from '../../../components/Position';
export default {
  name: "ViewAssignVanOrderSheets",
  data() {
    return {
      pullRefreshLoading: false,
      disabledPullRefresh: false,
      coords: {},
      list: [],
      loading: false,
      finished: false,
      // showChangeVan:false,
      // changeVanInfo:{},
      pageSize: 20,
      startRow: 0,
      getTotal: true,
      isChecked: false,
      deliveryList: [],
      deliveryListSubmit: [],
      popRegions: false,
      selectedSupcustNavigatorInfo: {},
      total: 0, total_qty: '', checked_qty: '',checked_nums: 0,checked_nums_all: '',
      showDate: false,
      minDate: new Date(2000, 1, 1),
      maxDate: new Date(),
      target:'region',
      rangeKey: 'name',
			idKey: 'id',
      sonNode:'subNodes',
      asPage:true,
			multipleCheck: true,
			parentSelectable: true,
			foldAll: true,
			confirmColor:'#e3a2a2',
			title: '片区选择',
      rootNode:{},
      showClearBtn:false
    };
  },
  props: {
    queryCondiValues: Object,
    queryCondiLabels: Object,
  },
  activated() {

  },
  async mounted() {
    window.addEventListener("scroll", this.handleScrollY, true);
    var operRegions = this.$store.state.operInfo.operRegions;
    if (operRegions) {
      operRegions = JSON.stringify(operRegions);
      //operRegions=operRegions.replace('[').replace(']')
      this.queryCondiValues.operRegions = operRegions;
    }
    this.deliveryList.length = 0;
    if (hasRight('orderFlow.retreatFromVan.see')) {
      this.canRetreatVan = true;
    }

  },
  onHide() {
    window.removeEventListener("scroll", this.handleScrollY, false);
  },
  components: {
    "van-form": Form,
    "van-icon": Icon,
    "van-list": List,
    "van-popup": Popup,
    "van-tag": Tag,
    "van-pull-refresh": PullRefresh,
    "van-swipe-cell": SwipeCell,
    "van-cell": Cell,
    "van-cell-group": CellGroup,
    "van-button": Button,
    "van-field": Field,
    "van-calendar": Calendar,
    "van-checkbox": Checkbox,
    "van-checkbox-group": CheckboxGroup,
    [Dialog.Component.name]: Dialog.Component,
    // RegionSelection,
    YjSelectTree
  },
  computed: {
    allSheetQtySum() {
      var arr = this.total_qty.split(',')
      var s = this.sumQty(arr)
      return s
    },
    selectedSheetsCount(){
      var ct=0
      this.deliveryListSubmit.forEach((item) => {
        if (item.checked) {
           ct++ 
        }
      })
      return ct? ct+"单":0;
    },
    selectedItemsCount(){
      var arr = []
     // this.checked_nums=0
      this.deliveryListSubmit.forEach((item) => {
       // if (item.checked) {
          arr.push(item.sheet_qty)
         // this.checked_nums++
       // }
      })
      return this.sumQty(arr)
    },
    selectedItemsWeightSum(){
      var ret = 0;
     // this.checked_nums=0
      this.deliveryListSubmit.forEach((item) => {
       // if (item.checked) {
          ret += parseInt(item.weight);
         // this.checked_nums++
       // }
      })
      return ret;
    }
  },
  methods: {
    retreatVan(){
      var tempList = []
      var sheetIDs = ''
      this.deliveryListSubmit.forEach((item) => {
        //if (item.checked) {
          tempList.push(item)
          if (sheetIDs != '') sheetIDs += ','
          sheetIDs += item.order_sheet_id
        //}
      })
      var errMsg =""
      if (tempList.length > 0) {
        tempList.forEach((item) => {
          if(item.order_status=='待装车'){
            errMsg+=item.order_sheet_no+'装车单未审核,无法回撤;'
          }else if(item.order_status=='已转单'){
            errMsg+=item.order_sheet_no+'装车单已转单,无法回撤;'
          }

        })

        if(errMsg!=""){
          Toast.fail(errMsg)
          return 
        }
        let params = {
          operKey:this.$store.state.operKey,
          sheetIDs: sheetIDs,
          senders:[]
        }
        Dialog.confirm({
          title:"回撤",
          message:"确认要回撤到装车前吗?",
          width:"320px"
        }).then(()=>{
          SaleOrderSheetRetreat(params).then((res)=>{
            if(res.result==="OK"){
              Toast.success('回撤成功')
              this.newQuery()
            }else{
              Toast.fail(res.msg)
            }
          })
        })
/*
       // this.isChecked = false
        var arr = []
        this.checked_nums=0
      this.deliveryListSubmit.forEach((item) => {
       // if (item.checked) {
          arr.push(item.sheet_qty)
          this.checked_nums++
        //}
      })
      this.checked_qty = this.sumQty(arr)
*/
      }
      else {
        Toast.fail('未选择单据')
      }

    },
    changeVan(){
      var tempList = []
      var sheetIDs = ''
      this.deliveryListSubmit.forEach((item) => {
        if (item.checked) {
          tempList.push(item)
          if (sheetIDs != '') sheetIDs += ','
          sheetIDs += item.order_sheet_id
        }
      })
      var errMsg =""
      if (tempList.length > 0) {
        var branchList = []
        var branchName = ""
        tempList.forEach((item) => {
          if(item.order_status=='待装车'){
            errMsg+=item.order_sheet_no+'装车单未审核,无法换车;'
          }else if(item.order_status!='已装车'){
            errMsg+=item.order_sheet_no+'装车单已转单或发货,无法换车;'
          }

          if (branchList.length === 0) {
            branchList.push(item.van_name)
            branchName = item.van_name
          }
        })
        // if (branchFlag) {
        //   Toast.fail('选中订单不在一个仓库')
        //   branchName = ""
        //   return
        // }
        if(errMsg!=""){
          Toast.fail(errMsg)
          return 
        }
        window.g_toAssignVanSheets = this.deliveryListSubmit
        this.$router.push({
          path: '/ShowAssignVanInfo',
          query: {
            sheetIDs: sheetIDs, branchName: branchName, isDone: this.queryCondiValues.isDone,type:'changeVan',needChangeSheets:tempList
          }
        })
        /* this.isChecked = false
       
        var arr = []
        this.checked_nums=0
      this.deliveryListSubmit.forEach((item) => {
        if (item.checked) {
          arr.push(item.sheet_qty)
          this.checked_nums++
        }
      })
      this.checked_qty = this.sumQty(arr)
      */

      }
      else {
        Toast.fail('未选择单据')
      }
      // this.showChangeVan=true
    },
    handleScrollY() {
      //思路：监听参照元素和下拉位置的距离，当距离接近参照元素时可以进行下拉，当远离参照元素时，禁止下拉。
      //因为是下拉，所以与距离为负数 当与参照距离过远则禁用下拉组件 反之上拉到一定位置时，则开启下拉组件 (zxk)
      //console.log(this.$refs.deliveryScroll == undefined);
      if (this.$refs.deliveryScroll == undefined) {
        return;
      }
      if (this.$refs.deliveryScroll.getBoundingClientRect().top <= -50) {
        //   this.disabledPullRefresh = true;
      } else {
        //    this.disabledPullRefresh = false;
      }
    },
    onRefresh() {

      this.pullRefreshLoading = false;
      /*this.isChecked = false
      var arr = []
      this.checked_nums=0
      this.deliveryListSubmit.forEach((item) => {
        if (item.checked) {
          arr.push(item.sheet_qty)
          this.checked_nums++
        }
      })
      this.checked_qty = this.sumQty(arr)
      */
      this.newQuery();
    },

    newQuery() {
      this.startRow = 0;
      this.finished = false;
      this.loading = true;
      /*this.isChecked = false
      var arr = []
      //this.deliveryListSubmit=[]
      //this.checked_qty=""
      this.checked_nums=0
      this.deliveryListSubmit.forEach((item) => {
        if (item.checked) {
          arr.push(item.sheet_qty)
          this.checked_nums++
        }
      })
      this.checked_qty = this.sumQty(arr)
      */
      this.deliveryList = [];
      this.startRow = 0;
      this.onNextPage();
    },
    onListLoad() {
      this.onNextPage();
    },
    onNextPage() {
      if (this.finished) return;
      /*if (!window.isBoss()) {
        var sheetViewRange =window.getRightValue('delicacy.sheetViewRange.value')
     
        var sheetViewRangeOnAssignVan =window.getRightValue('delicacy.sheetViewRangeOnAssignVan.value')
        if(sheetViewRangeOnAssignVan=="department"||sheetViewRangeOnAssignVan=="all"){
          sheetViewRange=sheetViewRangeOnAssignVan
        }
        if (sheetViewRange == 'self') {
            this.queryCondiValues.operID = this.$store.state.operInfo.oper_id
        } else if (sheetViewRange == "department") {
            this.queryCondiValues.departID = this.$store.state.operInfo.depart_id;
        } else {
            this.queryCondiValues.departID = ''
        }

        
      }*/
      var is_sender = this.$store.state.operInfo.is_sender == 'True' ? true : false
      var needReview = window.getSettingValue('reviewOrderBeforeAssignVan').toLowerCase() == 'true'
      var ignorePayFailedSheetOnAssignVan = window.getSettingValue('ignorePayFailedSheetOnAssignVan').toLowerCase() == 'true'
      var sheetViewRange = window.getRightValue('delicacy.sheetViewRange.value')
      var sheetViewRangeOnAssignVan = window.getRightValue('delicacy.sheetViewRangeOnAssignVan.value')
      if (sheetViewRangeOnAssignVan && sheetViewRangeOnAssignVan!='default') {
        sheetViewRange = sheetViewRangeOnAssignVan
      }
      
      if (sheetViewRange == 'department') {
        this.queryCondiValues.departID = this.$store.state.operInfo.depart_id; 
      }
 
      let params = {
        ...this.queryCondiValues,
        pageSize: this.pageSize,
        startRow: this.startRow,
        getTotal: this.getTotal,
        isBoss: window.isBoss(),
        sheetViewRange: sheetViewRange,
        is_sender: is_sender,
        needReview: needReview,
        ignorePayFailedSheetOnAssignVan: ignorePayFailedSheetOnAssignVan
      }

      GetOrdersForAssignVan(params).then((res) => {
        if (res.result === "OK") {
          if (this.finished) return
          
          res.data.forEach((item) => {
            if (item.last_visit_time) {
              var today = new Date(this.getDatePart(new Date()))
              if (new Date(item.last_visit_time) > today) {
                item.isVisited = true
              }
            }
            if (item.distance) {
              item.distanceStr = this.processDistanceAndFormatUnit(parseFloat(item.distance));
            }
            item.state = ''
            var orderStatus = {
              zd: '已转单',
              pzc:'待装车',
              fh:'已发货',
              xd: '已下单',
              dd: '已打单',
              hk: '已回库',
              zc: '已装车'
            }
            item.order_status = orderStatus[item.order_status]
            item.checked = false
            this.deliveryList.push(item);
/*
            //this.deliveryListSubmit.push(item);
            const exists = this.deliveryListSubmit.some(delivery => delivery.order_sheet_id === item.order_sheet_id);
            // 如果不存在，才将item添加到列表中
            if (!exists) {
              this.deliveryListSubmit.push(item);
            }
*/
            this.deliveryList.forEach((item) => {
              this.deliveryListSubmit.forEach((itemSubmit)=>{
                if(item.order_sheet_id==itemSubmit.order_sheet_id){
                  item.checked=itemSubmit.checked
                }
              })
            })

          })

          this.total = res.total
          this.total_qty = res.total_qty
          this.sheetList = res.data
          this.loading = false
          this.startRow = Number(this.startRow) + this.pageSize
          if (this.deliveryList.length >= Number(res.total)) {
            this.finished = true
          }
        }
      });

      let that = this


    },
    isDistanceMoreOneKM(distance) {
      return distance > 1000;
    },
    processDistanceAndFormatUnit(distance) {
      let distanceStr = "";
      if (this.isDistanceMoreOneKM(distance)) {
        distance = distance / 1000;
        distanceStr = distance.toFixed(2) + " km ";
      } else {
        distanceStr = parseInt(distance) + " m ";
      }
      return distanceStr;
    },
    selectAll(isChecked) {
      this.isChecked = isChecked
      this.deliveryList.forEach((item) => {
        item.checked = isChecked
      })
      this.updateSubmitList()
      //this.updateCheckedQty()
    },
    handleCheckBox(index) {
      
      this.updateSubmitList()


      var flag = true
      /* var currentItem = this.deliveryListSubmit[index];
      if(currentItem.checked){
        this.checked_nums++
      }else{
        this.checked_nums--
      } */
    //  var qty_b = 0, qty_m = 0, qty_s = 0;
     // this.deliveryListSubmit.forEach((item) => {
     //   flag = flag && item.checked
    //  })
     // this.isChecked = flag
     // this.updateCheckedQty()
    },
    updateSubmitList(){

      this.deliveryList.forEach((item) => {

           //this.deliveryListSubmit.push(item);
          
           const exists = this.deliveryListSubmit.some(delivery => delivery.order_sheet_id === item.order_sheet_id);
            // 如果不存在，才将item添加到列表中
            if(item.checked && !exists){ 
              this.deliveryListSubmit.push(item);
            }
            else if(!item.checked && exists){
              this.deliveryListSubmit=this.deliveryListSubmit.filter(delivery => delivery.order_sheet_id != item.order_sheet_id);
            }
  
      })
    },

    /*updateCheckedQty() {
      var arr = []
      this.checked_nums=0
      this.deliveryListSubmit.forEach((item) => {
        if (item.checked) {
          arr.push(item.sheet_qty)
          this.checked_nums++
        }
      })
      this.checked_qty = this.sumQty(arr)
      this.checked_nums_all = this.checked_nums + "单"
    },*/
    sumQty(arr) {
      var qty_b = 0, qty_m = 0, qty_s = 0;
      arr.forEach((item) => {

        var ss = item

        var n = ss.indexOf("退:");
        if (n >= 0) {
          ss = ss.substring(0, n);
        }
        ss = ss.replace("销:", "");
        ss = ss.replace(" ", "");
        var arr = ss.split("大");
        if (arr.length == 2) {
          var q = parseFloat(arr[0])
          qty_b += q
          ss = arr[1]
        }
        arr = ss.split("中");
        if (arr.length == 2) {
          var q = parseFloat(arr[0])
          qty_m += q
          ss = arr[1]
        }
        arr = ss.split("小");
        if (arr.length == 2) {
          var q = parseFloat(arr[0])
          qty_s += q
          ss = arr[1]
        }
      })
      
      var checked_qty = ""
      if (qty_b > 0) checked_qty += toMoney(qty_b) + "大"
      if (qty_m > 0) checked_qty += toMoney(qty_m) + "中"
      if (qty_s > 0.001) checked_qty += toMoney(qty_s) + "小"
      
      return checked_qty
    },
    onMoveInfoClick(item) {
      let params = {
        op_id: item.op_id
      }
      GetAssignedOrders(params).then((res) => {
        if (res.result === "OK") {
          var opInfo = res.assignSheet
          opInfo.branch_name = opInfo.from_branch_name
          var sumSheet = res.sumSheet
          var rowsInfo = opInfo.sheetRows
          // sumSheet.sheetRows.forEach(row => {
          // row.quantity = 0
          // row.sheet_order_quantity=0
          // rowsInfo.forEach(item => {
          //     if (row.item_id === item.item_id && row.unit_no === item.unit_no  && row.branch_id == item.branch_id && row.branch_position == item.branch_position && row.batch_id == item.batch_id) {
          //       row.sheet_order_quantity += item.sheet_order_quantity
          //       row.sheet_order_unit_no = item.unit_no
          //       row.sheet_order_unit_factor = item.unit_factor
          //       row.quantity += item.quantity
          //     }
          //   })
          // })
          // var rowsInfo = opInfo.sheetRows
          // var sumMoveSheet = res.sumMoveSheet
          // sumSheet.sheetRows.forEach(row => {
          //   if (rowsInfo.length > 0) {
          //     rowsInfo.forEach(item => {
          //       if (row.item_id === item.item_id && row.unit_no === item.unit_no) {
          //         row.sheet_order_quantity = item.sheet_order_quantity
          //         row.sheet_order_unit_no = item.unit_no
          //         row.sheet_order_unit_factor = item.unit_factor
          //         row.quantity = item.quantity
          //       }
          //     })
          //   } else {
          //     sumMoveSheet.sheetRows.forEach(mRow => {
          //       if (row.item_id === mRow.item_id && row.unit_no === mRow.unit_no) {
          //         row.sheet_order_quantity = row.quantity
          //         row.sheet_order_unit_no = row.unit_no
          //         row.sheet_order_unit_factor = row.unit_factor
          //         row.quantity = mRow.quantity

          //       }
          //     })

          //   }
          // })
          this.$router.push({
            path: '/ShowAssignVanInfo',
            query: {
              sheetIDs: opInfo.sale_order_sheets_id, branchName: opInfo.branch_name, isDone: this.queryCondiValues.isDone, sumSheet: sumSheet, sheetsInfo: res.oldSheets, sheet: opInfo, moveStock: res.moveStock, newSheetsInfo: res.sheets, 
            }
          })
        } else {
          Toast.fail(res.msg)
        }
      })
    },
    assignVan(type) {
      var tempList = []
      var sheetIDs = ''
      this.deliveryListSubmit.forEach((item) => {
       // if (item.checked) {
          tempList.push(item)
          if (sheetIDs != '') sheetIDs += ','
          sheetIDs += item.order_sheet_id
       // }
      })
      if (tempList.length > 0) {
        var branchList = []
        var branchFlag = false
        var branchName = ""
        tempList.forEach((item) => {
          if (branchList.length === 0) {
            branchList.push(item.branch_name)
            branchName = item.branch_name
          } 
          else {
            if (branchName !== item.branch_name) {
              branchFlag = true
            }
          }
        })
        var canMultiBranchAssign =window.getSettingValue('canMultiBranchAssign').toLowerCase()=="true"
        if (!canMultiBranchAssign&&branchFlag) {
          Toast.fail('选中订单不在一个仓库')
          branchName = ""
          return
        }

        //window.g_toAssignVanSheets = this.deliveryListSubmit
        window.g_objPrePage=this
        this.$router.push({
          path: '/ShowAssignVanInfo',
          query: {
            sheetIDs: sheetIDs, branchName: branchName, isDone: this.queryCondiValues.isDone,type:'assignVan'
          }
        })
       // this.isChecked = false
       /* var arr = []
        this.checked_nums=0
        this.deliveryListSubmit.forEach((item) => {
          if (item.checked) {
            arr.push(item.sheet_qty)
            this.checked_nums++
          }
        })*/
     // this.checked_qty = this.sumQty(arr)

      }
      else {
        Toast.fail('未选择单据')
      }


    },
    clearSubmitList(){
      var lstSubmit=this.deliveryListSubmit
      var lst=this.deliveryList
      
      if(lstSubmit){
        lstSubmit.forEach(sht_s=>{
          let indexToRemove = lst.findIndex(sht => sht === sht_s);
          if (indexToRemove !== -1) {
              lst.splice(indexToRemove, 1);
          }
        })
        console.log('listSubmit',this.deliveryListSubmit)
        this.deliveryListSubmit.length=0
      }
    },
    onClientRowClick(item, e) {
      if ($(e.target).parents('#check').length > 0) return

      if (e.target.className == 'sheet_checked') {
        return
      }

      window.g_curSheetInList = item;
      window.g_curSheetList = this.sheetList
      this.$router.push({ path: '/SaleSheet', query: { sheetID: item.order_sheet_id, sheetType: item.order_sheet_type } })
    },
    // onSelectRegion() {
    //   this.popRegions = true;
    // },
    getRootNode(node) {
      this.rootNode=node
    },
    onRegionSelected(region) {
      // if (region.regionID) {
      //   this.queryCondiLabels.regionName = region.regionName;
      //   this.queryCondiValues.regionID = region.regionID;
      // } else {
      //   this.queryCondiLabels.regionName = "";
      //   this.queryCondiValues.regionID = "";
      // }
      if(region.length>0){
        let regionID=[];
        let regionName=[];
        region.forEach((item)=>{
          regionID.push(item.id)
          regionName.push(item.name)
        })
        this.queryCondiLabels.regionName = regionName.join(',');
        this.queryCondiValues.regionID = regionID.join(',');
      }else {
        this.queryCondiLabels.regionName = "";
        this.queryCondiValues.regionID = "";
      }
      this.$refs.selectTreeRef.handleCancel()
      this.newQuery();
    },
    onClientNameInput() {
      this.newQuery();
    },
    onConfirm(date) {
      const [start, end] = date;
      this.showDate = false;
      // this.dateTime.startDate = `${this.formatDate(start)}`;
      //this.dateTime.endDate = `${this.formatDate(end)}`;
      this.queryCondiValues.startDate = `${this.formatDate(start)}`
      this.queryCondiValues.endDate = `${this.formatDate(end)}`
      this.queryCondiLabels.dateTimeInfo = this.queryCondiValues.startDate + " ~ " + this.queryCondiValues.endDate;

      // this.dateTime.dateTimeInfo =this.dateTime.startDate + " 至 " + this.dateTime.endDate;
      this.$emit("handleDateSon", this.queryCondiValues)
    },
  },
};
</script>
<style lang="less" scoped>
// height:136px
@flex_w: {
  display: flex;
  flex-wrap: wrap;
};
@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
};
@flex_a_bw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
};
@flex_a_end: {
  display: flex;
  align-items: center;
  justify-content: flex-end;
};
@flex_a_flex: {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
};
.navi-select-item {
  font-size: 0.65rem;
  color: #1887f7;
  border-bottom: solid 0.025rem #ccc;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.public_query {
  background: #ffffff;
  height: 100%;
  .public_query_title {
    padding-top: 5px;
    border-bottom: 1px solid #f2f2f2;
    .public_query_titleSrc {
      padding: 0 10px;
      height: 35px;
      @flex_a_bw();
      margin-top: 5px;
      .public_query_titleSrc_item {
        width: 48%;
        height: 100%;
        border: 1px solid #cccccc;
        border-radius: 0px;
        overflow: hidden;
        position: relative;
        vertical-align: top;
        input {
          height: 100%;
          width: calc(100% - 45px);
          padding: 0 45px 0 5px;
          border: none;
          font-size: 15px;
          line-height: 35px;
          color: #333333;
          vertical-align: top;
        }
        span {
          position: absolute;
          right: 0;
          top: 0;
          bottom: 0;
          width: 45px;
          font-size: 16px;
          color: #000;
          @flex_a_j();
          background: #4c99e7;
        }
        .van-icon {
          position: absolute;
          right: 0;
          top: 0;
          bottom: 0;
          width: 30px;
          font-size: 22px;
          @flex_a_j();
          color: #666666;
        }
      }
    }
    .public_list_title {
      height: 40px;
      @flex_a_bw();
      margin-top: 5px;
      padding: 0 5px;
      div {
        height: 40px;
        line-height: 40px;
        font-size: 15px;
        text-align: center;
        width: calc(25% - 10px);
        padding: 0 5px;
        font-weight: 500;
        color: #333333;
      }
      div:first-child {
        width: calc(20% - 10px);
        text-align: left;
      }
      div:last-child {
        width: calc(25% - 10px);
        text-align: right;
      }
    }
  }
}
.sales_list_boxs {
  height: calc(100% - 88px);
  overflow-x: hidden;
  overflow-y: auto;
  background: #f2f2f2;
  width: 100%;
}
.sales_list_boxs_no {
  height: calc(100% - 54px);
  @flex_a_flex();
  background: #f2f2f2;
  color: #999999;
  .whole_box_no_icon {
    font-size: 50px;
  }
  p {
    font-size: 15px;
  }
}
.sales_ul {
  width: 100%;
  height: auto;
  overflow: hidden;
  //padding: 0 5px;
  //background: #ffffff;
  li {
    width: 100%;
    height: auto;
    overflow: hidden;
    //padding: 0px 10px;
    border-bottom: 0px solid #f2f2f2;
    display: flex;
    flex-direction: column;
    align-items: center;
    // .sales_ul_t {
    //   overflow: hidden;
    //   @flex_a_bw();
    //   height: auto;
    //   div {
    //     font-size: 15px;
    //   }
    //   .sales_ul_tl {
    //     color: #333333;
    //     width: 30%;
    //     text-align: left;
    //   }
    //   .sales_ul_tc {
    //     color: #333333;
    //     text-align: left;
    //     width: 43%;
    //   }
    //   .sales_ul_tr {
    //     font-size: 15px;
    //     color: #1989fa;
    //     width: 27%;
    //     text-align: right;
    //   }
    // }
    // .sales_ul_b {
    //   overflow: hidden;
    //   @flex_a_bw();
    //   height: auto;
    //   margin-top: 5px;
    //   .sales_ul_bl {
    //     font-size: 13px;
    //     color: #666666;
    //     width: 30%;
    //     min-height: 15px;
    //     text-align: left;
    //   }
    //   .sales_ul_bc {
    //     font-size: 13px;
    //     color: #666666;
    //     width: 43%;
    //     text-align: left;
    //   }
    //   .sales_ul_br {
    //     font-size: 13px;
    //     width: 27%;
    //     color: #666666;
    //     min-height: 30px;
    //     @flex_a_end();
    //     i {
    //       font-size: 22px;
    //       color: #1989fa;
    //       margin-left: 10px;
    //     }
    //   }
    // }
    .reject-btn {
      margin-left: 10px;
      display: flex;
      color: #fff;
      height: 100%;
      width: 100px;
      font-size: 18px;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      background: #ee0a24;
    }
  }
  // li:last-child {
  //   border-bottom: none;
  // }
}
.wrapper {
  position: fixed;
  left: 0px;
  bottom: 0px;
  width: 100%;
  height: 45px;
  font-size: 0.5em;
  color: #333;
  border-top: 0px solid #f2f2f2;
  box-shadow: 0 -2px 5px #f2f6fc;
  background-color: #fff;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: space-around;
  .content {
    padding-left: 15px;
  }
  .button_assign {
    padding-right: 15px;
    height: 35px;
    border-radius: 11px;
    margin-right: 5px;
    color: #333;
    background: #ffcccc;
  }
  .record {
    padding: 0 10px;
  }
}

.sheet_wrapper {
  width: 100%;
  border-radius: 0px;
  display: flex;
  flex-direction: column;
  margin-bottom: 5px;
  padding: 6px;

  background-color: #fff;
  box-sizing: border-box;

  .sup_info {
    border-bottom: 0px solid #f2f2f2;
    margin-bottom: 2px;
    margin: 0 5px;
    padding-bottom: 4px;
    display: flex;
    .sup_name {
      flex: 3;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      font-size: 18px;
      font-weight: bolder;
      text-align: left;
    }
    .order-source-wrapper {
      border: 0px solid #fde3e4;
      padding: 1px 5px;
      background-color: #fde3e4;
      border-radius: 10px;
      margin-left: 10px;
    }
    .sup_contact {
      flex: 2;
      font-size: 16px;
      display: flex;
      flex-direction: column;
      align-items: flex-end;

      .sup_tel {
        margin: 2px 0;
        a {
          color: rgb(12, 89, 190);
        }
      }
    }
  }

  .mark-brief {
    margin: 6px 5px 0;
    display: flex;
    color: #999;
  }
}
</style>

