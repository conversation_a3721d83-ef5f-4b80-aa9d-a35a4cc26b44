<template>
  <div class="public_query">
    <div class="public_query_title">
      <van-cell-group class="cellgroup" style="margin-top:2px;display: flex;">
        <van-field v-model="queryCondiLabels.dateTimeInfo" style="color:#ccc;margin-left: 24%;" readonly label="" placeholder="日期范围" @click="showDate = true" />
      </van-cell-group>
      <!-- 全选功能 -->
      <van-cell-group class="cellgroup" style="margin-top:2px;">
        <div style="display: flex; align-items: center; padding: 10px;">
          <van-checkbox v-model="isChecked" @change="selectAll" style="margin-right: 10px;">
           全选
          </van-checkbox>
        </div>
      </van-cell-group>
    </div>
    <van-pull-refresh :disabled="disabledPullRefresh" style="height: 90%" v-model="pullRefreshLoading" @refresh="onRefresh">
      <div class="sales_list_boxs" ref="sales_list_boxs" v-if="deliveryList.length > 0">
        <van-list v-model="loading" :finished="finished" finished-text="到底了~" @load="onNextPage">
          <ul class="sales_ul" ref="sales_ul">
            <!--隐藏顶部 下拉距离参照-->
            <div ref="deliveryScroll"></div>
            <li v-for="(item, index) in deliveryList" :key="index" :ref="'sales_li' + item.order_sheet_no">
              <van-swipe-cell style="width:100%">
                <div class="sheet_wrapper" :style="{background:item.state=='submited'?'#eee':'#fff'}">
                  <div style="display: flex; justify-content: flex-end; width: 95%;">
                     <van-checkbox v-model="item.checked" @click="handleCheckBox(index)" id="check" />
                  </div>
                  <div class="sup_info">
                    <div class="sup_name" style="justify-content: flex-start">
                      出仓:{{ item.from_branch_name }}
                    </div>
                    <div class="sup_name" style="justify-content: flex-end">
                      入仓:{{ item.to_van_name }}
                    </div>
                  </div>
                  <div class="sheet_info">
                    <div class="sheet_info_left">
                      <div class="sheet_no_tag" style="color: #1989fa; ">
                        <div class="sheet_no" @click.stop="onMoveInfoClick(item)">{{item.op_no }}</div>
                      </div>

                      <div class="sheet_no_tag" style="justify-content: flex-start">
                        <div class="sheet_happentime">订单:{{ item.order_count }}个</div>
                        <div class="sheet_happentime" style="margin-left:20px" v-if="item.move_stock.toLowerCase()=='true'">移库</div>
                        <div class="sheet_happentime" style="margin-left:20px" v-else>未移库</div>
                      </div>

                    </div>
                    <div class="sheet_info_left">
                      <div class="sheet_no_tag"  >
                        <div  class="sheet_type" v-if="item.op_type.toLowerCase()=='v2v'">
                          换车
                        </div>
                        <div class="sheet_type" v-if="item.op_type.toLowerCase()=='retreat'">
                          回撤
                        </div>
                        <div  style="color:rgb(102, 101, 101);border: 1px solid rgb(102, 101, 101);padding: 1px 5px;border-radius: 4px;font-size: 14px;" v-if="item.op_type.toLowerCase()=='2v'">装车</div>
                      </div>
                      <div class="sheet_happentime">
                        {{ item.happen_time }}
                      </div>
                    </div>
                  </div>
                  <div class="seller_senders_info">
                    <div v-if="item.oper_name">业务员: {{item.oper_name}}</div>
                    <div v-if="item.senders_name">送货员: {{item.senders_name}}</div>
                  </div>
                </div>
              </van-swipe-cell>
            </li>
          </ul>
        </van-list>
      </div>
    </van-pull-refresh>
    <div class="sales_list_boxs_no" v-if="deliveryList.length === 0">
      <div class="whole_box_no_icon iconfont">&#xe664;</div>
      <p>没有相关订单</p>
    </div>
    <van-popup style="overflow: hidden !important" v-model="showAddress" duration="0.4" position="bottom" :style="{ height: '90%', width: '100%' }">
      <RegionSelection @onRegionSelected="onRegionSelected"></RegionSelection>
    </van-popup>
    <van-popup v-model="showChooseNavigationApp" position="bottom" :style="{ height: '30%' }">
      <div class="navi-select-item" @click="onNaviSelectItem(item)" v-for="(item, index) in navigationAppList" :key="index">
        {{ item.name }}
      </div>
    </van-popup>
    <van-calendar v-model="showDate" type="range" @confirm="onConfirm" title="请选择起止日期" :allow-same-day="true" :min-date="minDate" :max-date="maxDate" />
    <div class="wrapper" v-show="!showAddress">
      <div class="content">
        共<span class="record">{{ total }}</span>条
      </div>
      <div v-if="selectedSheetsCount > 0" class="content">
        已选<span class="record">{{ selectedSheetsCount }}</span>单
      </div>
      <van-button @click="batchApprove" class="batch-approve-btn" v-if="selectedSheetsCount > 0">
        批量审核
      </van-button>

    </div>
  </div>
</template>
<script>
import { SwipeCell, Cell, CellGroup, Button, Toast, PullRefresh, Tag, Form, Icon, List, Popup, Field, Calendar, Dialog, Checkbox, CheckboxGroup } from "vant";
import {
  GetAssignVanForApprove,
  SaleOrderSheetReject,
  SaleOrderSheetRecover,
  TransformToGaodePositionRequest,
  GetAssignedOrders,
  AssignVan
} from "../../../api/api";
import Navi from '../../service/Navi'
import RegionSelection from "../../components/RegionSelection";
import Position from '../../../components/Position';
export default {
  name: "ViewApproveAssignVan",
  data() {
    return {
      pullRefreshLoading: false,
      disabledPullRefresh: false,
      coords: {},
      list: [],
      loading: false,
      finished: false,
      pageSize: 20,
      startRow: 0,
      getTotal: true,
      isChecked: false,
      navigationAppList: [
        {
          id: 1,
          name: "高德地图",
        },
        {
          id: 2,
          name: "百度地图",
        },
      ],
      showChooseNavigationApp: false,
      deliveryList: [],
      showAddress: false,
      selectedSupcustNavigatorInfo: {},
      total: 0,
      /*
      dateTime : {
        startDate: '',
        endDate: '',
        dateTimeInfo: ''
      },*/
      showDate: false,
      minDate: new Date(2000, 1, 1),
      maxDate: new Date(),
    };
  },
  props: {
    queryCondiValues: Object,
    queryCondiLabels: Object,
  },
  activated() {

  },
  async mounted() {
    window.addEventListener("scroll", this.handleScrollY, true);
    var operRegions = this.$store.state.operInfo.operRegions;
    if (operRegions) {
      operRegions = JSON.stringify(operRegions);
      //operRegions=operRegions.replace('[').replace(']')
      this.queryCondiValues.operRegions = operRegions;
    }
    this.deliveryList.length = 0;

  },
  onHide() {
    window.removeEventListener("scroll", this.handleScrollY, false);
  },
  components: {
    "van-form": Form,
    "van-icon": Icon,
    "van-list": List,
    "van-popup": Popup,
    "van-tag": Tag,
    "van-pull-refresh": PullRefresh,
    "van-swipe-cell": SwipeCell,
    "van-cell": Cell,
    "van-cell-group": CellGroup,
    "van-button": Button,
    "van-field": Field,
    "van-calendar": Calendar,
    "van-checkbox": Checkbox,
    "van-checkbox-group": CheckboxGroup,
    [Dialog.Component.name]: Dialog.Component,
    RegionSelection,
  },
  computed:{
    selectedSheetsCount() {
      console.log(this.deliveryList.filter(item => item.checked).length)
      return this.deliveryList.filter(item => item.checked).length
    }
  },
  methods: {
    handleScrollY() {
      //思路：监听参照元素和下拉位置的距离，当距离接近参照元素时可以进行下拉，当远离参照元素时，禁止下拉。
      //因为是下拉，所以与距离为负数 当与参照距离过远则禁用下拉组件 反之上拉到一定位置时，则开启下拉组件 (zxk)
      //console.log(this.$refs.deliveryScroll == undefined);
      if (this.$refs.deliveryScroll == undefined) {
        return;
      }
      if (this.$refs.deliveryScroll.getBoundingClientRect().top <= -50) {
        this.disabledPullRefresh = true;
      } else {
        this.disabledPullRefresh = false;
      }
    },
    onRefresh() {

      this.newQuery();

      this.pullRefreshLoading = false;
      this.isChecked = false

    },

    newQuery() {
      this.startRow = 0;
      this.finished = false;
      this.deliveryList = [];
      this.startRow = 0;
      this.onNextPage();
    },
    openNavigationAppDialog(item) {
      //打开弹出框并且把选中商家的导航信息存入全局变量
      this.selectedSupcustNavigatorInfo = {
        sup_addr: item.sup_addr,
        addr_lng: item.addr_lng,
        addr_lat: item.addr_lat,
      };

      this.showChooseNavigationApp = true;
    },
    onNaviSelectItem(item) {
      if (isiOS) {
        this.jumpiOSNaviUrlBySelectAppName(item.name);
      } else if(isHarmony){
        this.jumpHarmonyNaviUrlBySelectAppName(item.name);
      } else{
        this.jumpAndroidNaviUrlBySelectAppName(item.name);
      }
      //隐藏弹出框
      this.showChooseNavigationApp = false;
    },
    batchApprove(){
      const selectedSheets = this.deliveryList.filter(item => item.checked);
      if (selectedSheets.length === 0) {
        Toast.fail('请选择要审核的单据');
        return;
      }

      // 显示确认对话框
      Dialog.confirm({
        title: '批量审核',
        message: `确认要审核选中的 ${selectedSheets.length} 张装车单吗？`,
        width: "320px"
      }).then(() => {
        this.doBatchApprove(selectedSheets);
      }).catch(() => {
        // 用户取消
      });
    },

    // 执行批量审核
    async doBatchApprove(selectedSheets) {
      // 保存每条单据的审核结果
      const approveResults = [];

      // 显示审核进度
      Toast.loading({
        message: `正在审核 0/${selectedSheets.length} 张装车单...`,
        forbidClick: true,
        duration: 0
      });

      try {

        for (let i = 0; i < selectedSheets.length; i++) {
          const currentSheet = selectedSheets[i];

          Toast.loading({
            message: `正在审核 ${i + 1}/${selectedSheets.length} 张装车单...`,
            forbidClick: true,
            duration: 0
          });

          const result = await this.doSingleApprove(currentSheet);

          approveResults.push({
            sheet: currentSheet,
            success: result.success,
            message: result.message,
            sheetNo: result.sheetNo || currentSheet.op_no
          });

          await new Promise(resolve => setTimeout(resolve, 500));
        }

        Toast.clear();

        const successResults = approveResults.filter(r => r.success);
        const failResults = approveResults.filter(r => !r.success);

        this.showBatchApproveResults(successResults, failResults);

        this.isChecked = false;

        this.onRefresh();

      } catch (error) {
        Toast.clear();
        console.error('批量审核过程中发生错误:', error);
        Toast.fail('批量审核过程中发生错误，请重试');
      }
    },

    // 显示批量审核结果
    showBatchApproveResults(successResults, failResults) {
      const successCount = successResults.length;
      const failCount = failResults.length;

      if (failCount === 0) {
        // 全部成功
        Toast.success(`批量审核完成！\n成功审核 ${successCount} 张装车单`);
      } else {
        // 有失败的情况，显示详细信息
        let message = `批量审核完成！<br/>成功：${successCount} 张，失败：${failCount} 张<br/>`;

        if (successResults.length > 0) {
          message += '<div style="text-align: center;">成功的单据</div>';
          successResults.forEach(result => {
            message += 
              `<div style="text-align: left;font-size: 16px;">• 装车单:${result.sheetNo}<br/>`;
          });
        }

        if (failResults.length > 0) {
          message += '<br/><div style="text-align: center;">失败的单据：</div>';
          failResults.forEach(result => {
            message += 
              `<div style="text-align: left;font-size: 16px;">• 装车单:${result.sheetNo}<br/>` +
              '<div style="text-align: left;font-size: 16px;">失败原因:</div>' +
              `<div style="text-align: center;font-size: 15px;">${result.message}</div><br/><br/>`;
          });
        }

        Dialog.alert({
          title: '批量审核结果',
          message: message,
          width: '320px',
          confirmButtonText: '确定',
          allowHtml: true  // 允许HTML渲染
        });
      }
    },

    async doSingleApprove(item) {
      try {
        const assignedOrdersParams = {
          op_id: item.op_id
        };

        const assignedOrdersRes = await GetAssignedOrders(assignedOrdersParams);

        if (assignedOrdersRes.result !== "OK") {
          const errorMsg = `获取装车单信息失败: ${assignedOrdersRes.msg}`;
          return { success: false, message: errorMsg, sheetNo: item.op_no };
        }

        const sheetInfo = assignedOrdersRes.assignSheet;
        sheetInfo.branch_name = sheetInfo.from_branch_name;
        const sumSheet = assignedOrdersRes.sumSheet;

        let senders = [];
        const is_sender = this.$store.state.operInfo.is_sender === 'True';

        if (sheetInfo.senders_id) {
          const sendersID = sheetInfo.senders_id.split(',');
          sendersID.forEach(id => {
            const senderName = sheetInfo.senders_name || this.$store.state.operInfo.oper_name;
            senders.push(id + ',' + senderName);
          });
        } else if (is_sender) {
          senders.push(this.$store.state.operInfo.oper_id + ',' + this.$store.state.operInfo.oper_name);
        } else {
          const errorMsg = '请先安排送货员';
          return { success: false, message: errorMsg, sheetNo: sheetInfo.op_no || item.op_no };
        }

        if (!sheetInfo.to_van) {
          const errorMsg = '必须指定车辆';
          return { success: false, message: errorMsg, sheetNo: sheetInfo.op_no || item.op_no };
        }

        const approveParams = {
          sheetIDs: sheetInfo.sale_order_sheets_id,
          moveStock: assignedOrdersRes.moveStock,
          sumSheet: sumSheet,
          vanID: sheetInfo.to_van,
          senders: senders,
          sheets: assignedOrdersRes.oldSheets,
          newSheets: assignedOrdersRes.sheets,
          fromApp: true,
          submitType: 'approve', // 审核类型
          op_id: sheetInfo.sheet_id,
          op_no: sheetInfo.sheet_no
        };

        const approveRes = await AssignVan(approveParams);

        if (approveRes.result === "OK") {
          const index = this.deliveryList.findIndex(listItem => listItem.op_id === item.op_id);
          if (index !== -1) {
            this.deliveryList[index].state = 'approved';
            this.deliveryList[index].approve_time = approveRes.happen_time;
            this.deliveryList[index].checked = false; // 取消选中状态
          }

          return { success: true, message: '', sheetNo: sheetInfo.op_no };
        } else {
          const errorMsg = `${approveRes.msg}`;
          return { success: false, message: errorMsg, sheetNo: sheetInfo.op_no };
        }

      } catch (error) {
        console.error('审核装车单时发生错误:', error);
        const errorMsg = `审核时发生错误: ${error.message || '未知错误'}`;
        if (!silent) Toast.fail(errorMsg);
        return { success: false, message: errorMsg, sheetNo: item.op_no };
      }
    },


    async getCurPosition(){
        const positionService=new Position(isiOS)
        const position = await positionService.currentPosition()
        console.log(position)
        return position
    },
    async jumpHarmonyNaviUrlBySelectAppName(name) {
      //从全局变量中取选中商家的导航信息
      const navigationInfo = this.selectedSupcustNavigatorInfo;
      const { addr_lng, addr_lat, sup_addr } = navigationInfo
      const { longitude, latitude }=await this.getCurPosition()
      if (name == "百度地图") {
        const navi = new Navi("baidu", isiOS, addr_lng + "," + addr_lat, sup_addr, isHarmony)
        var ref = cordova.InAppBrowser.open(navi.getBaiduUrl(), "_system");
        ref.show();
      }
      if (name == "高德地图") {
        const navi = new Navi("gaode", isiOS,addr_lng + "," + addr_lat, sup_addr, isHarmony, longitude + "," + latitude)
        var ref = cordova.InAppBrowser.open(await navi.getGaoDeUrl(), "_system");
        ref.show();
      }
    },
    async jumpiOSNaviUrlBySelectAppName(name) {
      //从全局变量中取选中商家的导航信息
      const navigationInfo = this.selectedSupcustNavigatorInfo;
      const { addr_lng, addr_lat, sup_addr } = navigationInfo
      if (name == "百度地图") {
        const navi = new Navi("baidu", isiOS, addr_lng + "," + addr_lat, sup_addr)
        cordova.InAppBrowser.open(navi.getBaiduUrl(), "_system");
      }
      if (name == "高德地图") {
        const navi = new Navi("gaode", isiOS, addr_lng + "," + addr_lat, sup_addr)
        cordova.InAppBrowser.open(await navi.getGaoDeUrl(), "_system");

      }
    },

    async jumpAndroidNaviUrlBySelectAppName(name) {
      //从全局变量中取选中商家的导航信息
      const navigationInfo = this.selectedSupcustNavigatorInfo;
      const { addr_lng, addr_lat, sup_addr } = navigationInfo
      if (name == "百度地图") {
        const navi = new Navi("baidu", isiOS, addr_lng + "," + addr_lat, sup_addr)
        window.location.href = navi.getBaiduUrl()
      }
      if (name == "高德地图") {
        // const position = await this.baiduPoi2gaodePoi(addr_lng,addr_lat);
        // const lng = position.split(",")[0];
        // const lat = position.split(",")[1];
        const navi = new Navi("gaode", isiOS, addr_lng + "," + addr_lat, sup_addr)
        window.location.href = await navi.getGaoDeUrl()
      }
    },
    onNextPage() {
      if (this.finished) return;
      var branchRights = this.$store.state.operInfo.branchRights
      var branches = ""
      branchRights.forEach(branch => {
        if (branch.sheet_dc === 'True') {
          if (branches !== "") branches += ','
          branches += branch.branch_id
        }
      })
      if(branches =="") {
        Toast.fail("您无任何仓库的调拨出库权限")
        return
      }
      let params = {
        ...this.queryCondiValues,
        pageSize: this.pageSize,
        startRow: this.startRow,
        getTotal: this.getTotal,
        branches: branches
      }

      GetAssignVanForApprove(params).then((res) => {
        if (res.result === "OK") {
          if (this.finished) return
          // this.deliveryList = res.data
          res.data.forEach((item)=>{
            item.checked = false
            this.deliveryList.push(item)
          })

          this.total = res.total;
          this.sheetList = this.deliveryList
          this.loading = false;
          this.startRow = Number(this.startRow) + this.pageSize;
          if (this.deliveryList.length >= Number(res.total)) {
            this.finished = true;
          }
        }
      });

      let that = this


    },
    isDistanceMoreOneKM(distance) {
      return distance > 1000;
    },
    processDistanceAndFormatUnit(distance) {
      let distanceStr = ""
      if (this.isDistanceMoreOneKM(distance)) {
        distance = distance / 1000
        distanceStr = distance.toFixed(2) + " km "
      } else {
        distanceStr = parseInt(distance) + " m "
      }
      return distanceStr
    },
    selectAll(isChecked) {
      this.isChecked = isChecked
      this.deliveryList.forEach((item) => {
        item.checked = isChecked
      })
    },
    // 处理单个复选框变化
    handleCheckBox() {
      const allChecked = this.deliveryList.every(item => item.checked);
      this.isChecked = allChecked;
    },
    onMoveInfoClick(item) {
      let params = {
        op_id: item.op_id
      }
      GetAssignedOrders(params).then((res) => {
        if (res.result === "OK") {
          var opInfo = res.assignSheet
          opInfo.branch_name = opInfo.from_branch_name
          var sumSheet = res.sumSheet
          var rowsInfo = opInfo.sheetRows
          // sumSheet.sheetRows.forEach(row => {
          // row.quantity = 0
          // row.sheet_order_quantity=0
          // rowsInfo.forEach(item => {
          //     if (row.item_id === item.item_id && row.unit_no === item.unit_no  && row.branch_id == item.branch_id && row.branch_position == item.branch_position && row.batch_id == item.batch_id) {
          //       row.sheet_order_quantity += item.sheet_order_quantity
          //       row.sheet_order_unit_no = item.unit_no
          //       row.sheet_order_unit_factor = item.unit_factor
          //       row.quantity += item.quantity
          //     }
          //   })
          // })
          //var moveSheet = res.moveSheet
          // var sumMoveSheet = res.sumMoveSheet
          // var rowsInfo = opInfo.sheetRows
          // sumSheet.sheetRows.forEach(row => {

          //   if (rowsInfo.length > 0) {

          //     rowsInfo.forEach(item => {
          //       if (row.item_id === item.item_id && row.unit_no === item.unit_no) {
          //         row.sheet_order_quantity = item.sheet_order_quantity
          //         row.sheet_order_unit_no = item.unit_no
          //         row.sheet_order_unit_factor = item.unit_factor
          //         row.quantity = item.quantity
          //       }
          //     })
          //   } else {
          //     sumMoveSheet.sheetRows.forEach(mRow => {
          //       if (row.item_id === mRow.item_id && row.unit_no === mRow.unit_no) {
          //         row.sheet_order_quantity = row.quantity
          //         row.sheet_order_unit_no = row.unit_no
          //         row.sheet_order_unit_factor = row.unit_factor
          //         row.quantity = mRow.quantity

          //       }
          //     })
          //   }
          // })
          // sumSheet.sheetRows = sumMoveSheet.sheetRows
          // sumSheet.sum_quantity_unit_conv = sumMoveSheet.sum_quantity_unit_conv
          this.$router.push({
            path: '/ShowAssignVanInfo',
            query: {
              sheetIDs: opInfo.sale_order_sheets_id, branchName: opInfo.branch_name, isDone: true, sumSheet: sumSheet, sheetsInfo: res.oldSheets, sheet: opInfo, moveStock: res.moveStock,newSheetsInfo: res.sheets, 
            }
          })
        } else {
          Toast.fail(res.msg)
        }
      })
    },
    assignVan() {
      var tempList = []
      var sheetIDs = ''
      this.deliveryList.forEach((item) => {
        if (item.checked) {
          tempList.push(item)
          if (sheetIDs != '') sheetIDs += ','
          sheetIDs += item.order_sheet_id
        }
      })
      if (tempList.length > 0) {
        var branchList = []
        var branchName = ""
        tempList.forEach((item) => {
          if (branchList.length === 0) {
            branchList.push(item.branch_name)
            branchName = item.branch_name
          }
        })
        this.$router.push({
          path: '/ShowAssignVanInfo',
          query: {
            sheetIDs: sheetIDs, branchName: branchName, isDone: this.queryCondiValues.isDone
          }
        })
        this.isChecked = false

      }
      else {
        Toast.fail('未选择单据')
      }


    },
    onClientRowClick(item) {
      window.g_curSheetInList = item;
      window.g_curSheetList = this.sheetList
      this.$router.push({ path: '/MoveSheet', query: { sheetID: item.move_sheet_id, sheetType: 'DB' } })
    },
    onAddressShow() {
      this.showAddress = true;
    },
    onRegionSelected(region) {
      this.showAddress = region.isShow;
      if (region.regionID) {
        this.queryCondiLabels.regionName = region.regionName;
        this.queryCondiValues.regionID = region.regionID;
      } else {
        this.queryCondiLabels.regionName = "";
        this.queryCondiValues.regionID = "";
      }
      this.newQuery();
    },
    onClientNameInput() {
      this.newQuery();
    },
    onConfirm(date) {
      const [start, end] = date;
      this.showDate = false;
      // this.dateTime.startDate = `${this.formatDate(start)}`;
      //this.dateTime.endDate = `${this.formatDate(end)}`;
      this.queryCondiValues.startDate = `${this.formatDate(start)}`
      this.queryCondiValues.endDate = `${this.formatDate(end)}`
      this.queryCondiLabels.dateTimeInfo = this.queryCondiValues.startDate + " 至 " + this.queryCondiValues.endDate;

      // this.dateTime.dateTimeInfo =this.dateTime.startDate + " 至 " + this.dateTime.endDate;
      this.$emit("handleDateSon", this.queryCondiValues)
    },
  },
};
</script>
<style lang="less" scoped>
// height:136px
@flex_w: {
  display: flex;
  flex-wrap: wrap;
};
@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
};
@flex_a_bw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
};
@flex_a_end: {
  display: flex;
  align-items: center;
  justify-content: flex-end;
};
@flex_a_flex: {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
};
.navi-select-item {
  font-size: 0.65rem;
  color: #1887f7;
  border-bottom: solid 0.025rem #ccc;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.public_query {
  background: #ffffff;
  height: 100%;
  .public_query_title {
    padding-top: 5px;
    border-bottom: 1px solid #f2f2f2;
    .public_query_titleSrc {
      padding: 0 10px;
      height: 35px;
      @flex_a_bw();
      margin-top: 5px;
      .public_query_titleSrc_item {
        width: 48%;
        height: 100%;
        border: 1px solid #cccccc;
        border-radius: 4px;
        overflow: hidden;
        position: relative;
        vertical-align: top;
        // div {
        //   height: 100%;
        //   width: calc(100% - 40px);
        //   padding: 0 30px 0 10px;
        //   border: none;
        //   font-size: 15px;
        //   line-height: 35px;
        //   color: #333333;
        //   text-align: left;
        // }
        input {
          height: 100%;
          width: calc(100% - 45px);
          padding: 0 45px 0 5px;
          border: none;
          font-size: 15px;
          line-height: 35px;
          color: #333333;
          vertical-align: top;
        }
        span {
          position: absolute;
          right: 0;
          top: 0;
          bottom: 0;
          width: 45px;
          font-size: 16px;
          color: #000;
          @flex_a_j();
          background: #4c99e7;
        }
        .van-icon {
          position: absolute;
          right: 0;
          top: 0;
          bottom: 0;
          width: 30px;
          font-size: 22px;
          @flex_a_j();
          color: #666666;
        }
      }
    }
    .public_list_title {
      height: 40px;
      @flex_a_bw();
      margin-top: 5px;
      padding: 0 5px;
      div {
        height: 40px;
        line-height: 40px;
        font-size: 15px;
        text-align: center;
        width: calc(25% - 10px);
        padding: 0 5px;
        font-weight: 500;
        color: #333333;
      }
      div:first-child {
        width: calc(20% - 10px);
        text-align: left;
      }
      div:last-child {
        width: calc(25% - 10px);
        text-align: right;
      }
    }
  }
}
.sales_list_boxs {
  height: calc(100% - 88px);
  overflow-x: hidden;
  overflow-y: auto;
  background: #f2f2f2;
  width: 100%;
}
.sales_list_boxs_no {
  height: calc(100% - 54px);
  @flex_a_flex();
  background: #f2f2f2;
  color: #999999;
  .whole_box_no_icon {
    font-size: 50px;
  }
  p {
    font-size: 15px;
  }
}
.sales_ul {
  width: 100%;
  height: auto;
  overflow: hidden;
  //padding: 0 5px;
  //background: #ffffff;
  li {
    width: 100%;
    height: auto;
    overflow: hidden;
    //padding: 0px 10px;
    border-bottom: 1px solid #f2f2f2;
    display: flex;
    flex-direction: column;
    align-items: center;
    // .sales_ul_t {
    //   overflow: hidden;
    //   @flex_a_bw();
    //   height: auto;
    //   div {
    //     font-size: 15px;
    //   }
    //   .sales_ul_tl {
    //     color: #333333;
    //     width: 30%;
    //     text-align: left;
    //   }
    //   .sales_ul_tc {
    //     color: #333333;
    //     text-align: left;
    //     width: 43%;
    //   }
    //   .sales_ul_tr {
    //     font-size: 15px;
    //     color: #1989fa;
    //     width: 27%;
    //     text-align: right;
    //   }
    // }
    // .sales_ul_b {
    //   overflow: hidden;
    //   @flex_a_bw();
    //   height: auto;
    //   margin-top: 5px;
    //   .sales_ul_bl {
    //     font-size: 13px;
    //     color: #666666;
    //     width: 30%;
    //     min-height: 15px;
    //     text-align: left;
    //   }
    //   .sales_ul_bc {
    //     font-size: 13px;
    //     color: #666666;
    //     width: 43%;
    //     text-align: left;
    //   }
    //   .sales_ul_br {
    //     font-size: 13px;
    //     width: 27%;
    //     color: #666666;
    //     min-height: 30px;
    //     @flex_a_end();
    //     i {
    //       font-size: 22px;
    //       color: #1989fa;
    //       margin-left: 10px;
    //     }
    //   }
    // }
    .reject-btn {
      margin-left: 10px;
      display: flex;
      color: #fff;
      height: 100%;
      width: 100px;
      font-size: 18px;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      background: #ee0a24;
    }
  }
  // li:last-child {
  //   border-bottom: none;
  // }
}
.wrapper {
  position: fixed;
  left: 0px;
  bottom: 0px;
  width: 100%;
  height: 35px;
  font-size: 0.5em;
  color: #333;
  border-top: 1px solid #f2f2f2;
  box-shadow: 0 -2px 5px #f2f6fc;
  background-color: #fff;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  .content {
    padding-left: 15px;
  }
  .button_assign {
    padding-right: 15px;
    height: 35px;
    border-radius: 11px;
    margin-right: 5px;
    color: #333;
    background: #ffcccc;
  }
  .record {
    padding: 0 10px;
  }
}

.sheet_wrapper {
  width: 100%;
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  margin-bottom: 5px;
  padding: 6px;

  background-color: #fff;
  box-sizing: border-box;

  .sup_info {
    border-bottom: 1px solid #f2f2f2;
    margin-bottom: 2px;
    margin: 0 5px;
    padding-bottom: 4px;
    display: flex;
    .sup_name {
      flex: 3;
      display: flex;
      align-items: center;
      font-size: 18px;
      font-weight: bolder;
      text-align: left;
    }
    .order-source-wrapper {
      border: 1px solid #fde3e4;
      padding: 1px 5px;
      background-color: #fde3e4;
      border-radius: 10px;
      margin-left: 10px;
    }
    .sup_contact {
      flex: 2;
      font-size: 16px;
      display: flex;
      flex-direction: column;
      align-items: flex-end;

      .sup_tel {
        margin: 2px 0;
        a {
          color: rgb(12, 89, 190);
        }
      }
    }
  }
  .sheet_info {
    margin: 2px 5px;

    display: flex;
    .sheet_info_left {
      flex: 2;
      display: flex;
      padding: 4px 0;
      flex-direction: column;
      .sheet_no_tag {
        display: flex;
        justify-content: space-between;
        .sheet_no {
          font-size: 17px;
        }
        .sheet_type{
          font-size: 14px;
          color:#f88;
          border: 1px solid #f88;
          padding: 1px 5px;
          border-radius: 4px;
        }
      }
      .sheet_happentime {
        display: flex;
        color: #ccc;
      }
    }
    .sheet_tag {
      flex: 1;
      max-width: 35px;
      display: flex;
      align-items: center;
      justify-content: center;
      span {
        padding: 2px;
      }
    }
    .sheet_info_right {
      flex: 2;
      display: flex;
      font-family: numfont;
      font-size: 26px;
      justify-content: center;
      align-items: center;
      color: #c40000;
    }
    .sheet_checked {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .sheet_add {
    border-top: 1px solid #ddd;
    padding-top: 2px;
    width: 100%;
    color: #555;
    display: flex;
    padding-left: 5px;
  }
  .seller_senders_info {
    margin: 4px 5px 0;
    display: flex;
    justify-content: space-between;
    margin-right: 20px;
    color: #000;
  }
  .mark-brief {
    margin: 6px 5px 0;
    display: flex;
    color: #999;
  }
}
/* 批量审核按钮样式*/
.batch-approve-btn {
  background-color: #ff9999 !important;
  color: #fff !important;
  border: none !important;
  border-radius: 15px !important;
  height: 30px !important;
  padding: 0 16px !important;
  font-size: 14px !important;
  white-space: nowrap !important;
  margin-left: 8px !important;
}

.batch-approve-btn:active {
  background-color: #ff7777 !important;
}
</style>

