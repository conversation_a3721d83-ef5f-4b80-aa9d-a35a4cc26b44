export default {
 
  app_version: 10.767,
 

  //app_version:8.1,
  //6.26888
  wechatConf: {
    imgUrl: "https://yingjiang168.com/images/logo.svg",
    baseUrl: "https://www.yingjiang.co",
    miniAppId: "wx7b10b3a9f0a20b6b",
  },
  obs_server_uri: "https://yingjiang.obs.cn-east-3.myhuaweicloud.com",
  coolieUri: "",
  miniQrCodeUri: "https://s3.yingjiang.co",
  tianDiTuToken: "8a9cdbccf3fd24290c65b7e04200dbf6",
  mobileManufactor: "",
  getUnitQtyFromSheetRow(row) {
    let b_qty = 0,
      m_qty = 0,
      s_qty = 0
    let leftQty = row.quantity * row.unit_factor
    let unitsQty = ""
    let absLeftQty = Math.abs(leftQty)
    let flag = leftQty < 0 ? -1 : 1

    if (row.b_unit_factor) {
      b_qty = parseInt(absLeftQty / row.b_unit_factor)
      absLeftQty = absLeftQty % row.b_unit_factor 
      if (b_qty < 0.001) {
        b_qty = 0
      }

      if (b_qty > 0) {
        b_qty *= flag
        unitsQty += toMoney(b_qty) + row.b_unit_no
      }
    }
    if (row.m_unit_factor) {
      m_qty = parseInt(absLeftQty / row.m_unit_factor)
      absLeftQty = absLeftQty % row.m_unit_factor
      if (m_qty < 0.001) {
        m_qty = 0
      }
      if (m_qty > 0) {
        m_qty *= flag
        unitsQty += toMoney(m_qty) + row.m_unit_no
      }
    }
    s_qty = absLeftQty
    if (s_qty < 0.001) {
      s_qty = 0
    }
    if (s_qty > 0) {
      s_qty *= flag
      unitsQty += toMoney(s_qty) + row.s_unit_no
    }
    return unitsQty
  },
  getQtyUnit(sQuantity,s_unit_no,b_unit_no,b_unit_factor,m_unit_no,m_unit_factor) {
    let b_qty = 0,
      m_qty = 0,
      s_qty = 0
    let leftQty = sQuantity
    let unitsQty = ""
    let absLeftQty = Math.abs(leftQty)
    let flag = leftQty < 0 ? -1 : 1

    if (b_unit_factor) {
      var m_unit_factor = Number(m_unit_factor||1)
      b_qty = parseInt(absLeftQty / Number(b_unit_factor) / m_unit_factor)
      absLeftQty = absLeftQty % (b_unit_factor*m_unit_factor)
      if (b_qty < 0.001) {
        b_qty = 0
      }

      if (b_qty > 0) {
        b_qty *= flag
        unitsQty += toMoney(b_qty) + b_unit_no
      }
    }
    if (m_unit_factor) {
      m_qty = parseInt(absLeftQty / m_unit_factor)
      absLeftQty = absLeftQty % m_unit_factor
      if (m_qty < 0.001) {
        m_qty = 0
      }
      if (m_qty > 0) {
        m_qty *= flag
        unitsQty += toMoney(m_qty) + m_unit_no
      }
    }
    s_qty = absLeftQty
    if (s_qty < 0.001) {
      s_qty = 0
    }
    if (s_qty > 0) {
      s_qty *= flag
      unitsQty += toMoney(s_qty) + s_unit_no
    }
    return {qtyUnit: unitsQty,s_qty:s_qty,m_qty:m_qty,b_qty:b_qty}
  },
  getUnitQtyNumFromSheetRow(row) {
 
    let b_qty = 0,
      m_qty = 0,
      s_qty = 0
    let leftQty = row.quantity * row.unit_factor
    let absLeftQty = Math.abs(leftQty)
    let flag = leftQty < 0 ? -1 : 1

    if (row.b_unit_factor) {
      b_qty = parseInt(absLeftQty / Number(row.b_unit_factor) )
      absLeftQty = (b_qty==0?absLeftQty:absLeftQty % (b_qty*Number(row.b_unit_factor)))
      if (b_qty < 0.001) {
        b_qty = 0
      }

      if (b_qty > 0) {
        b_qty *= flag
      }
    }
    if (row.m_unit_factor) {
      m_qty = parseInt(absLeftQty / Number(row.m_unit_factor))
      absLeftQty = (m_qty==0?absLeftQty:absLeftQty % (m_qty*Number(row.m_unit_factor)))
      if (m_qty < 0.001) {
        m_qty = 0
      }
      if (m_qty > 0) {
        m_qty *= flag
      }
    }
    s_qty = absLeftQty
    if (s_qty < 0.001) {
      s_qty = 0
    }
    if (s_qty > 0) {
      s_qty *= flag
    }
    console.log(s_qty)
    return {
      m_qty,b_qty,s_qty
    }
  }
}
