<template>
  <div class="wrapper" ref="wrapper">

    <div id="nar_bar_small" class="small">
      <van-col span="3">
        <div class="navbar_img">
          <div class="headImg">
            <img :src="logoUrl" class="smallLogoImg">
          </div>
        </div>
      </van-col>
      <van-col span="21" class="navbar_title">
        <div class="navbar_title_son">
          <div class="navbar_title_son_info">
            <div>
              <div class="navbar_title_son_operName">{{ operName }}</div>
            </div>
          </div>
          <svg class="navbar_title_son_ico" style="margin-right: 25px;">
            <use :xlink:href="'#icon-' + this.weatherIconCode"></use>
          </svg>

        </div>
      </van-col>
    </div>

    <van-pull-refresh v-if="!net_error" class="pull_refresh" v-model="isRefresh" success-text="刷新成功" :disabled="refreshDisabled" @refresh="onPullRefresh" ref="pullRefresh" @touchend.native="settingOpacity()">

      <!-- <div id="nar_bar_main" class="nav_bar_home black-back"  ref="nav_bar_home"> -->
      <div id="nar_bar_main" class="nav_bar_home black-back" ref="nav_bar_home">
        <div class="navbar_img">
          <div class="headImg">
            <img :src="logoUrl" class="mainLogoImg">
          </div>
        </div>
        <div class="navbar_title_info_wrapper">
          <div class="navbar_title">
            <div class="navbar_title_son">
              <div class="navbar_title_son_company">{{ companyName }}</div>
              <div class="navbar_title_son_operName">{{ operName }}
                <span v-if="this.$store.state.releaseType == 'test'" style="padding:0 5px 0 2px;" class="navbar_title_son_test">测</span>
              </div>
            </div>
          </div>

          <div class="navbar_weather">
            <div class="navbar_weather_temp" @click="loadWeather">
              <div>{{ numFormatBank(this.weatherInfo.now.temp, 0) }}</div>
              <div class="navbar_weather_temp_centigrade">°C</div>
              <div class="navbar_weather_temp_info">{{ ellipsis(this.weatherInfo.now.text, 4) }}</div>
            </div>
            <div class="navbar_weather_content">
              <div class="navbar_weather_icon">
                <svg>
                  <use :xlink:href="'#icon-' + this.weatherIconCode"></use>
                </svg>
              </div>
              <div class="navbar_weather_name"> {{ this.weatherInfo.location.name }}</div>
            </div>
          </div>

        </div>
      </div>
      <div class="noticeStr" v-if="expireMsg">{{ expireMsg }}</div>
      <div class="today_sale_wrapper"></div>

      <!-- <div class="work_bench_title">常用功能</div> -->
      <!-- <div class="line"></div -->
      <div style="height:1px"></div>
      <SlickList
          v-if="!loginErrMsg"
          class="slickList-wrapper"
          style="margin:0px 0px 10px 0px;display:flex; flex-wrap:wrap;"
          
          :lockToContainerEdges="true"
          axis="xy"
          lockAxis="xy"
          v-model="layout"
          @input="getChangeLists"
          :pressDelay=pressDelay
          @sort-start="sortStart"
          @sort-end="sortEnd">
        <SlickItem v-for="(item, index) in layout" :class="handleSlickItemClass(item)" :index="index" :key="item.key" @click="handleSlickItemClick(item)">

          <template v-if="item.key == 'cardTodaySale'">
            <div class="sale_left_icon"><img src="../assets/icons/png/iconSale.png"></div>
            <div class="sale_left_salesum">{{ numFormatBank(saleTrend.salesum, 2) }}</div>
            <div class="sale_left_amount" style="padding-left: 10px;">
              <div class="sale_left_amount_num">销 {{ numFormatBank(saleTrend.x_amount, 2) }}
                <div style="width: 10px">
                </div>退
                {{ numFormatBank(saleTrend.t_amount, 2) }}
              </div>
            </div>
          </template>

          <template v-else-if="item.key == 'cardTodayVisit' && canSeeVisitRecord">
            <div class="sale_right_icon" @click="onGoRouter('/VisitRecord')"><img src="../assets/icons/png/iconTotal.png">
            </div>
            <div class="sale_right_salesum" @click="onGoRouter('/VisitRecord')"><span>访</span>{{ todayInfo.visitCount }}
            </div>
            <div class="sale_right_salesum_oper" @click="onGoRouter('/VisitRecord')">
              <div class="sale_right_salesum_opername">
                <div class="salesum_title">新增客户 {{ todayInfo.createSupcustCount }}</div>
                <!-- <div><img src="../assets/icons/png/iconRight.png"></div> -->
              </div>
              <!-- <div class="sale_right_salesum_link"><span>→</span></div> -->
            </div>
          </template>

          <template v-else-if="item.key == 'chartSaleTrend' && canSeeSalesTrend">
            <div class="saleTrend_chart" @click="onGoRouter('/CustomerRanking?types=salesTrend&names=销量走势图')">
              <v-charts class="saleTrend_chart_view" :catrsDatas="trendData" />
            </div>
          </template>

          <template v-else-if="item.key !== 'cardTodaySale' &&
            item.key !== 'cardTodayVisit' &&
            item.key !== 'chartSaleTrend'">
            <div class="icon"  @click="goUrl(item.goUrl)" >
              <template v-if="item.key && countsResult[item.key] != '0'">
                <van-badge :content="countsResult[item.key]" >
                  <svg stroke-width="1.3" :class="item.iconColor">
                    <use :xlink:href="item.iconSrc"></use>
                  </svg>
                </van-badge>
              </template>
              <template v-else>
                <svg stroke-width="1.3" :class="item.iconColor">
                  <use :xlink:href="item.iconSrc"></use>
                </svg>
              </template>
            </div>
            <div class="module_title" @click="goUrl(item.goUrl)">{{ item.title }}</div>
          </template>
        </SlickItem>
        <div class="moudle add_moudle_wrapper" @click="handleEdit()">
          <div class="add_moudle">
            <svg stroke-width="1" style="margin-top: 10px;" fill="#C0C4CC">
              <use xlink:href="#icon-add"></use>
            </svg>
          </div>
        </div>
      </SlickList>
      <div v-else class="error_msg">
        <div>{{ loginErrMsg }}</div>
      </div>
      <div style="height:50px "></div>
    </van-pull-refresh>
    <div v-else class="net_error">
      <img src="../assets/net_error.png" class="net_error_p" />
      <van-button plain round type="info" class="net_error_b" @click="myGetOperRight(0)">点击重试</van-button>
    </div>
  </div>
</template>

<script>
import { NavBar, Col, Button, Toast, PullRefresh, List, Icon, Badge, Dialog } from 'vant'
import axios from 'axios'
import WeatherDistrictIdJson from '../components/weather_district_id.json'
import charts from "./components/Chart"
// import VueGridLayout from "vue-grid-layout"
import {
  SellerGetVisitorCountAndCreateCustomerCount,
  GetSellerRank,
  GetTrends,
  BossGetVisitorCountAndCreateCustomerCount,
  GetWeatherCondition,
  ApiReportLog,
  GetDesktop,
  SaveDesktop,
  //GetPersonalSetting,
  QueryDesktopData,
  DepartManagerGetVisitorCountAndCreateCustomerCount
} from "../api/api"
import store from '../store/store'
import { SlickList, SlickItem } from 'vue-slicksort'
import globalVars from '../static/global-vars'
import wholeJson from "../components/whole.json";
import FONT_SIZE from "../util/fontSize";
import MQTT from './service/MQTT'

export default {
  name: "Workbench",
  data() {
    return {
      itemDatas: [],
      layout: [],
      isShow: false,
      logoUrl:'logo.svg',
      chartStyle: [8, 6],
      overviewStyle: [8, 3],
      btnStyle: [4, 3],
      trendData: {},
      startRow: 0,
      weatherInfo: { location: { name: '' }, now: { name: '', temp: '' } },
      weatherIconCode: "",
      weatherCurrentCity: "",
      todayInfo: {
        visitCount: '0',
        createSupcustCount: '0'
      },
      pageSize: 30,
      dateType: "month",
      companyName: '',
      operName: '',
      expired: '',
      expireMsg: '',
      loginErrMsg: '',
      net_error: false,
      saleTrend: {
        salesum: 0,
        profit: 0,
        x_amount: 0,
        t_amount: 0
      },
      sellerTrend: {
        oper_name: '',
        salesum: 0
      },
      isRefresh: false,
      refreshDisabled: false,
      show: false,
      scroll: 0,
      scrollTopIndex: '',
      pressDelay: 700,
      appORpcFlag: "app",
      navattr: [ // 默认
        { key: 'cardTodaySale', title: "今日销售", "right": "", goUrl: "", class: "today_sale_left", iconColor: "black" },
        { key: 'cardTodayVisit', title: "拜访信息", "right": "report.visitRecord.see", goUrl: "", class: "today_sale_right", iconColor: "black" },
        { key: 'chartSaleTrend', title: "销量走势图", "right": "report.salesTrend.see", goUrl: "", class: "saleTrend", iconColor: "black" },
        { key: 'viewSaleSheets', title: "销售查单", iconSrc: "#icon-viewSheets", "right": "sale.sheetSale.make|sale.sheetReturn.make|stock.sheetMove.make", goUrl: "/ViewSheetAll?keys=sheetSale,sheetReturn,sheetMove", iconColor: "black" },
        { key: 'saleSheet', title: "销售单", iconSrc: "#icon-saleSheet", "right": "sale.sheetSale.make", goUrl: "/SaleSheet?sheetType=X", iconColor: "black" },
        { key: 'returnSheet', title: "退货单", iconSrc: "#icon-returnSheet", "right": "sale.sheetReturn.make", goUrl: "/SaleSheet?sheetType=T", iconColor: "black" },
        { key: 'buySheet', title: "采购单", iconSrc: "#icon-buySheet", "right": "buy.sheetBuy.make", goUrl: "/SaleSheet?sheetType=CG", iconColor: "black" },
        { key: 'saleOrder', title: "销售订单", iconSrc: "#icon-saleOrder", "right": "sale.sheetSaleOrder.make", goUrl: "/SaleSheet?sheetType=XD", iconColor: "black" },
        { key: 'moveOutSheet', title: "装车调拨", iconSrc: "#icon-moveOutSheet", "right": "stock.sheetMove.make", goUrl: "/MoveSheet?moveType=moveOut", iconColor: "black" },
        { key: 'moveInSheet', title: "回库调拨", iconSrc: "#icon-moveInSheet", "right": "stock.sheetMove.make", goUrl: "/MoveSheet?moveType=moveIn", iconColor: "black" },

        { key: 'arrearsSheet', title: "收款", iconSrc: "#icon-arrearsSheet", "right": "money.sheetGetArrears.make", goUrl: "/GetAndPayArrearsSheet?sheetType=SK", iconColor: "black" },
        { key: 'feeout', title: "费用支出", iconSrc: "#icon-feeout", "right": "money.sheetFeeOut.make", goUrl: "/FeeOut", iconColor: "black" },
        { key: 'stock', title: "库存查询", iconSrc: "#icon-stock", "right": "report.viewStock.see", goUrl: "/Stock", iconColor: "black" },
        { key: 'checkAccount', title: "交账单", iconSrc: "#icon-checkAccount", "right": "sale.sheetCheckSheets.make", goUrl: "/CheckAccount", iconColor: "black" },
        { key: 'visit', title: "拜访门店", iconSrc: "#icon-visit", "right": "sale.sheetVisit.see", goUrl: "/VisitUser", iconColor: "black" },
        { key: 'visitRecord', title: "访店记录", iconSrc: "#icon-visitRecord", "right": "report.visitRecord.see", goUrl: "/VisitRecord", iconColor: "black" },
        { key: 'journey', title: "足迹", iconSrc: "#icon-journey", "right": "report.visitPath.see", goUrl: "/journey", iconColor: "black" },
        { key: 'sellerRank', title: "销售排行", iconSrc: "#icon-sellerRank", "right": "report.salesSummaryBySeller.see", goUrl: "/CustomerRanking?types=salesmanTrend&names=业务销售排行", iconColor: "black" },
        { key: 'customerArchives', title: "客户", iconSrc: "#icon-customerArchives", "right": "info.infoClient.see", goUrl: "/CustomerArchives", iconColor: "black" },
      ],
      regularNavattr: [
        { key: 'cardTodaySale', title: "今日销售", "right": "", goUrl: "", class: "today_sale_left", iconColor: "black" },
        { key: 'cardTodayVisit', title: "拜访信息", "right": "report.visitRecord.see", goUrl: "", class: "today_sale_right", iconColor: "black" },
        { key: 'chartSaleTrend', title: "销量走势图", "right": "report.salesTrend.see", goUrl: "", class: "saleTrend", iconColor: "black" }
      ],
      checkRemoveDomClassName: ['.moudle', '.today_sale_left', '.today_sale_right', '.saleTrend'],
      allnavattr: [ //全部
        // { key: 'cardTodaySale', title: "今日销售", "right": "", goUrl: "", class: "today_sale_left", iconColor: "black" },
        // { key: 'cardTodayVisit', title: "拜访信息", "right": "report.visitRecord.see", goUrl: "", class: "today_sale_right", iconColor: "black" },
        // { key: 'chartSaleTrend', title: "销量走势图", "right": "report.salesTrend.see", goUrl: "", class: "saleTrend", iconColor: "black" },
        // { key: 'viewSheets', title: "查单", icons: "#icon-viewSheets", goUrl: "/ViewSheetAll", iconColor: "black" },
        // { key: 'viewSheets_old', title: "查单(旧)", icons: "#icon-viewSheets", goUrl: "/ViewSheets", iconColor: "black" },
        // { key: 'saleSheet', title: "销售单", icons: "#icon-saleSheet", "right": "sale.sheetSale.make", goUrl: "/SaleSheet?sheetType=X", iconColor: "black" },
        // { key: 'saleOrder', title: "销售订单", icons: "#icon-saleOrder", "right": "sale.sheetSaleOrder.make", goUrl: "/SaleSheet?sheetType=XD", iconColor: "black" },
        // { key: 'returnSheet', title: "退货单", icons: "#icon-returnSheet", "right": "sale.sheetReturn.make", goUrl: "/SaleSheet?sheetType=T", iconColor: "black" },
        // { key: 'returnOrder', title: "退货订单", icons: "#icon-returnOrder", "right": "sale.sheetReturnOrder.make", goUrl: "/SaleSheet?sheetType=TD", iconColor: "black" },
        // { key: 'moveOutSheet', title: "装车调拨", icons: "#icon-moveOutSheet", "right": "stock.sheetMove.make", goUrl: "/MoveSheet?moveType=moveOut", iconColor: "black" },
        // { key: 'moveInSheet', title: "回库调拨", icons: "#icon-moveInSheet", "right": "stock.sheetMove.make", goUrl: "/MoveSheet?moveType=moveIn", iconColor: "black" },
        // { key: 'moveOutInSheet', title: "调拨单", icons: "#icon-moveNormalSheet", "right": "stock.sheetMove.make", goUrl: "/MoveSheet", iconColor: "black" },
        // { key: 'inventorySheet', title: "盘点单", icons: "#icon-inventory", "right": "stock.sheetInvent.see", goUrl: "/InventorySheet", iconColor: "black" },
        // { key: 'inventReduceSheet', title: "报损单", icons: "#icon-InventoryChange", "right": "stock.sheetInventReduce.see", goUrl: "/InventoryChangeSheet", iconColor: "black" },
        // { key: 'arrearsSheet', title: "收款单", icons: "#icon-arrearsSheet", "right": "money.sheetGetArrears.make", goUrl: "/GetArrearsSheet", iconColor: "black" },
        // { key: 'prepaySheet', title: "预收款单", icons: "#icon-pregetSheet", "right": "money.sheetPreget.make", goUrl: "/PrepaySheet", iconColor: "black" },
        // { key: 'prepaySheetPay', title: "预付款单", icons: "#icon-prepaySheet", "right": "money.sheetPrepay.make", goUrl: "/PrepaySheet?sheetType=YF", iconColor: "black" },
        // { key: 'buySheet', title: "采购单", icons: "#icon-buySheet", "right": "buy.sheetBuy.make", goUrl: "/SaleSheet?sheetType=CG", iconColor: "black" },
        // { key: 'buyReturnSheet', title: "采购退货", icons: "#icon-buyReturnSheet", "right": "buy.sheetBuyReturn.make", goUrl: "/SaleSheet?sheetType=CT", iconColor: "black" },
        // { key: 'feeout', title: "费用支出", icons: "#icon-feeout", "right": "money.sheetFeeOut.make", goUrl: "/FeeOut", iconColor: "black" },
        // { key: 'feein', title: "其他收入", icons: "#icon-feein", "right": "money.sheetFeeIn.make", goUrl: "/OtherInCome", iconColor: "black" },
        // { key: 'orderItemSheet', title: "定货会", icons: "#icon-orderItemSheet", "right": "sale.orderItemSheet.make", goUrl: "/SaleSheet?sheetType=DH", iconColor: "black" },
        // { key: 'deliveryReceipt', title: "送货签收", icons: "#icon-deliveryReceipt", "right": "orderFlow.orderToSale.make|orderFlow.orderToSale.approve|sale.saleFromOrder.make", goUrl: "/DeliveryReceipt", iconColor: "black" },

        // { key: 'saleTrend', title: "销量走势", icons: "#icon-saleTrend", "right": "report.salesTrend.see", goUrl: "/CustomerRanking?types=salesTrend&names=销量走势图", iconColor: "black" },
        // { key: 'clientRank', title: "客户排行", icons: "#icon-clientRank", "right": "report.salesSummaryByClient.see", goUrl: "/CustomerRanking?types=customerRanking&names=客户排行榜", iconColor: "black" },
        // { key: 'sellerRank', title: "销售排行", icons: "#icon-sellerRank", "right": "report.salesSummaryBySeller.see", goUrl: "/CustomerRanking?types=salesmanTrend&names=业务销售排行", iconColor: "black" },
        // { key: 'boomProduct', title: "热销商品", icons: "#icon-boomProduct", "right": "report.salesSummaryByItem.see", goUrl: "/CustomerRanking?types=productSalesSum&names=热销商品排行", iconColor: "black" },
        // { key: 'brandAmount', title: "品牌汇总", icons: "#icon-brandAmount", "right": "report.salesSummaryByBrand.see", goUrl: "/CustomerRanking?types=brandSalesSum&names=品牌销量汇总", iconColor: "black" },
        // { key: 'stock', title: "库存查询", icons: "#icon-stock", "right": "report.viewStock.see", goUrl: "/Stock", iconColor: "black" },
        // { key: 'checkAccount', title: "交账单", icons: "#icon-checkAccount", "right": "sale.sheetCheckSheets.see", goUrl: "/CheckAccount", iconColor: "black" },
        // { key: 'checkAcreceivescount', title: "应收款", icons: "#icon-receives", "right": "report.arrearsBlance.see", goUrl: "/Receivables", iconColor: "black" },
        // { key: 'journey', title: "外勤轨迹", icons: "#icon-journey", "right": "report.visitPath.see", goUrl: "/journey", iconColor: "black" },
        // { key: 'moveSummary', title: "调拨汇总", icons: "#icon-moveSummary", "right": "report.moveSummary.see", goUrl: "/MoveSummary", iconColor: "black" },
        // { key: 'prepayBalance', title: "预收款余额", icons: "#icon-prepayBalance", "right": "report.pregetBalance.see", goUrl: "/PrepayBalance", iconColor: "black" },
        // { key: 'visitRecord', title: "拜访记录", icons: "#icon-visitRecord", "right": "report.visitRecord.see", goUrl: "/VisitRecord", iconColor: "black" },
        // { key: 'itemArchives', title: "商品档案", icons: "#icon-itemArchives", "right": "info.infoItem.see", goUrl: "/GoodsArchives", iconColor: "black" },
        // { key: 'customerArchives', title: "客户档案", icons: "#icon-customerArchives", "right": "info.infoClient.see", goUrl: "/CustomerArchives", iconColor: "black" },
        // { key: 'visit', title: "拜访门店", icons: "#icon-visit", "right": "sale.sheetVisit.see", goUrl: "/VisitUser", iconColor: "black" },
        // { key: 'displayAgreement', title: "陈列协议", icons: "#icon-displayAgreement", "right": "sale.sheetDisplayAgreement.see", goUrl: "/displayAgreementSheet", iconColor: "black" },
        // { key: 'clientRador', title: "新客雷达", icons: "#icon-visit", "right": "sale.sheetVisit.see", goUrl: "/SupcustRadar", iconColor: "black" },
        // { key: 'clientLayout', title: "客户分布", icons: "#icon-visit", "right": "report.supcustDistribution.see", goUrl: "/SupcustDistribution", iconColor: "black" },

      ],
      /* navattr : [ // 默认
      //   {key:'cardTodaySale',title:"今日销售","right":"",goUrl:"",class:"today_sale_left",iconColor:"black"},
      //   {key:'cardTodayVisit',title:"拜访信息","right":"report.visitRecord.see",goUrl:"",class:"today_sale_right",iconColor:"black"},
      //   {key:'chartSaleTrend',title:"销量走势图","right":"report.salesTrend.see",goUrl:"",class:"saleTrend",iconColor:"black"},
      //   {key:'saleSheet',title:"销售",icons:"#icon-saleSheet", "right":"sale.sheetSale.make",goUrl:"/SaleSheet?sheetType=X",iconColor:"black"},
      //   {key:'returnSheet',title:"退货",icons:"#icon-returnSheet", "right":"sale.sheetReturn.make",goUrl:"/SaleSheet?sheetType=T",iconColor:"black"},
      //   {key:'saleSheetBuy',title:"采购",icons:"#icon-saleSheetBuy","right":"buy.sheetBuy.make",goUrl:"/SaleSheet?sheetType=CG",iconColor:"black"},
      //   {key:'saleOrder',title:"订单",icons:"#icon-saleOrder","right":"sale.sheetSaleOrder.make",goUrl:"/SaleSheet?sheetType=XD",iconColor:"black"},
      //   {key:'moveOutSheet',title:"装车",icons:"#icon-moveOutSheet", "right":"stock.sheetMove.make",goUrl:"/MoveSheet?moveType=moveOut",iconColor:"black"},
      //   {key:'moveInSheet',title:"回库",icons:"#icon-moveInSheet", "right":"stock.sheetMove.make",goUrl:"/MoveSheet?moveType=moveIn",iconColor:"black"},
      //   {key:'arrearsSheet',title:"收款",icons:"#icon-arrearsSheet","right":"money.sheetGetArrears.make",goUrl:"/GetAndPayArrearsSheet?sheetType=SK",iconColor:"black"},
      //   {key:'feeout',title:"支出",icons:"#icon-feeout","right":"money.sheetFeeOut.make",  goUrl:"/FeeOut",iconColor:"black"},
      //   {key:'viewSheets',title:"查单",icons:"#icon-viewSheets",goUrl:"/ViewSheets",iconColor:"black"},
      //   {key:'stock',title:"库存",icons:"#icon-stock", "right":"report.viewStock.see",goUrl:"/Stock",iconColor:"black"},
      //   {key:'checkAccount',title:"交账",icons:"#icon-checkAccount", "right":"sale.sheetCheckSheets.see",goUrl:"/CheckAccount",iconColor:"black"},
      //   {key:'visit',title:"访店",icons:"#icon-visit","right":"sale.sheetVisit.see",goUrl:"/VisitUser",iconColor:"black"},
      //   {key:'visitRecord',title:"访店史",icons:"#icon-visitRecord","right":"report.visitRecord.see",goUrl:"/VisitRecord",iconColor:"black"},
      //   {key:'journey',title:"行迹",icons:"#icon-journey", "right":"report.visitPath.see",  goUrl:"/journey",iconColor:"black"},
      //   {key:'sellerRank',title:"业务榜",icons:"#icon-sellerRank", "right":"report.salesSummaryBySeller.see",  goUrl:"/CustomerRanking?types=salesmanTrend&names=业务销售排行",iconColor:"black"},
      //   {key:'customerArchives',title:"客户榜",icons:"#icon-customerArchives", "right":"info.infoClient.see",  goUrl:"/CustomerArchives",iconColor:"black"}
      // ],
      // allnavattr : [ //全部
      //       {key:'cardTodaySale',title:"今日销售","right":"",goUrl:"",class:"today_sale_left",iconColor:"black"},
      //       {key:'cardTodayVisit',title:"拜访信息","right":"report.visitRecord.see",goUrl:"",class:"today_sale_right",iconColor:"black"},
      //       {key:'chartSaleTrend',title:"销量走势图","right":"",goUrl:"",class:"saleTrend",iconColor:"black"},

      //       {key:'saleSheet',title:"销售",icons:"#icon-saleSheet", "right":"sale.sheetSale.make",goUrl:"/SaleSheet?sheetType=X",iconColor:"black"},
      //       {key:'saleOrder',title:"订单",icons:"#icon-saleOrder","right":"sale.sheetSaleOrder.make",goUrl:"/SaleSheet?sheetType=XD",iconColor:"black"},
      //       {key:'returnSheet',title:"退货",icons:"#icon-returnSheet", "right":"sale.sheetReturn.make",goUrl:"/SaleSheet?sheetType=T",iconColor:"black"},
      //       {key:'returnOrder',title:"退订",icons:"#icon-returnSheet", "right":"sale.sheetReturnOrder.make",goUrl:"/SaleSheet?sheetType=TD",iconColor:"black"},
      //       {key:'moveOutSheet',title:"装车",icons:"#icon-moveOutSheet", "right":"stock.sheetMove.make",goUrl:"/MoveSheet?moveType=moveOut",iconColor:"black"},
      //       {key:'moveInSheet',title:"回库",icons:"#icon-moveInSheet", "right":"stock.sheetMove.make",goUrl:"/MoveSheet?moveType=moveIn",iconColor:"black"},
      //       {key:'arrearsSheet',title:"收款",icons:"#icon-arrearsSheet","right":"money.sheetGetArrears.make",goUrl:"/GetAndPayArrearsSheet?sheetType=SK",iconColor:"black"},
      //       {key:'prepaySheet',title:"预收",icons:"#icon-prepaySheet","right":"money.sheetPreget.make",goUrl:"/PrepaySheet",iconColor:"black"},
      //       {key:'saleSheetBuy',title:"采购",icons:"#icon-saleSheetBuy","right":"buy.sheetBuy.make",goUrl:"/SaleSheet?sheetType=CG",iconColor:"black"},
      //       {key:'sheetBuyReturn',title:"采退",icons:"#icon-saleSheetBuy","right":"buy.sheetBuyReturn.make",goUrl:"/SaleSheet?sheetType=CT",iconColor:"black"},
      //       {key:'feeout',title:"支出",icons:"#icon-feeout","right":"money.sheetFeeOut.make",  goUrl:"/FeeOut",iconColor:"black"},
      //       {key:'viewSheets',title:"查单",icons:"#icon-viewSheets",goUrl:"/ViewSheets",iconColor:"black"},
      //       {key:'orderItemSheet',title:"定货",icons:"#icon-orderItemSheet","right":"sale.orderItemSheet.make",goUrl:"/SaleSheet?sheetType=DH",iconColor:"black"},
      //       {key:'deliveryReceipt',title:"签收",icons:"#icon-deliveryReceipt","right":"sale.orderManage.see",goUrl:"/DeliveryReceipt",iconColor:"black"},


      //       {key:'saleTrend',title:"销势",icons:"#icon-saleTrend", "right":"report.salesTrend.see", goUrl:"/CustomerRanking?types=salesTrend&names=销量走势图",iconColor:"black"},
      //       {key:'clientRank',title:"客户榜",icons:"#icon-clientRank","right":"report.salesSummaryByClient.see",goUrl:"/CustomerRanking?types=customerRanking&names=客户排行榜",iconColor:"black"},
      //       {key:'sellerRank',title:"业务榜",icons:"#icon-sellerRank", "right":"report.salesSummaryBySeller.see",goUrl:"/CustomerRanking?types=salesmanTrend&names=业务销售排行",iconColor:"black"},
      //       {key:'boomProduct',title:"热销榜",icons:"#icon-saleSheet", "right":"report.salesSummaryByItem.see",goUrl:"/CustomerRanking?types=productSalesSum&names=热销商品排行",iconColor:"black"},
      //       {key:'brandAmount',title:"品牌榜",icons:"#icon-brandAmount", "right":"report.salesSummaryByBrand.see",goUrl:"/CustomerRanking?types=brandSalesSum&names=品牌销量汇总",iconColor:"black"},
      //       {key:'stock',title:"库存",icons:"#icon-stock", "right":"report.viewStock.see",goUrl:"/Stock",iconColor:"black"},
      //       {key:'checkAccount',title:"交账",icons:"#icon-checkAccount", "right":"sale.sheetCheckSheets.see",goUrl:"/CheckAccount",iconColor:"black"},
      //       {key:'checkAcreceivescount',title:"应收",icons:"#icon-receives", "right":"report.arrearsBlance.see",goUrl:"/Receivables",iconColor:"black"},
      //       {key:'journey',title:"行迹",icons:"#icon-journey", "right":"report.visitPath.see",  goUrl:"/journey",iconColor:"black"},
      //       {key:'moveSummary',title:"调拨汇总",icons:"#icon-moveSummary", "right":"report.moveSummary.see",  goUrl:"/MoveSummary",iconColor:"black"},
      //       {key:'prepayBalance',title:"预收余额",icons:"#icon-prepayBalance", "right":"report.pregetBalance.see",  goUrl:"/PrepayBalance",iconColor:"black"},
      //       {key:'visitRecord',title:"访店史",icons:"#icon-visitRecord","right":"report.visitRecord.see",goUrl:"/VisitRecord",iconColor:"black"},

      //       {key:'itemArchives',title:"商品",icons:"#icon-itemArchives", "right":"info.infoItem.see",  goUrl:"/GoodsArchives",iconColor:"black"},
      //       {key:'customerArchives',title:"客户",icons:"#icon-customerArchives", "right":"info.infoClient.see",  goUrl:"/CustomerArchives",iconColor:"black"},

      //       {key:'visit',title:"访店",icons:"#icon-visit","right":"sale.sheetVisit.see",goUrl:"/VisitUser",iconColor:"black"}
      //       ]*/
      paramsCounts: {},
      countsResult: {},
      departID: "",
      startDate: "",
      endDate: ""
    }
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      //因为当钩子执行前，组件实例还没被创建
      // vm 就是当前组件的实例相当于上面的 this，所以在 next 方法里你就可以把 vm 当 this 来用了。
      if (from.name == "WorkbenchEdit") {
        var returnObj = vm.$route.query.returnObj;
        ;
        if (returnObj) {
          if (returnObj.isReturn) { //还原布局
            vm.layout = JSON.parse(JSON.stringify(vm.navattr));
          } else {
            console.log("this页面", returnObj.selectNavattr)
            returnObj.selectNavattr.forEach((item) => {
              if (item.key === "viewSaleSheets") {
                item.title = "销售查单"
              } else if (item.key === "viewStockSheets") {
                item.title = "仓库查单"
              } else if (item.key === "viewBuySheets") {
                item.title = "采购查单"
              } else if (item.key === "viewFundSheets") {
                item.title = "资金查单"
              } else if (item.key === "viewActiveSheets") {
                item.title = "营销查单"
              }
            })
            vm.layout = returnObj.selectNavattr;
          }
          vm.$forceUpdate()
          vm.saveAppDesktop();
        }
      }
      // if(from.name == "Login" && to.name == 'Workbench') {
      //   vm.getAppDesktop();
      // }
    });
  },
  async mounted() {
    if (!store.state.operKey) {
      this.$router.push({ path: '/Login' })
      return
    }
    if (window.device) {
      globalVars.mobileManufactor = window.device.manufacturer
    }


    // this.inithandleScroll()
    //    this.getAppDesktop();

    //   this.myGetOperRight()
    //window.addEventListener('scroll', this.handleScroll, true)
    if(this.$route.query.fromLogin){
      this.onPullRefresh()
    }

    // 调试：确认 onBackToThisPage 方法已正确绑定
    console.log('Workbench mounted, onBackToThisPage 方法类型:', typeof this.onBackToThisPage);

  },
  activated() {
    if (!store.state.operKey) {
      return
    }

    if (window.g_operInfoRefreshed) {
      // 如果操作权限已经刷新过，只刷新数字计数
      this.handleGetShowCounts();
      return
    }

    this.show = false
    window.g_debugInfo += 'A'
    setTimeout(() => {
      this.myGetOperRight(0)
      this.inithandleScroll()
      window.addEventListener('scroll', this.handleScroll, true)

    }, 100)
  },
  components: {
    "van-nav-bar": NavBar,
    "van-col": Col,
    "v-charts": charts,
    "van-button": Button,
    "van-pull-refresh": PullRefresh,
    "van-list": List,
    "van-icon": Icon,
    "van-badge": Badge,
    SlickItem,
    SlickList
  },
  computed: {
    tabIndex() { 
      return 0
    },
    seeInPrice() {
      return hasRight('delicacy.seeInPrice.value')
    },
    /*canSeeSalesSummaryBySeller(){
      return hasRight('report.salesSummaryBySeller.see')
    },*/
    canSeeVisitRecord() {
      return hasRight('report.visitRecord.see')
    },
    canSeeSalesTrend() {
      return hasRight('report.salesTrend.see')
    }

  },
  watch: {
    '$route'(to, from) {
      console.log(`Listened route change from ${from.name} to ${to.name}`);
      if (to.name=='Workbench') {
        // 从别的界面回到桌面重新获取消息数
        //console.log('路由监听：从', from.name, '返回到工作台');
       // this.onBackToThisPage(from.name, from.params);
      }
    }
  },
  methods: {
    // 从其他页面返回到工作台时调用
    onBackToThisPage(fromPageName, params) {
      console.log('返回到工作台，来自页面:', fromPageName, '参数:', params);
      // 刷新数字计数
      this.handleGetShowCounts();

      // 特别处理送货签收页面返回
      if (fromPageName === 'DeliveryReceipt' || fromPageName === 'SaleSheet') {
        console.log('从送货签收相关页面返回，强制刷新数字');
        // 延迟一点时间再刷新，确保后端数据已更新
        setTimeout(() => {
          this.handleGetShowCounts();
        }, 500);
      }
    },
    loadWeather() {
      //debugger
      const WEATHER_SVG_ICON_PREFIX = "weather_"
      const weatherDic = {
        "晴": "qing",
        "多云": "duoyun",
        "阴": "yin",
        "阵雨": "zhenyu",
        "雷阵雨": "leizhenyu",
        "雷阵雨伴有冰雹": "leizhenyubingbao",
        "雨夹雪": "yujiaxue",
        "小雨": "yu1",
        "中雨": "zhongyu",
        "大雨": "dayu",
        "暴雨": "baoyu",
        "大暴雨": "dabaoyu",
        "特大暴雨": "tedabaoyu",
        "阵雪": "zhenxue",
        "小雪": "xiaoxue",
        "中雪": "zhongxue",
        "大雪": "daxue",
        "暴雪": "baoxue",
        "雾": "wu",
        "沙尘暴": "shachenbao",
        "小到中雨": "xiaodaozhongyu",
        "中到大雨": "zhongdaodayu",
        "大到暴雨": "dadaobaoyu",
        "暴雨到大暴雨": "baoyudaodabaoyu",
        "大暴雨到特大暴雨": "dabaoyuzhuantedabaoyu",
        "小到中雪": "xiaodaozhongxue",
        "中到大雪": "zhongdaodaxue",
        "大到暴雪": "dadaobaoxue",
        "扬沙": "yangsha",
        "强沙尘暴": "qiangshachenbao",
        "浓雾": "nongwu",
        "轻雾": "wu",
        "强浓雾": "qiangnongwu",
        "霾": "mai",
        "中度霾": "zhongdumai",
        "重度霾": "zhongdumai1",
        "严重霾": "yanzhongmai",
        "大雾": "大雾",
        "特强浓雾": "teqiangnongwu",
        "雨": "yu1",
        "雪": "xiaoxue"
      }
      let that_ = this
      if (isiOS) {
        this.getiOSWeatherCurrentCity(cityName => {
          //跨域问题
          //https://stackoverflow.com/questions/36000757/cordova-ios-error-origin-null-is-not-allowed-by-access-control-allow-origin
          const cityCode = that_.getCityCode(cityName)
          this.getWeatherInfo(cityCode, weatherInfo => {
            if (!weatherInfo) return
            that_.weatherInfo = weatherInfo
            that_.weatherIconCode = WEATHER_SVG_ICON_PREFIX + weatherDic[weatherInfo.now.text]
          }, error => {
            console.log(JSON.stringify(error))
          })
        })
      }
      else {
        this.getAndroidWeatherCurrentCity(cityName => {
          const cityCode = that_.getCityCode(cityName)
          this.getWeatherInfo(cityCode, weatherInfo => {
            if (!weatherInfo) return
            that_.weatherInfo = weatherInfo
            that_.weatherIconCode = WEATHER_SVG_ICON_PREFIX + weatherDic[weatherInfo.now.text]
            console.log(weatherInfo)
          }, error => {
            console.log(JSON.stringify(error))
          })
        })
      }
    },
    getWeatherInfo(cityCode, cbSuccess, cbError) {
      axios.get(`http://api.map.baidu.com/weather/v1/?ak=Qa6pXVRjlsnQrI8mNyNxHvTck4E3gndl&&data_type=all&&district_id=${cityCode}`,
        {
          cache: false,
          headers: {
            "Access-Control-Allow-Origin": "*",
            "cache-control": "no-cache",
            "If-Modified-Since": "0"
          }
        })
        .then(res => {
          console.log(res)
          const weatherInfo = res.data.result
          cbSuccess(weatherInfo)
        })
        .catch(error => {
          cbError(error)
        })
    },
    getNthCharInString(string, char, n) {
      var idx = 0
      var findTime = 0
      var findIdx = 0
      while (idx <= string.length - 1 && findTime <= n) {
        if (string.charAt(idx) === char) {
          findTime++
          findIdx = idx
        }
        idx++
      }
      if (findTime < n) {
        findIdx = -1
      }
      return findIdx
    },
    getCityCode(cityName) {
      console.log(cityName)
      const processedCityName = this.getExcludeCharCityName(cityName)
      console.log(processedCityName)
      const currentCity = WeatherDistrictIdJson.filter(i => i.cityName == processedCityName)[0]
      const cityCode = currentCity.cityCode
      return cityCode
    },
    getExcludeCharCityName(cityName) {
      console.log(cityName)
      //去掉 县/区字样
      // cityName='滁州市明光市'
      if (cityName.indexOf('区') != -1) {
        return cityName.substring(0, cityName.indexOf("区"))
      }
      if (cityName.indexOf('县') != -1) {
        return cityName.substring(0, cityName.indexOf("县"))
      }
      if (this.getNthCharInString(cityName, '市', 2) != -1) {
        return cityName.substring(0, this.getNthCharInString(cityName, '市', 2))
      }
      return cityName
    },
    showTodayVisitCountAndCreateSupcustCount() {
      const operKey = this.$store.state.operKey
      var viewRange = window.getRightValue('delicacy.sheetViewRange.value')
      console.log("viewRange", viewRange)
      if (viewRange == 'all') {
        BossGetVisitorCountAndCreateCustomerCount(operKey)
          .then(res => {
            const todayInfo = {
              visitCount: res.visit_count,
              createSupcustCount: res.create_supcust_count
            }
            console.log("todayInfo", todayInfo)
            this.todayInfo = todayInfo

          })
      } else if (viewRange === 'department') {
        console.log("测试")
        DepartManagerGetVisitorCountAndCreateCustomerCount(operKey).then(res => {
          const todayInfo = {
            visitCount: res.visit_count,
            createSupcustCount: res.create_supcust_count
          }
          console.log("todayInfo", todayInfo)
          this.todayInfo = todayInfo


        })
      }
      else {
        SellerGetVisitorCountAndCreateCustomerCount(operKey).then(res => {
          const todayInfo = {
            visitCount: res.visit_count,
            createSupcustCount: res.create_supcust_count
          }
          console.log("todayInfo", todayInfo)
          this.todayInfo = todayInfo
        })
      }
    },
    goUrl(url) {
      this.handleRemoveSlickItem()
      window.goUrl(this, url)
    },
    /*getVisitUrlByMode(visitShowMode) {
      if (!visitShowMode || visitShowMode === 'visitUser') {
        return '/VisitUser'
      }
      if (visitShowMode === 'visitDay') {
        return '/VisitDay'
      }
    },*/
    myGetOperRight(retryCount = 0) {
      //if (window.g_operInfoRefreshed) return
      var that = this
      console.log('this.$store.state.operInfo', this.$store.state.operInfo  )
      
       
      console.log('enter getOperRight')
      window.getOperRights(store.state.operKey, function (res) {
       // window.g_debugInfo += 'B'
        if (res.result !== 'OK') {
          Toast(res.msg)
          //setTimeout(function(){
          
          //  },1000)
          // 重试机制：最多重试 3 次
             /*
          if (retryCount < 3) {
            console.log(`Retry attempt: ${retryCount + 1}`);
            setTimeout(function () {
              that.myGetOperRight(retryCount + 1);
            }, 1000); // 延迟1秒后重试
          } else {
            */
           // Toast('请检查网络')
            that.net_error = true
            console.error('Maximum retry attempts reached.');
         // }
            return
        }
        window.g_operInfoRefreshed = true
        if (!res.operInfo) {
          alert('res.operInfo Workbench' + JSON.stringify(res.operInfo))
        }
        var operInfo = res.operInfo
        if(operInfo.oem_logo_url){
          that.logoUrl=operInfo.oem_logo_url
        }
        var canLoginApp = window.getRightValue('delicacy.allowLoginApp.value')
        if (canLoginApp == 'false') {
          that.loginErrMsg = '您没有登录APP的权限'
        }
        else {
          that.expired = res.expired
          that.expireMsg = res.expireMsg
          if (that.expired) that.loginErrMsg = res.expireMsg
        }
        window.loginErrMsg = that.loginErrMsg//传给whole
         
        // that.getAppPersonalSetting()
        that.startNewPage()
        that.refreshViewByOperRights()
        that.net_error = false
        retryCount = 0
        that.getAppDesktop()
        that.isRefresh = false
        setTimeout(() => {
          if (operInfo && operInfo.log_report) {
            if (operInfo.log_report.indexOf('report') >= 0) {
              let params = {
                log: window.g_debugInfo,
                operKey: this.$store.state.operKey
              }
              ApiReportLog(params).then(res => {

              })
            }
          }
        }, 20000)

        //that.onPullRefresh()
        //store.commit("operRights", res.operRights)
        //  next(); //继续往后走

      })

    },
    refreshViewByOperRights() {

      this.layout = this.layout.filter(menu => {
        /*if(menu.title =="装车申请"||menu.title == "装车审核"){
          var canAssignVan = getSettingValue('flowExistMove')
          if(canAssignVan.toLowerCase()!="true"){
            return false
          }
        }*/
        var rightPath = menu.right;
        if (rightPath) {
          var can = function (path) {
            if (path.indexOf('.make') > 0) {
              let rightPathApprove = path.replace('.make', '.approve')
              if (hasRight(rightPathApprove) || hasRight(path)) {
                return true
              }
            }
            else if (hasRight(path)) {
              return true
            }
            return false
          }
          if (rightPath.indexOf("")) {
            
          }
          if (rightPath.indexOf('orderToSale') >= 0) {
            
          }
          var paths = rightPath.split('|')
          var bMet = false
          paths.forEach(p => {
            if (can(p)) bMet = true
          })
          if (bMet) return true

        }
        else return true;
        return false;
      })
    },
    startNewPage() {
      window.g_debugInfo += 'C'
      this.companyName = this.$store.state.account.companyName
      this.operName = this.$store.state.operInfo.oper_name
      var viewRange = window.getRightValue('delicacy.sheetViewRange.value')
      var sellerID = ''
      var departID = ""
      if (viewRange == 'self') sellerID = this.$store.state.operInfo.oper_id
      if (viewRange == 'department') departID = this.$store.state.operInfo.depart_id
      let dateTime = this.getCurrentTime(this.dateType)
      console.log('日期：');
      console.log(dateTime);
      let condi = {
        dateType: this.dateType,
        startTime: dateTime.startTime,
        endTime: dateTime.endTime,
        viewRange: viewRange,
        sellerID: sellerID,
        departID:departID,
        startRow: this.startRow,
        pageSize: this.pageSize
      }
      if(!isHarmony)
        this.loadWeather()
      window.g_debugInfo += 'D'
      this.showTodayVisitCountAndCreateSupcustCount()
      window.g_debugInfo += 'E'
      GetTrends(condi).then((res) => {
        if (res.result === "OK") {
          window.g_debugInfo += 'F'
          this.saleTrend = {
            salesum: this.fix(res.todayTotal.total),
            profit: this.fix(res.todayTotal.profittotal),
            x_amount: this.fix(res.todayTotal.saletotal),
            t_amount: this.fix(res.todayTotal.returntotal)
          }

          window.g_debugInfo += JSON.stringify(this.saleTrend)
          /*setTimeout(() => {
            var aa = $('canvas')
            aa.css('background-color', '#fffcfa')
          }, 200)*/

          /* bad code
         let objs ={
          types: 0,
          datas: res.records,
          titles:"销售净额",
          names:"日期",
          select:true
        }
        this.trendData = objs
         */

          this.trendData = {
            types: 0,
            datas: res.records,
            titles: "销售净额",
            names: "日期",
            select: true
          }
        }
      })

      var date = new Date()
      var y = date.getFullYear()
      var m = (date.getMonth() + 1 + '').padStart(2, '0')
      condi.startTime = `${y}-${m}-01`
      condi.endTime = this.getSystemDate() + ' ' + '23:59:59'
      /*GetSellerRank(condi).then((res) => {
        if (res.result === "OK") {
          if (res.records.length > 0)
            this.sellerTrend = res.records[0]
        }
      })*/
      // this.getCurrentDateSale()
    },
    /*getCurrentDateSale() {
      let dateTime = this.getCurrentTime('day')
      let params = {
        dateType: '',
        startTime: dateTime.startTime,
        endTime: dateTime.endTime,
        startRow: this.startRow,
        pageSize: this.pageSize
      }
      GetTrends(params).then((res) => {
        if(res.result === 'OK') {
          if(res.count === '0') {
            this.saleTrend = {
              salesum: 0,
              profit: 0,
              x_amount: 0,
              t_amount: 0
            }
          }
          else {
            this.saleTrend = {
              salesum: this.fix(res.records[0].salesum),
              profit: this.fix(res.records[0].profit),
              x_amount: this.fix(res.records[0].x_amount),
              t_amount: this.fix(res.records[0].t_amount)
            }
          }
        }
      })
    },*/
    onPullRefresh() {   
      this.myGetOperRight(0)
      //setTimeout(() => {
      //  this.isRefresh = false
      //  this.startNewPage()
      //}, 500)
    },
    onTranslate() {
      this.isShow = !this.isShow
    },
    onGoRouter(obj) {
      this.handleRemoveSlickItem()
      this.$router.push({ path: obj })
    },
    fix(num, n = 2) {
      var pn = Math.pow(10, n)
      return Number(Math.round(num * pn) / pn)
    },
    getCurrentTime(dateType) {
      var curentDate = new Date()
      var endTime = this.getDatePart(new Date())
      //curentDate.toLocaleString('chinese', { hour12: false })
      if (dateType === 'day') {
        var startTime = this.getDatePart(curentDate) + ' ' + '00:00:00'
      } else if (dateType === 'month') {
        var startTime = this.getDatePart(new Date(curentDate - 1000 * 60 * 60 * 24 * 31)) + ' ' + '00:00:00'
        endTime = this.getDatePart(new Date()) + ' ' + '23:59:59'
      } else if (dateType === 'year') {
        var startTime = this.getDatePart(new Date(curentDate - 1000 * 60 * 60 * 24 * 31 * 6)) + ' ' + '00:00:00'
      } else if (dateType === 'other') {
        var startTime = this.reportForm.startTime + ' ' + '00:00:00'
        var endTime = this.reportForm.endTime + ' ' + '23:59:59'

      }

      return { startTime, endTime }
    },
    getiOSWeatherCurrentCity(cbSuccess, cbFail) {
      if (window.baidumap_location) {
        window.baidumap_location.getCurrentPosition(res => {
          console.log(res)
          const cityName = res.city + res.district
          cbSuccess(cityName)
        }, error => {
          cbFail()
        })
      }
      else {
        if (cbFail) cbFail()
      }

    },
    getAndroidWeatherCurrentCity(cbSuccess, cbFail) {
      
      var options = {
        enableHighAccuracy: true,  // 是否使用 GPS
        coorType: 'bd09ll'         // 默认是 gcj02，可填 bd09ll 以获取百度经纬度用于访问百度 API
      }
      if (navigator.geolocation) {
        if(isHarmony&&!this.$store.state.positionWeather){
          Dialog.confirm({
          title: '获取权限',
          message: '需要定位权限用于获取所在位置天气信息',
          confirmButtonText: '允许',
          cancelButtonText: '禁止'
        })
          .then(() => {
            this.$store.commit('positionWeather',true)
            navigator.geolocation.getCurrentPosition(success, error, options)
          })
        }
        else navigator.geolocation.getCurrentPosition(success, error, options)
        function success(position, extra) {
          if(!extra || !extra.placeName ) return
          let weatherCurrentCity=""
          if(isHarmony)
            weatherCurrentCity = extra.placeName.substring(extra.placeName.indexOf("省") + 1)
          else weatherCurrentCity = extra.addr.substring(extra.addr.indexOf("省") + 1)
          console.log(position.coords)
          if (cbSuccess) {
            cbSuccess(weatherCurrentCity)
          }
        }
        function error() {
          if (cbFail) {
            cbFail()
          }
        }
      }
      else {
        if (cbFail) cbFail()
      }

    },
    settingOpacity() {
      var narBarMain = document.getElementById('nar_bar_main')
      var narBarSmall = document.getElementById('nar_bar_small')
      if (Number(narBarMain.style.opacity) > Number(narBarSmall.style.opacity)) {
        narBarMain.style.opacity = 1
        narBarSmall.style.opacity = 0
      } else {
        narBarMain.style.opacity = 0
        narBarSmall.style.opacity = 1
      }
    },
    handleScroll(e) {

      if (this.$route.path == '/SelectItems') return // 解决多选属性触发
      var narBarMain = document.getElementById('nar_bar_main')
      var narBarSmall = document.getElementById('nar_bar_small')
      if (!narBarMain || !narBarSmall) return //不在工作台时，该时事件也会触发
      if ((Number(narBarSmall.style.opacity) > Number(narBarMain.style.opacity) && (Number(e.target.scrollTop) > Number(this.scrollTopIndex)))
      ) {
        narBarSmall.style.opacity = 1
      } else if ((Number(narBarMain.style.opacity) > Number(narBarSmall.style.opacity) && (Number(e.target.scrollTop) < Number(this.scrollTopIndex)))) {
        narBarMain.style.opacity = 1
        narBarSmall.style.opacity = 0
      }
      else {
        narBarMain.style.opacity = 1 - Number(e.target.scrollTop) * 0.03
        narBarSmall.style.opacity = Number(e.target.scrollTop) * 0.03
      }

      this.scrollTopIndex = Number(e.target.scrollTop)
    },
    inithandleScroll() {
      var narBarMain = document.getElementById('nar_bar_main')
      if (narBarMain !== undefined && narBarMain !== null) {
        narBarMain.style.opacity = 1
      }
      var narBarSmall = document.getElementById('nar_bar_small')
      if (narBarSmall !== undefined && narBarSmall !== null) {
        narBarSmall.style.opacity = 0
      }



    },
    numFormatBank(data, len) {
      if (!data || Number(data) === 0) return 0
      len = len ? len : 0
      if (data != null) {
        return Number(data).toFixed(len).replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,');
      } else {
        return;
      }
    },
    ellipsis(value, len) {
      if (!value) return ''
      if (value.length > len) {
        return value.slice(0, len) + '...'
      }
      return value
    },
    getChangeLists(vals) {
      this.layout = vals
      this.refreshViewByOperRights();
      this.saveAppDesktop();

    },
    handleallNavattr() {
      let wholeData = []
      JSON.parse(JSON.stringify(wholeJson)).forEach(obj => {
        if (obj.subsetAttr.length > 0) {
          obj.subsetAttr.forEach(menu => {
            wholeData.push(menu)
          })
        }
      })
      let filterArr = unique(wholeData)
      function unique(arr) {
        const res = new Map()
        return arr.filter((item) => !res.has(item.key) && res.set(item.key, 1))
      }
      this.allnavattr = [...this.regularNavattr, ...filterArr]
    },
    handleGetShowCounts() {
      
      var showCount = false
      let paramsCounts = {}
      var operID = "", departID = ""
      if (!window.isBoss()) {
        if (this.$store.state.operInfo.operRights && this.$store.state.operInfo.operRights.delicacy && this.$store.state.operInfo.operRights.delicacy.sheetViewRange) {
          if (this.$store.state.operInfo.operRights.delicacy.sheetViewRange.value == 'self') {
            operID = this.$store.state.operInfo.oper_id
          } else if (this.$store.state.operInfo.operRights.delicacy.sheetViewRange.value == "department") {
            this.departID = this.$store.state.operInfo.depart_id;
          } else {
            departID = ''
          }
        }
      }
      var operRegions = this.$store.state.operInfo.operRegions;
      if (operRegions) operRegions = JSON.stringify(operRegions);


      this.layout.forEach((menu) => {
        if (menu.showCounts && menu.showCounts == true) {
          paramsCounts[menu.key] = {}
          var startDate = this.computeDate(-6) + ' 00:00:00';
          var endDate = this.computeDate(0) + ' 23:59:59';
          var is_sender = this.$store.state.operInfo.is_sender == 'True' ? true : false
          
          paramsCounts[menu.key] = {
            startDate: startDate,
            endDate: endDate,
            operRegions: operRegions,
            operID: operID,
            departID: departID,
            is_sender: is_sender,            
            isBoss: window.isBoss()
          }

          if (menu.key == 'toAssignOrders') {
            var needReview = window.getSettingValue('reviewOrderBeforeAssignVan').toLowerCase() == 'true'
            paramsCounts[menu.key].needReview = needReview
          }
          else if (menu.key == 'toApproveBackBranch' || menu.key == 'toApproveAssignVan') {
            var branchRights = this.$store.state.operInfo.branchRights
            var branches = ""
            branchRights.forEach(branch => {
              if (branch.sheet_dc === 'True') {
                if (branches !== "") branches += ','
                branches += branch.branch_id
              }
            })
            paramsCounts[menu.key].branches = branches
          }
          else if (menu.key == 'deliveryReceipt') {
             // debugger
              const setting = this.$store.state.operInfo.setting
              var flowExistMove = ""
              if (setting) flowExistMove = setting.flowExistMove.toString().toLowerCase()
              var isAssignVanNecessary = flowExistMove == 'true'
              var noVanOrderToSale = window.getRightValue('delicacy.noVanOrderToSale.value').toLowerCase() == 'true'
              if (isAssignVanNecessary) paramsCounts[menu.key].isAssignVanNecessary = isAssignVanNecessary
              paramsCounts[menu.key].noVanOrderToSale = noVanOrderToSale
              let cacheValue = this.$store.state.yjSelectCalendarCacheStore['ViewDeliveryReceiptCacheKey'];
              if(!cacheValue) cacheValue = "7-day+7"
              const currentDate = new Date();
              let start_date = new Date(currentDate.getTime());
              let end_date = new Date(currentDate.getTime());
              if (cacheValue) {
                let cacheValueArr = (cacheValue + '').split('+')
                if (cacheValueArr.length > 1) {
                  let arr = cacheValueArr[0].split('-')
                  if (cacheValueArr[0] === 'yesterday') {
                    start_date = new Date(currentDate.getTime() - 1 * 24 * 60 * 60 * 1000);
                    end_date = new Date(currentDate.getTime() - 1 * 24 * 60 * 60 * 1000);
                  } else if (cacheValueArr[0] === 'currentMonth') { // 本月
                    let startYear = currentDate.getFullYear();
                    start_date = new Date(startYear, currentDate.getMonth(), 1);
                  } else {
                    if (arr[1] === 'day') {
                      start_date = new Date(currentDate.getTime() - (arr[0] - 1) * 24 * 60 * 60 * 1000);
                    } else if (arr[1] === 'month') {
                      let startYear = currentDate.getFullYear();
                      let startMonth = currentDate.getMonth() - arr[0];
                      while (startMonth < 0) {
                        startMonth += 12;
                        startYear--;
                      }
                      const prevMonthLastDay = new Date(startYear, startMonth + 1, 0).getDate();
                      const currentDay = currentDate.getDate();
                      const startDay = Math.min(prevMonthLastDay, currentDay);
                      start_date = new Date(startYear, startMonth, startDay);

                    }
                  }

                }else if(cacheValueArr.length==1){
                  let arr = cacheValueArr[0].split('-')
                  start_date = new Date(currentDate.getTime() - (arr[0] - 1) * 24 * 60 * 60 * 1000);
                }
              }

              paramsCounts[menu.key].startDate = `${start_date.getFullYear()}-${start_date.getMonth() + 1 < 10 ? '0' : ''}${start_date.getMonth() + 1}-${start_date.getDate() < 10 ? '0' : ''}${start_date.getDate()}` + ' 00:00:00'
              paramsCounts[menu.key].endDate = `${end_date.getFullYear()}-${end_date.getMonth() + 1 < 10 ? '0' : ''}${end_date.getMonth() + 1}-${end_date.getDate() < 10 ? '0' : ''}${end_date.getDate()}` + ' 23:59:59'
          }
            showCount = true
          }
        })
      if (showCount) {
        QueryDesktopData(paramsCounts).then((res) => {
          if (res.result === "OK") {
            this.countsResult = res.data
          }
        })
      }
    },
    computeDate(days) {
      var d = new Date();
      d.setDate(d.getDate() + days);
      var m = d.getMonth() + 1;
      return d.getFullYear() + '-' + m + '-' + d.getDate();
    },
    handleDate(startDay, endDay) {
      this.startDate = this.computeDate(startDay) + ' 00:00:00';
      this.endDate = this.computeDate(endDay) + ' 23:59:59';
    },
    getAppDesktop() {
      if (this.$store.state.operKey == '') {
        return
      }
      this.layout = []
      let that = this
      let params = {
        appORpcFlag: this.appORpcFlag
      };
      this.handleallNavattr()
      GetDesktop(params).then(res => {
        if (res.data.app_desktop == "" || JSON.parse(res.data.app_desktop).length == 0) {
          that.layout = JSON.parse(JSON.stringify(that.navattr));
        } else {
          JSON.parse(res.data.app_desktop).forEach(item => {
            that.allnavattr.forEach(obj => {
              if (item == obj.key) {
                that.layout.push(obj)
              }
            })
          })
          /*
          that.layout.forEach((item) => {
            if (item.key === "viewSaleSheets") {
              item.title = "车销查单"
            } else if (item.key === "viewStockSheets") {
              item.title = "仓库查单"
            } else if (item.key === "viewBuySheets") {
              item.title = "采购查单"
            } else if (item.key === "viewFundSheets") {
              item.title = "资金查单"
            } else if (item.key === "viewActiveSheets") {
              item.title = "营销查单"
            }
          })*/
        }
        that.refreshViewByOperRights();
        
        if (res.data.app_setting && res.data.app_setting != this.$store.state.operSetting) {
          let app_setting = JSON.parse(res.data.app_setting)
          if (app_setting.fontSize===undefined) {
            app_setting.fontSize = 1
          }
          this.$store.commit('operSetting', app_setting) 

        } else {
           this.$store.commit('operSetting', { fontSize: 1 })
        }
        if (res.data.app_setting){
          //  setGlobalFontSize(res.data.app_setting.fontSize)
          //  this.$forceUpdate()
        }
         var fontSize=this.$store.state.operSetting.fontSize
        if (FONT_SIZE.fontLevel[fontSize] != document.documentElement.style.fontSize) {
            setGlobalFontSize(fontSize)
            this.$forceUpdate() 
        }
        this.handleGetShowCounts()
      }).catch(err => {
        Toast("获取失败,展示默认桌面,"+ err.message,5000)
        that.layout = JSON.parse(JSON.stringify(that.navattr));
      })
    },
    saveAppDesktop() {
      this.refreshViewByOperRights()
      let app_desktop = [...new Set(this.layout.map(item => {
        return item.key
      }))]
      SaveDesktop({
        operKey: this.$store.state.operKey,
        app_desktop: app_desktop,
        appORpcFlag: this.appORpcFlag,
      }).then(res => {
      })

    },
    // getAppPersonalSetting() {
    //   if (this.$store.state.operKey == '') {
    //     return
    //   }
    //   let params = {
    //     type: this.appORpcFlag
    //   };
    //   GetPersonalSetting(params).then(res => {
    //     console.log("获取个性化结果",res)
    //     if (res.data.app_setting && res.data.app_setting != this.$store.state.personalSetting) {
    //       let app_setting=JSON.parse(res.data.app_setting)
    //       if (!app_setting.fontSize) {
    //         app_setting.fontSize = 1
    //       }
    //       this.$store.commit('personalSetting', app_setting)
    //     } else {
    //       this.$store.commit('personalSetting', { fontSize: 1 })
    //     }
    //     if (fontSize.fontLevel[this.$store.state.personalSetting.fontSize] != document.documentElement.style.fontSize) {
    //       setGlobalFontSize(this.$store.state.personalSetting.fontSize)
    //     }
    //   }).catch(err => {
    //   })
    // },
    handleEdit() {
      this.handleRemoveSlickItem()
      this.$router.push({ path: '/WorkbenchEdit', query: { allnavattr: this.allnavattr, layout: this.layout } })
    },
    handleSlickItemClass(item) {
      if (item.class) {
        if (item.class === 'chartSaleTrend') {
          if (hasRight(item.right)) {
            return item.class
          } else {
            return ""
          }
        } else {
          return item.class
        }

      } else {
        return "moudle"
      }
    },
    handleSlickItemClick(item) {
      if (item.key == 'cardTodayVisit' && this.canSeeVisitRecord) {
        return this.onGoRouter('/VisitRecord');
      }
      return ""
    },
    handleLayout() {
      console.log(this.$route)
    },
    sortStart() {
      this.refreshDisabled = true
    },
    sortEnd() {
      this.refreshDisabled = false
    },
    handleRemoveSlickItem() {
      const removeDomArr = []
      this.checkRemoveDomClassName.forEach(item => {
        let dom = document.querySelectorAll(`body > ${item}`);
        if (dom) {
          removeDomArr.push(dom)
        }
      })
      console.log('moudleDom', removeDomArr)
      removeDomArr.forEach(removeItem => {
        removeItem.forEach(element => {
          element.remove();
        })
      })
    }
  }
}
</script>

<style lang="less" scoped>
@import "../assets/font/font.css";

.black {
  stroke: currentColor;
  // color: #757575;
  color: #454545;
}

body > .moudle {
  background-color: #f2f6fc;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0px 0px 10px;
  border-radius: 5px;
  width: 33%;

  .icon {
    margin-top: 20px;
    font-size: 28px;

    svg {
      width: 32px;
      height: 32px;
    }
  }

  p {
    font-size: 16px;
    margin: 5px 0 0;
    color: #000;
  }

  h6 {
    font-size: 14px;

    margin: 5px 0 0;
    color: #000;
  }
}

.saleTrend {
  margin-bottom: 20PX;
}

body > .saleTrend .saleTrend_chart_view {
  height: 210PX !important;
  border-radius: 10PX;
  //background-color: chartreuse;
  background-image: linear-gradient(
    -225deg,
    rgb(252, 221, 225) 0%,
    rgb(253, 245, 246) 100%
  );
}

.wrapper {
  // font-family: font;
  background-color: #fff;
}

.todaySale {
  color: #777;

  border-radius: 10px !important;
  height: 100px;

  div {
    margin-bottom: 3px;
    font-size: 12px;
  }

  span {
    font-size: 14px;
  }

  .red {
    color: #e1251b;
    font-size: 25px;
  }

  .gray {
    color: #888;
  }

  width: 130px;

  background: #fff;
  border-radius: 3px;
}

.noticeStr {
  padding: 0 15px 0 15px;
  display: flex;
  color: #e1251b;
  margin-top: 4px;
  justify-content: space-around;
}

.today_sale_wrapper {
  padding: 0 15px 0 15px;
  display: flex;
  margin-top: 4PX;
  justify-content: space-between;
}

.today_sale_left {
  height: 130PX;
  margin: 5PX 10PX;
  min-width: 120PX;
  width: 45%;
  flex: 1;
  background-image: url("../assets/icons/png/backgroundBlue2x.png");
  background-size: 100% 100%;
  display: flex;

  flex-direction: column;
  align-items: flex-start;
  border-radius: 8PX;

  .sale_left_icon {
    width: 52PX;
    height: 52PX;

    img {
      padding: 15PX 0 0 15PX;
      width: 50PX;
      height: 32PX;
    }
  }

  .sale_left_salesum {
    padding-left: 15PX;
    margin-top: 10PX;
    color: #333;
    font-family: numfont;
    font-size: 32PX;
  }

  .sale_left_amount {
    font-size: 16PX;
    // padding-left: 15px;
    color: #999999;
    display: flex;
    font-family: font;

    .sale_left_amount_num {
      height: 20PX;
      line-height: 20PX;
      display: flex;
      font-size: 12PX;
      font-family: font;
      z-index: 300;
    }
  }
}

.slickList-wrapper {
  margin: 0px 0px 10px 0px;
  display: flex;
  flex-wrap: wrap;
}

.today_sale_right{
  margin: 5PX 10PX;
  min-width: 120PX;
  width: 45%;
  flex: 1;
  background-image: url("../assets/icons/png/backgroundPink2x.png");
  background-size: 100% 100%;
  height: 130PX;

  border-radius: 8PX;
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  .sale_right_icon {
    width: 52PX;
    height: 52PX;

    img {
      padding: 15PX 0 0 15PX;
      width: 50PX;
      height: 32PX;
    }
  }

  .sale_right_salesum {
    padding-left: 15PX;
    margin-top: 10PX;
    color: #333;
    font-family: numfont;
    font-size: 32PX;
  }

  .sale_right_salesum > span {
    margin-right: 10PX;
    color: #333;
    font-family: numfont;
    font-size: 18PX;
  }

  .sale_right_salesum_oper {
    padding-left: 15PX;
    color: #999999;

    .sale_right_salesum_opername {
      height: 20PX;
      line-height: 20PX;
      display: flex;

      .salesum_title {
        font-size: 12PX;
        font-family: font;
      }

      img {
        height: 10PX;
      }
    }
  }
}

@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
};

@flex_ac_jb: {
  display: flex;
  align-items: center;
  justify-content: space-between;
};

@flex_a_flex: {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
};

.work_bench_title {
  display: flex;
  align-items: center;
  text-align: left;
  padding: 0 10px;
  color: #000;
  margin-left: 12px;
  font-size: 16px;
  font-weight: bold;

  .line {
    width: 3px;
    height: 20px;
    background: rgba(245, 108, 108, 0.8);
    border-radius: 15px;
    margin-right: 6px;
  }
}

.pull_refresh {
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  margin-top: 0px;
 // background-color: #fffcfa;

  .current_day_boss {
    height: 65px;
    display: flex;

    div {
      width: 33.33%;
      height: 80%;
      background: #ffffff;
      border-radius: 5px;
      margin: 0 5px 5px 0;
      padding: 8px 3px 0;

      h6 {
        height: 50%;
        color: rgb(78, 78, 78);
        font-size: 14px;
      }

      h5 {
        color: #e1251b;
        height: 50%;
        font-size: 15px;
      }
    }

    div:last-child {
      margin: 0 0 5px 0;
    }

    .x_t_amount {
      div {
        width: 80%;
        height: 25%;
        color: rgb(78, 78, 78);
        font-size: 14px;
        padding: 0 0 8px 10px;
        text-align: left;
        font-weight: 600;

        span {
          font-size: 15px;
          font-weight: 600;
          color: #e1251b;
        }
      }
    }
  }

  .current_day_seller {
    height: 65px;
    display: flex;

    div {
      width: 48%;
      height: 80%;
      background: #ffffff;
      border-radius: 5px;
      margin: 0 5px 5px 0;
      padding: 8px 3px 0;

      h6 {
        height: 50%;
        color: rgb(78, 78, 78);
        font-size: 14px;
      }

      h5 {
        color: #e1251b;
        height: 50%;
        font-size: 15px;
      }
    }

    div:last-child {
      margin: 0 0 5px 0;
    }

    .x_t_amount {
      div {
        width: 80%;
        height: 25%;
        color: rgb(78, 78, 78);
        font-size: 14px;
        padding: 0 0 8px 10px;
        text-align: center;
        font-weight: 600;

        span {
          font-size: 15px;
          color: #e1251b;
        }
      }
    }
  }

  .saleTrend {
    width: 100%;
    height: 210PX;

    background: #ffffff;
    margin-top: 10PX;

    .saleTrend_chart {
      height: 210PX;

      .saleTrend_chart_view {
        height: 210PX;
      }
    }
  }

  .clearfix:before,
  .clearfix:after {
    content: "";
    display: table;
  }

  .clearfix:after {
    clear: both;
  }

  .clearfix {
    *zoom: 1;
  }

  .other44 {
    margin-top: 10px;
    border-radius: 10px;
    border-radius: 10px !important;
    background-color: #fff;
    margin-bottom: 10px;
  }

  .moudle {
    color: #000 !important;
    width: 33%;
    height: 95px;
    // margin: 0 2px 2px 0;
    float: left;

    // display: flex;
    // align-items: center;
    .icon {
      margin: 10px 0;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 28px;

      svg {
        width: 32px;
        height: 32px;
      }
    }

    .module_title {
      width: 100%;
      text-align: center;
      font-size: 16px;
      color: #000;
    }

    p {
      font-size: 16px;
      margin: 5px 0 0;
      color: #000;
    }

    h6 {
      font-size: 14px;

      margin: 5px 0 0;
      color: #000;
    }
  }

  .add_moudle_wrapper {
    display: flex;
    justify-content: center;
    align-items: center;

    .add_moudle {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 50%;

      border-radius: 10px;
      height: 100%;

      svg {
        width: 35px;
        height: 35px;
      }
    }
  }

  /*
  .moudle:nth-child(3){
    margin: 0 0 0px 0;
  }
  .moudle:nth-child(6){
    margin: 0 0 0px 0;
  }*/
  .error_msg {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #eee;
  }

  .visit {
    margin: 0 0 0 5px;
  }

  .sellerRank {
    width: calc((100vw - 44px) / 3 * 2);
    height: 85px;
    background: #ffffff;
    padding: 5px 10px;
    float: left;
    margin-right: 2px;

    h4 {
      height: 30px;
      font-size: 16px;
      text-align: left;
      font-weight: normal;
      margin-top: 10px;
    }

    h6 {
      height: 20px;
      font-size: 15px;
      font-weight: normal;
      @flex_ac_jb();
      margin-top: 15px;
    }
  }
}

.net_error {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  background-color: #ffffff;

  .net_error_p {
    height: 30%;
    width: auto;
    margin-top: 100px;
    margin-left: 15px;
  }

  .net_error_b {
    height: 30px;
  }
}

#nar_bar_main {
  width: 100%;
}

.nav_bar_home {
  color: #000;
  box-shadow: 0 2px 5px #f2f6fc;
  background-color: #fff;
  margin-bottom: 1px;
  display: flex;
  align-items: center;
  height: 50px;
  width: 100%;
  padding: 10px 5px;

  .navbar_img {
    .headImg {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      margin-left: 5px;

      .smallLogoImg {
        width: 30px;
        height: 30px;
      }

      .mainLogoImg {
        width: 35px;
        height: 35px;
      }
    }
  }

  .navbar_title_info_wrapper {
    width: 100%;
    height: 48px;
    margin-top: 10px;

    .navbar_title {
      position: absolute;
      width: 70%;
      text-align: left;
      margin-left: 10px;

      .navbar_title_son {
        .navbar_title_son_company {
          font-size: 12px;
          color: #666;
        }

        .navbar_title_son_operName {
          font-size: 13px;
          margin-top: 4px;

          .navbar_title_son_test {
            color: #fff;
            background: #67c23a;
            border-radius: 5px;
            // padding: 0 5px 0 2px;
            font-size: 12px;
          }
        }

        h5 {
          font-size: 14px;
          font-weight: bold;
        }

        h6 {
          font-size: 12px;
          font-weight: normal;
          margin-top: 3px;
        }
      }
    }

    .navbar_weather {
      z-index: 3000;
      position: absolute;
      right:0;
      display: flex;
      height: 45px;
      align-items: center;
      font-family: numfont;
      justify-content: flex-end;
      align-items: center;
      margin-right: 10px;

      .navbar_weather_temp {
        font-size: 20px;
        color: #323232;
        display: flex;
        margin-right: 15px;

        .navbar_weather_temp_centigrade {
          font-size: 12px;
          margin-top: 0px;
          margin-right: 4px;
        }

        .navbar_weather_temp_info {
          font-size: 14px;
        }
      }

      .navbar_weather_text {
        margin-right: 10px;
        color: gray;
      }

      .navbar_weather_content {
        display: flex;
        flex-direction: column;
        margin-right: 10px;
        margin-top: -6px;

        .navbar_weather_icon {
          text-align: center;

          svg {
            width: 24px;
            height: 24px;
          }
        }

        .navbar_weather_name {
          color: gray;
          font-size: 12px;
        }
      }
    }
  }
}

.small {
  color: #000;
  box-shadow: 0 2px 5px #f2f6fc;
  background-color: #fff;
  display: flex;
  align-items: center;
  min-height: 30px;
  width: 100%;
  padding: 8px 15px 8px 5px;
  position: fixed;
  z-index: 999;
  top: 0;

  .navbar_img {
    .headImg {
      border-radius: 50%;
      margin-left: 10px;
      width: 30px;
      height: 30px;
    }
  }

  .navbar_title {
    text-align: left;
    margin-left: 10px;
    height: 30px;
    line-height: 30px;

    .navbar_title_son {
      display: flex;
      justify-content: space-between;

      .navbar_title_son_info {
        display: flex;
        justify-content: flex-start;

        .navbar_title_son_operName {
          font-size: 14px;
        }

        h6 {
          margin-left: 0px;
          font-size: 14px;
          font-weight: normal;
        }
      }

      .navbar_title_son_ico {
        width: 24px;
        height: 24px;
      }

      .navbar_weather_temp {
        font-size: 16px;
        font-weight: bold;
        padding-right: 10px;
      }

      h5 {
        font-size: 16px;
        font-weight: bold;
      }

      h6 {
        margin-left: 20px;
        font-size: 14px;
        font-weight: normal;
      }
    }
  }
}

// .vue-grid-item{
//   position: relative;
//   background: #ffffff;
//   box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
// }
// .home_title{
//   font-size: 15px;
//   text-align: left;
//   position: absolute;
//   left: 5px;
//   top: 5px;
// }
// .addWork{
//   position: fixed;
//   bottom: 100px;
//   width: 70px;
//   height: 50px;
//   background: #ffffff;
//   right: -50px;
//   transition:0.3s;
//   @flex_a_j();
//   h5{
//     width: 20px;
//     height: 100%;
//     @flex_a_j();
//     font-size: 16px;
//     color: #999999;
//   }

//   .addWork_add{
//     width: 40px;
//     height: 40px;
//     margin:0 5px;
//     @flex_a_j();
//     color: #1989fa;
//     border-radius: 4px;
//     font-size: 25px;
//     @flex_a_flex();
//     p{
//       font-size: 13px;
//       margin: 0;
//     }
//   }
// }
// .addWorkOpen{
//   right:0;
//   h5{
//     transform: rotate(180deg);
//   }
// }
// .iconsBtn{
//   @flex_a_flex();
//   height: 100%;
//   .iconsBtn_icon{
//     font-size: 25px;
//     color: #1989fa;
//   }
//   p{
//     padding: 0;
//     margin: 10px 0 0 0;
//     font-size: 14px;
//     font-weight: 500;
//   }
// }
// .chartShow{
//   height: calc(100% - 30px);
//   margin-top: 30px;
//   overflow: hidden;
//   @flex_a_j();
//   .chartShow_chart{
//     margin-left: -10px;
//   }
// }
// .work_box{
//   position: absolute;
//   left: 0;
//   right: 0;
//   top: 0;
//   bottom: 0;
// }
</style>
