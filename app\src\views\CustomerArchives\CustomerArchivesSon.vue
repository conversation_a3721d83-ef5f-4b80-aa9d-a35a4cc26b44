<template>
  <div class="pages">
    <my-preview-image v-if="showImagePreviewFlag" @closePreviewEvent="showImagePreviewFlag = false"
      :images="[isBase64(FormData.sup_door_photo) ? FormData.sup_door_photo : obsPrefix + FormData.sup_door_photo]"></my-preview-image>
       <my-preview-image v-if="showOtherImagePreviewFlag" @closePreviewEvent="showOtherImagePreviewFlag = false"
      :images="[...FormData.sup_other_photo.map(el=>{
        return isBase64(FormData.sup_door_photo) ? FormData.sup_door_photo : obsPrefix + FormData.sup_door_photo
      })]"></my-preview-image>
    <van-nav-bar title="客户档案" left-arrow @click-left="myGoBack($router)" safe-area-inset-top>
      <template #right v-if="this.canEdit || (!FormData.supcust_id && this.canAdd)">
        <div class="icon_filter" v-if="(FormData.approve_status !== 'wait approve' || !editLog)"
          @click="saveCustomerProfile(null)">保存
        </div>
        <div class="icon_filter" v-else-if="editLog && editLog.oper_action == 'EDIT'" @click="showChangeLog = true"
          style="color:#444;font-style: normal;font-size: 14px;">查看修改记录</div>
        <div class="icon_filter" v-else-if="editLog" @click="handleApprove(true)"
          style="color:#444;font-style: normal;font-size: 14px;">审核</div>
      </template>
    </van-nav-bar>
    <div class="public_box2">
      <div class="public_box2_t">
        <van-form v-model="FormData">
          <van-field v-model="FormData.sup_name" required label="客户名称" @input="onInputClientName" />
          <van-field v-model="FormData.py_str" label="客户助记名" />
          <van-field v-model="FormData.sup_alias" label="客户别名" />
          <van-field v-model="FormData.supcust_no" label="客户编号" />
          <van-field v-model="FormData.boss_name" v-bind:required="bossNecessary" label="老板姓名" />
          <van-field v-model="FormData.mobile" v-bind:required="mobileNecessary" label="联系电话" />
          <select-one v-if="clientRelateSeller" ref="selectGroup" style="width:100%" label="业务员"  :inputAlign="'left'"
            :allowedit="canEdit" :hasClearBtn="false" :target="'seller'" :formObj="FormData"
            :formFld="'charge_seller'" :formNameFld="'charge_name'">
            <template #right-icon>
              <div class="iconfont cus_icon">&#xe691;</div>
            </template>
          </select-one>
          <select-one ref="selectClientGroup" style="width:100%" label="客户渠道" :required="groupNecessary" :inputAlign="'left'"
            :allowedit="canEdit" :hasClearBtn="false" :target="'group'" :formObj="FormData" :formFld="'sup_group'"
            :formNameFld="'group_name'" @addClick="showEditGroup" @delClick="itemDel">
            <template #right-icon>
              <div class="iconfont cus_icon">&#xe691;</div>
            </template>
          </select-one>
          <!-- <van-field v-model="FormData.group_name" readonly v-bind:required="groupNecessary" label="客户渠道" @click="selectCustomergroup">
            <template #right-icon>
              <div class="iconfont cus_icon">&#xe691;</div>
            </template>
          </van-field> -->
          <select-one ref="selectRank" style="width:100%" label="客户等级" :required="levelNecessary" :inputAlign="'left'"
            :allowedit="canEdit" :hasClearBtn="false" :target="'rank'" :formObj="FormData" :formFld="'sup_rank'"
            :formNameFld="'rank_name'" @addClick="showEditRank" @delClick="itemDel">
            <template #right-icon>
              <div class="iconfont cus_icon">&#xe692;</div>
            </template>
          </select-one>
          <!-- <van-field v-model="FormData.rank_name" readonly label="客户等级" v-bind:required="levelNecessary" placeholder="请选择" @click="selectCustomerRank">
            <template #right-icon>
              <div class="iconfont cus_icon">&#xe692;</div>
            </template>
          </van-field> -->
          <van-field v-model="FormData.license_no" label="营业执照" placeholder="" />
          <YjSelectTree ref="selectTreeRef" :target="'region'" :originTreeData="originTreeData" :rootNode="rootNode"
            :allowEditing="true" :canEdit="canEdit" :confirmColor="confirmColor" :rangeKey="rangeKey"
            :idKey="idKey" :title="title" :sonNode="sonNode" :parentSelectable="parentSelectable" :popupHeight="'90%'"
            @handleConfirm="onRegionSelected" @getRootNode="getRootNode">
            <template #select-tree-content>
              <van-field v-model="FormData.other_region_name" readonly v-bind:required="regionNecessary" label="片区" />
            </template>
            <template #tree-edit-content>
              <div style="height: 40px;line-height: 40px;" v-show="canEdit" @click="getEditRegion">
                <van-icon name="ellipsis" :style="[{ color: confirmColor }]" size="25"
                  style="transform: rotate(-90deg);line-height: 40px;" />
              </div>
            </template>
          </YjSelectTree>
          <div class="acct_type_wrapper" style="padding: 10px 16px;position:relative;">
            <div class="acct_type_content">结算类型</div>
            <div class="acct_type_radio" style="margin-left: 40px;">
              <van-radio-group v-model="FormData.acct_type" direction="horizontal">
                <van-radio name="pay">现结</van-radio>
                <van-radio name="arrears">欠款</van-radio>
              </van-radio-group>
            </div>
          </div>
          <div class="acct_type_wrapper" style="padding: 10px 16px;position:relative;">
            <div class="acct_type_content">销售方式</div>
            <div class="acct_type_radio" style="margin-left: 40px;">
              <van-radio-group v-model="FormData.retail_wholesale_flag" direction="horizontal">
                <van-radio name="r">零售</van-radio>
                <van-radio name="w">批发</van-radio>
              </van-radio-group>
            </div>
          </div>
          <select-one ref="selectGroup" style="width:100%" label="结算方式" :inputAlign="'left'" :allowedit="canEdit"
            :disabled="(FormData.acct_type == '' || FormData.acct_type == null)" :hasClearBtn="false"
            :target="FormData.acct_type" :formObj="FormData" :formFld="'acct_way_id'" :formNameFld="'acct_way_name'">
            <template #right-icon>
              <div class="iconfont cus_icon">&#xe691;</div>
            </template>
          </select-one>
          <!-- <van-field v-model="FormData.acct_way_name" label="结算方式" /> -->
          <div v-if="canChangeStatus" class="acct_type_wrapper" style="padding: 10px 16px;position:relative;">
            <div class="acct_type_content">客户状态</div>
            <div class="acct_type_radio" style="margin-left: 40px;">
              <van-radio-group v-model="FormData.status" direction="horizontal">
                <van-radio name="1">正常</van-radio>
                <van-radio name="0">停用</van-radio>
              </van-radio-group>
              <!-- <van-swicth @change="statusChange" v-model="FormData.status_flag"></van-swicth> -->
            </div>
          </div>
          <div class="acct_type_wrapper" style="display:none; padding: 10px 16px;position:relative;">
            <div class="acct_type_content">是否包场</div>
            <div class="acct_type_radio" style="margin-left: 40px;">
              <van-checkbox  checked-color="#444444" v-model="FormData.relate_store"/>
            </div>
          </div>
          <div class="acct_type_wrapper" v-if="FormData.supcust_id !== '' && FormData.mall_type"
            style="padding: 10px 16px;">
            <div class="acct_type_content">小程序状态</div>
            <div class="acct_type_radio" style="margin-left: 25px;">
              <!-- <van-radio-group direction="horizontal" v-model="FormData.binding_user" disabled>
                <van-radio name="1">已绑定</van-radio>
                <van-radio name="0">未绑定</van-radio> -->
              <span v-if="Number(FormData.total_wx_user) != 0">已绑定{{ Number(FormData.total_wx_user) }}位用户</span>
              <span style="margin-left: 10px;text-decoration:underline;" @click="handleInvite">绑定</span>
              <!-- </van-radio-group> -->
            </div>
          </div>
          <van-field name="checkboxGroup" label="商城支付方式">
            <template #input>
              <van-checkbox-group v-model="mallClientPaymentType" direction="horizontal">
                <van-checkbox name="online">在线支付</van-checkbox>
                <van-checkbox name="offline">货到付款</van-checkbox>
              </van-checkbox-group>
            </template>
          </van-field>
          <div class="acct_type_wrapper" style="padding: 10px 16px;position:relative;">
            <div class="acct_type_content" :required="doorPicNecessary">门店照</div>
            <div class="face-container">
              <ul v-if="FormData.sup_door_photo" class="exhibition">
                <li>
                  <img @click="showImagePreviewFlag = true" :src="isBase64(FormData.sup_door_photo) ? FormData.sup_door_photo : obsPrefix + FormData.sup_door_photo" alt="" />
                  <div class="exhibition_deli iconfont">
                    <van-icon @click="FormData.sup_door_photo = ''" name="close" />
                  </div>
                </li>
              </ul>
              <ul @click="takePhoto" v-else class="exhibition">
                <li class="iconfont" style="line-height:88px; ">&#xe62e;</li>
              </ul>
            </div>
            <div v-if="!showOtherPhotoUpload" @click="showOtherPhotoUpload = true" style="display:flex;felx-direction:row;margin-left:40%;">
                <div style="margin-right:10px;">展开</div>
                <svg width="22px" height="22px">
                  <use :xlink:href="'#icon-add'"></use>
              </svg>
            </div>
            <div v-else @click="showOtherPhotoUpload = false" style="display:flex;felx-direction:row;margin-left:40%;">
                <div style="margin-right:10px;">收起</div>
                <svg width="22px" height="22px">
                  <use :xlink:href="'#icon-menu_close'"></use>
              </svg>
            </div>
          </div>
          <div v-if="showOtherPhotoUpload" class="acct_type_wrapper" style="padding: 10px 16px;">
            <div class="acct_type_content">其他</div>
            <div class="face-container">
              <ul v-if="FormData.sup_other_photo.length!=0" style="display:flex;flex-direction:row;"   class="exhibition">
                <li v-for="(other_photo,index) in FormData.sup_other_photo" :key="index">
                  <img @click="showOtherImagePreviewFlag = true" :src="isBase64(other_photo) ? other_photo : obsPrefix + other_photo" alt="" />
                  <div @click="deleteOtherPhoto(index)" class="exhibition_deli iconfont">
                    <van-icon name="close" />
                  </div>
                </li>
              </ul>
              <ul @click="takeOtherPhoto"  class="exhibition">
                <li class="iconfont">&#xe62e;</li>
              </ul>
            </div>

          </div>
        <div style="position:relative">
          <van-field v-model="FormData.sup_addr" clearable label="地址:" placeholder="" class="address-fld" :required="clientLocationNecessary">
            <template #right-icon>
              <div class="iconfont cus_icon" @click="loadPositionInfo" style="color:#f88; margin-right: 10px;" title="重新定位">
                &#xe778;
              </div>
              <div v-if="FormData.addr_lng && FormData.addr_lat" class="iconfont cus_icon" @click="testAddressGet" style="color:#1989fa;" title="获取地址">
                &#xe69c;
              </div>
            </template>
          </van-field>
          <div style="position:absolute;z-index:9999;background:#f0f0f0;height:200px;overflow-y:auto;" v-if="supAddrTips">
            <div style="padding:6px;border-bottom:1px solid;"
            @click="supAddrTipItemClick(item)"
             v-for="item,key in supAddrTips" :key="key">
              {{item.address+item.name}}
            </div>
          </div>

        </div>

          <van-field v-model="FormData.supcust_remark" label="备注:" placeholder="" class="address-fld" />
        </van-form>
        <div id="container" style="margin-top:20px;"></div>
      </div>

      <!-- <van-popup class="select_popup" v-model="showCustomerGroup" style="overflow: hidden !important" :style="{ height: '40%', width: '100%' }" position="bottom">
        <van-picker title="客户渠道" show-toolbar :columns="customerGroupList" value-key="group_name" @confirm="onConfirmCustomerChannel" @cancel="onCancelCustomerChannel" />
      </van-popup> -->
      <!-- <van-popup class="select_popup" v-model="showCustomerRank" style="overflow: hidden !important" :style="{ height: '40%', width: '100%' }" position="bottom">
        <van-picker title="客户等级" show-toolbar :columns="customerRankList" value-key="rank_name" @confirm="onConfirmCustomerRank" @cancel="onCancelCustomerRank" />
      </van-popup> -->
      <van-dialog v-model="DeleteRegionObj.deleteRegion" width="320px" :message="`确认删除${DeleteRegionObj.region_name}吗？`"
        closeOnPopstate confirmButtonColor="#eaaba2" closeOnClickOverlay show-cancel-button show-confirm-button
        @confirm="deleteRegionConfirm">
      </van-dialog>
      <van-popup @open="beforeOpenEditRegion" @closed="closedEditRegion" v-model="showAddOrEditRegion" round
        :style="{ padding: '20px', width: '310px', height: 'auto' }">
        <add-or-edit-region ref="handleRegion" :addForAll="addForAll" :regionTitle="regionTitle" :rootNode="rootNode"
          :nowSelectedRegion="nowSelectedRegion" :isNewRecord="isNewRecord" @saveRegionNode="saveRegionNode"
          @editRegionNode="editRegionNode" @closeAddOrEditRegion="closeAddOrEditRegion">
        </add-or-edit-region>
      </van-popup>
      <van-popup v-model="showHandleRegionOptions" round :style="{ padding: '20px', width: '170px', height: 'auto' }">
        <div v-if="nowSelectedRegion.name === '全部'" class="popover-wrapper">
          <div v-for="(action, popoverIndex) in allaction" @click="onSelect(action, true)"
            :class="popoverIndex === allaction.length - 1 ? 'popover-item noline' : 'popover-item'">
            <van-icon style="margin-right: 10px;" :name="action.icon" />
            <div>{{ action.text }}</div>
          </div>
        </div>
        <div v-else class="popover-wrapper">
          <div v-for="(action, popoverIndex) in actions" :key="'item_' + action.text" @click="onSelect(action, false)"
            :class="popoverIndex === actions.length - 1 ? 'popover-item noline' : 'popover-item'">
            <van-icon style="margin-right: 10px;" :name="action.icon" />
            <div>{{ action.text }}</div>
          </div>
        </div>
      </van-popup>
      <van-popup v-model="showCustomerSeller" round :style="{ padding: '20px', width: '310px', height: 'auto' }">
        <div class="seller-box">

        </div>
      </van-popup>
      <van-popup v-model="showCustomerGroup" round :style="{ padding: '20px', width: '310px', height: 'auto' }">
        <add-or-edit-group ref="handleGroup" :isNewRecord="isNewGroup" :groupTitle="groupTitle" @getAddGroup="getAddGroup"
          @hideAddGroup="hideAddGroup"></add-or-edit-group>
      </van-popup>
      <van-popup v-model="showCustomerRank" round :style="{ padding: '20px', width: '310px', height: 'auto' }">
        <add-or-edit-rank ref="handleRank" :isNewRecord="isNewRank" :rankTitle="rankTitle" @getAddRank="getAddRank"
          @hideAddRank="hideAddRank"></add-or-edit-rank>
      </van-popup>
      <we-chat-sale-sheet-qr-code ref="weChatSaleSheetQrCodeRef" :backgroundColor="'#fffcfa'" :sheet="FormData" />
      <van-popup v-model="showChangeLog" round position="bottom" :style="{ width: '100%', height: '80%' }">
        <div class="change-log-box">
          <h4 class="title">客户档案审核</h4>
          <div class="change-log-items" v-if="diff_describe">
            <span>修改内容</span>
            <template v-for="(item, key) in diff_describe">
              <div class="change-log-item"
                v-if="key !== 'sup_rank' && key !== 'sup_group' && key !== 'region_id' && key !== 'other_region' && key !== 'charge_seller' && key !== 'acct_way_id' && key.indexOf('addr_desc')==-1">
                <span style="margin-right:8px;width:120px;text-align: left;color:#646566;">{{ computeFieldLabel(key)
                }}</span>
                <div class="change-log-value" v-if="key == 'acct_type'"
                  style="display:flex;justify-content: space-between;width: 100%;">
                  <span class="newValue">{{ item.newValue == 'pay' ? '现结' : '欠款' }}</span>
                  <del class="oldValue" style="color:#aaa;">{{ item.oldValue == 'pay' ? '现结' : '欠款' }}</del>
                </div>
                <div class="change-log-value" v-else-if="key == 'sup_door_photo'"
                  style="display:flex;justify-content: space-between;width: 100%;">
                  <img @click="showImagePreviewFlag = true" style=" width: 79px;height: 79px;margin-right:10px;"
                    :src="isBase64(item.newValue) ? item.newValue : obsPrefix + item.newValue" alt="" />
                  <img @click="showImagePreviewFlag = true" style=" width: 79px;height: 79px;margin-right:10px;"
                    :src="isBase64(item.oldValue) ? item.oldValue : obsPrefix + item.oldValue" alt="" />
                </div>
                <div class="change-log-value" v-else style="display:flex;justify-content: space-between;width: 100%;">
                  <span class="newValue">{{ item.newValue }}</span>
                  <del class="oldValue" style="color:#aaa;">{{ item.oldValue }}</del>
                </div>
              </div>
              <div class="change-log-item"
                v-if="key.indexOf('addr_desc')>-1">
                <span style="margin-right:8px;width:120px;text-align: left;color:#646566;">{{ key.replace("addr_desc","收货地址") }}</span>
                <div class="change-log-value" style="display:flex;justify-content: space-between;width: 100%;">
                  <span class="newValue">{{ item.newValue }}</span>
                  <del class="oldValue" style="color:#aaa;">{{ item.oldValue }}</del>
                </div>
              </div>
            </template>
          </div>
          <div class="approve-mod" v-if="editLog">
            <!-- <button @click="showChangeLog = true" style="background-color: #07c160;color:#fff;border-radius: 5px;">查看修改记录</button> -->
            <div class="approve-btns">
              <button style="background-color: #ff8888;" @click="handleApprove(false)">不通过</button>
              <button style="background-color: #07c160;" @click="handleApprove(true)">通过</button>
            </div>
            <div class="approve-info">
              <span>编辑人:{{ editLog.edit_name }}</span>
              <span>{{ editLog.happen_time }}</span>
            </div>
            <div class="approve-brief">
              <van-field style="border:1px solid #ebedf0;" v-model="approve_brief" rows="2" autosize type="textarea"
                maxlength="50" placeholder="请输入审核备注" show-word-limit />
            </div>
          </div>
        </div>
      </van-popup>
    </div>
  </div>
</template>

<script>
import {
  NavBar,
  Form,
  Field,
  Popup,
  Picker,
  Toast,
  RadioGroup,
  Radio,
  Switch,
  Icon,
  Dialog,
  Button,
  Checkbox,
  CheckboxGroup
} from "vant";
import {
  GetMaxSupcustNum,
  GetClientById,
  GetRank,
  DeleteRegion,
  SaveSupcustsInfo,
  CheckData,
  GetAddressByPoi,
  DeleteGroup,
  DeleteRank,
  RadarSearchByTDT
} from "../../api/api";

import RegionSelection from "../components/RegionSelection";
import pinyinCode from "../../util/PinyinCode.js";
import TicketAccess from '../SaleSheet/TicketAccess';
import ImageUtil from '../service/Image';
import globalVars from '../../static/global-vars';
import TakePhoto from '../service/TakePhoto';
import MyPreviewImage from '../VisitRecord/MyPreviewImage.vue';
import { UseCommonMap } from '../service/MapLoader';
import YjSelectTree from "../components/yjTree/YjSelectTree.vue";
import AddOrEditRegion from '../components/AddOrEditRegion.vue';
import AddOrEditGroup from "../components/AddOrEditGroup.vue";
import AddOrEditRank from "../components/AddOrEditRank.vue";
import Tree from '../components/tree/tree.vue';
import Position from '../../components/Position.js'
// import TreePopup from "../components/tree/TreePopup.vue";
import SelectOne from "../components/SelectOne.vue";
import WeChatSaleSheetQrCode from "../components/wechatComponents/WeChatSaleSheetQrCode.vue";
import { parsePercent } from "echarts/lib/util/number";
import axios from 'axios';

export default {
  name: "CustomerArchivesSon",
  data() {
    return {
      FormData: {
        supcust_id: "",
        sup_name: "",
        py_str: "",
        sup_alias:"",
        boss_name: "",
        mobile: "",
        charge_seller: "",
        charge_name: "",
        sup_group: "",
        group_name: "",
        sup_rank: "",
        rank_name: "",
        region_id: "",
        other_region: "",
        region_name: "",
        other_region_name:"",
        sup_addr: "",
        supcust_no: "",
        addr_lng: "",
        addr_lat: "",
        license_no: "",
        status: '1',
        sup_door_photo: '',
        supcust_remark: '',
        approve_status: '',
        acct_type: null,// pay现结 arrears欠款
        retail_wholesale_flag: 'w',//销售方式  【r 零售】 【w 批发】 【null / "" 默认代表批发】   
        acct_way_id: "",
        acct_way_name: "",
        total_wx_user: '',
        mall_type: '',
        relate_store: false,
        sup_other_photo:[],
        mall_client_payment_type: ''
      },
      showOtherPhotoUpload:false,
      supAddrTips:[],
      useRequestTdt:true,
      originMarker:null,
      clickTipContent:"",
      supAddrWatchTimeOut:null,
      oldFormData: null,//用于判断审核人有没有直接更改
      editLog: null,
      diff_describe: null,
      variances: {},
      approve_brief: '',
      longitude: 116.2,
      latitude: 39.56,
      showCustomerSeller: false,
      showCustomerGroup: false,
      showCustomerRank: false,
      showCustomerArea: false,
      showImagePreviewFlag: false,
      showOtherImagePreviewFlag:false,
      customerGroupList: [],
      customerRankList: [],
      listData: [],
      addForAll: false,
      rangeKey: 'name',
      idKey: 'id',
      sonNode: 'subNodes',
      asPage: true,
      multipleCheck: false,
      parentSelectable: true,
      confirmColor: '#e3a2a2',
      title: '片区选择',
      motherTitle: '上级片区选择',
      rootNode: {},
      showHandleRegionOptions: false,
      nowSelectedRegion: {},
      popupflod: true,
      showAddOrEditRegion: false,
      allaction: [
        { text: '添加下级片区', icon: 'add-o' }
      ],
      actions: [
        { text: '编辑片区', icon: 'records' },
        { text: '添加下级片区', icon: 'add-o' },
        { text: '添加同级片区', icon: 'add-o' },
        { text: '删除片区', icon: 'delete-o' },
      ],
      isNewRecord: false,
      isNewGroup: false,
      isNewRank: false,
      regionTitle: '',
      groupTitle: '',
      rankTitle: '',
      DeleteRegionObj: {
        deleteRegion: false,
        region_name: '',
        region_id: '',
        operKey: ''
      },
      originTreeData: [],
      showChangeLog: false,
      msgId: "",
      sellers: [],
      mallClientPaymentType:["online", "offline"]
    };
  },
  
  async mounted() {
    this.$store.commit('customerPageOption', {
      type: 'see',
      formData: null
    })
    await UseCommonMap()
    // this.loadPositionInfo() 这里不应该直接获取地址，如果是打开一个已有的客户，不应该获取地址，只有创建新客户才需要。而且在onloadData里调用了loadPositionInfo，重复了
    this.onloadData();
  },
  components: {
    WeChatSaleSheetQrCode,
    "van-nav-bar": NavBar,
    "van-form": Form,
    "van-field": Field,
    "van-popup": Popup,
    "van-picker": Picker,
    "van-radio-group": RadioGroup,
    "van-radio": Radio,
    "van-swicth": Switch,
    "van-icon": Icon,
    "van-button": Button,
    "van-checkbox": Checkbox,
    [Dialog.Component.name]: Dialog.Component,
    "my-preview-image": MyPreviewImage,
    "tree": Tree,
    YjSelectTree,
    "add-or-edit-region": AddOrEditRegion,
    "add-or-edit-group": AddOrEditGroup,
    "add-or-edit-rank": AddOrEditRank,
    "select-one": SelectOne,
    "van-checkbox-group": CheckboxGroup,
  },
  watch: {
    'FormData.total_wx_user': {
      immediate: true,
      handler(obj) {
        console.log("FormData.total_wx_user", this.FormData.total_wx_user)
      }
    },
    'FormData.sup_addr': {
      immediate: true,
      handler(newVal,oldVal) {
        this.supAddrWatchTimeOut = setTimeout(()=>{
          console.log(this.supAddrWatchTimeOut)
         if(this.supAddrWatchTimeOut){
          clearTimeout(this.supAddrWatchTimeOut)
          
         }
         if(!newVal || (!oldVal && newVal) || (newVal === this.clickTipContent )){
            this.supAddrTips = []
            return
          }
          const jsonObj  = {
            keyWord:newVal,
            level:12,
            queryType:1,
            start:0,
            count:10,
            mapBound:"73.33,3.51,135.05,53.33"
          }
          RadarSearchByTDT({postStr:JSON.stringify(jsonObj),
                        type:"query",
                        resultType:1,
                        tk: globalVars.tianDiTuToken
                      
                      }).then(res=>{
                          this.supAddrTips  = res.data.pois
                          this.$forceUpdate()
                      })
        },500)

      }
    }
  },
  computed: {
    obsPrefix() {
      return globalVars.obs_server_uri + "/uploads"
    },
    canChangeStatus() {
      return hasRight('info.infoClient.delete')
    },
  
    canSyncTicketSys() {
      var operInfo=this.getOperInfo()
      return operInfo.setting != null && operInfo.setting.openTicketAccessSys === 'True'
    },
    bossNecessary() {
      var clientBossNecessary = getSettingValue('clientBossNecessary')
      return clientBossNecessary.toLowerCase() == "true"
    },
    mobileNecessary() {
      var clientMobileNecessary = getSettingValue('clientMobileNecessary')
      return clientMobileNecessary.toLowerCase() == "true"
    },
    clientRelateSeller() {
      var clientRelateSeller = getSettingValue('clientRelateSeller')
      return clientRelateSeller.toLowerCase() == "true"
    },
    regionNecessary() {
      var clientRegionNecessary = getSettingValue('clientRegionNecessary')
      return clientRegionNecessary.toLowerCase() != "false"
    },
    groupNecessary() {
      var clientGroupNecessary = getSettingValue('clientGroupNecessary')
      return clientGroupNecessary.toLowerCase() == "true"
    },
    levelNecessary() {
      var clientLevelNessary = getSettingValue('clientLevelNecessary')
      return clientLevelNessary.toLowerCase() == "true"
    },
    clientLocationNecessary() {
      var clientLocationNecessary = getSettingValue('clientLocationNecessary')
      return clientLocationNecessary.toLowerCase() == "true"
    },
    doorPicNecessary(){
      var doorPicNecessary = getSettingValue('doorPicNecessary')
      return doorPicNecessary.toLowerCase() == "true"
    },
    canAdd() {
      return hasRight('info.infoClient.create')
    },
    canEdit() {
      return hasRight('info.infoClient.edit')
    },
    approveAuthority() {
      let auth = getRightValue('info.infoClient.approve')
      if (auth == "false") {
        return false
      } else {
        return true
      }
    },
    computeFieldLabel() {
      return function (value) {
        let label = ""
        switch (value) {
          case "create_time":
            label = "创建时间"
            break;
          case "sup_name":
            label = "客户名称"
            break;
          case "py_str":
            label = "客户助记码"
            break;
            case "sup_alias":
            label = "客户别名"
            break;
          case "supcust_no":
            label = "客户编号"
            break;
          case "boss_name":
            label = "老板姓名"
            break;
          case "mobile":
            label = "联系电话"
            break;
          case "charge_name" || "charge_seller":
            label = "业务员"
            break;
          case "creator_name" || "creator_id":
            label = "创建人"
            break;
          case "group_name" || "sup_group":
            label = "客户渠道"
            break;
          case "rank_name" || "sup_rank":
            label = "客户等级"
            break;
          case "license_no":
            label = "营业执照"
            break;
          case "region_name" || "region_id":
            label = "片区"
            break;
          case "acct_type":
            label = "结算类型"
            break;
          case "acct_type_name" || "acct_way_name":
            label = "结算方式"
            break;
          case "status":
            label = "客户状态"
            break;
          case "sup_door_photo":
            label = "门店照"
            break;
          case "sup_door_photo":
            label = "门店照"
            break;
          case "sup_addr":
            label = "地址"
            break;
          case "supcust_remark":
            label = "备注"
            break;
          case "binding_user":
            label = "小程序状态"
            break;
          case "addr_desc":
            label = "收货地址"
            break;

          default:
            break;
        }
        return label
      }
    },
  },
  methods: {
    supAddrTipItemClick(item){
      this.useRequestTdt = false
      this.FormData.sup_addr = item.address+item.name
      this.clickTipContent = item.address+item.name
      var wsg84lng = Number(item.lonlat.split(",")[0])
      var wsg84lat = Number(item.lonlat.split(",")[1])
      var position = Position.gcj2bd(Position.wgs84Togc02(wsg84lng,wsg84lat))
      console.log(position)
      var tipPosition = new BMap.Point(position.longitude,position.latitude)
      this.map.centerAndZoom(tipPosition, 18);
      this.originMarker.setPosition(tipPosition)
      this.supAddrTips = []
    },
    async initMap(lng, lat) {
      var map = new BMap.Map("container");
      console.log(map);
      map.centerAndZoom(new BMap.Point(lng, lat), 18);
      map.enableScrollWheelZoom(true);
      var originMarker = new BMap.Marker(new BMap.Point(lng, lat), {
        enableDragging: true,
      });
    
      originMarker.addEventListener("dragend", (e) => {
        console.log(e);
        this.FormData.addr_lng = e.point.lng;
        this.FormData.addr_lat = e.point.lat;
        var geoc = new BMap.Geocoder()
        console.log(geoc)
        geoc.getLocation(e.point, (result) => {
          console.log(result)
          var addr = ''
          if (result.surroundingPois.length != 0) {
            addr = result.address + result.surroundingPois[0].title + "附近"
          }
          this.FormData.sup_addr = addr
        })
      });
      // 在地图上添加点标记
      map.addOverlay(originMarker);
      this.originMarker = originMarker
      this.map = map
    },
    // async getListData(callBack) {
    //   var operRegions = this.$store.state.operInfo.operRegions
    //   if (operRegions) {
    //     operRegions = JSON.stringify(operRegions)
    //   }
    //   if (!operRegions) {
    //     operRegions = ''
    //   }
    //   let params = { operRegions: operRegions }
    //   const res = await GetRegion(params)
    //   if (res.result === 'OK') {
    //     this.listData = res.data.subNodes
    //     this.rootNode = {
    //       id: res.data.id,
    //       name: res.data.name
    //     }
    //     let allCls= {
    //       company_Id: res.data.company_Id,
    //       id:res.data.id,
    //       mother_Id: '',
    //       name:"全部 ",
    //       subNodes: []
    //     }
    //     res.data.subNodes.unshift(allCls)
    //     this.listData=res.data.subNodes
    //     if(callBack) {
    //       callBack()
    //     }
    //   }
    // },
    deleteOtherPhoto(index){
      this.FormData.sup_other_photo.splice(index,1)
    },
    onloadData() {
      const supcustId = this.$route.query.supcust_id;
      let source = this.$route.query.source;
      let supcustNoSerial = ''
      this.FormData.sup_other_photo = []
      // console.log(this.$store.state.operInfo.setting)
      if (this.$store.state.operInfo.setting && this.$store.state.operInfo.setting.supcustNoSerial) {
        supcustNoSerial = this.$store.state.operInfo.setting.supcustNoSerial
      }

      if (!supcustId && supcustNoSerial == 'True') {
        let params = {
          operKey: this.$store.state.operKey,
        }
        GetMaxSupcustNum(params).then(res => {
          this.FormData.supcust_no = res
        })
      }
      if (supcustId) {
        GetClientById({ supcustId }).then((res) => {
          this.FormData = res.data;
          if (!this.FormData.retail_wholesale_flag) {
            this.FormData.retail_wholesale_flag = 'w';  // 默认勾选“批发”
          }
          if(this.FormData.relate_store) {
            this.FormData.relate_store = JSON.parse(this.FormData.relate_store.toLowerCase())
          }
          console.log('this.FormData.relate_store',this.FormData.relate_store);
          this.initOtherPhoto(res.data.sup_other_photo)
          this.oldFormData = JSON.parse(JSON.stringify(res.data))
          this.editLog = res.editLog ? res.editLog : null
          this.diff_describe = res.editLog && res.editLog.diff_describe ? JSON.parse(res.editLog.diff_describe) : null
          if (!res.data.sup_addr && res.data.addr_lng && res.data.addr_lat) {
            this.fillAddressFromBaidu(res.data.addr_lng, res.data.addr_lat)
          }
          this.initMap(res.data.addr_lng, res.data.addr_lat);
          if (!res.data.py_str) {
            this.FormData.py_str = pinyinCode(res.data.sup_name);
          }
          if (Number(res.data.total_wx_user)) {
            this.FormData.binding_user = '1'
            console.log("FormData", Number(this.FormData.total_wx_user))
          } else {
            this.FormData.binding_user = '0'
          }
          if (Number(res.data.total_wx_user)) {
            this.FormData.binding_user = '1'
            console.log("FormData", Number(this.FormData.total_wx_user))
          } else {
            this.FormData.binding_user = '0'
          }
          if (this.FormData.mall_client_payment_type) {
            this.mallClientPaymentType = this.FormData.mall_client_payment_type.split(',').map(payItem => payItem.trim());
          }
        });
      }
      else if (source === 'SupcustRadar') {
        const supcustInfo = JSON.parse(this.$route.query.supcustInfo)
        this.FormData.sup_name = supcustInfo.name
        this.FormData.addr_lng = supcustInfo.lng
        this.FormData.status = '1'
        this.FormData.addr_lat = supcustInfo.lat
        this.FormData.sup_addr = supcustInfo.address
        this.FormData.py_str = pinyinCode(supcustInfo.name);
        this.initMap(supcustInfo.lng, supcustInfo.lat);
      }
      else {
        this.FormData.status = '1'
        this.loadPositionInfo();
      }
      let regionInfoObj = this.$route.query.regionInfoObj;
      if (regionInfoObj) {
        this.FormData.region_name = regionInfoObj.regionName
        this.FormData.region_id = regionInfoObj.regionID
        this.FormData.other_region = regionInfoObj.other_region
      }
      this.msgId = this.$route.query.msgId ? this.$route.query.msgId : ''
      if (!this.FormData.retail_wholesale_flag) {
      this.FormData.retail_wholesale_flag = 'w';  // 默认勾选“批发”

    }
    },
    async fillAddressFromBaidu(addr_lng, addr_lat) {
      console.log("尝试从百度地图获取地址:", addr_lng, addr_lat)

      try {
        // 确保百度地图已加载
        await UseCommonMap()

        if (typeof BMap === 'undefined') {
          console.error("百度地图API未加载")
          this.$toast("地图服务未加载，无法获取地址")
          return
        }

        var point = new BMap.Point(addr_lng, addr_lat)
        var geoc = new BMap.Geocoder()

        return new Promise((resolve, reject) => {
          geoc.getLocation(point, (result) => {
            console.log("百度地图逆地理编码结果:", result)

            if (!result) {
              console.error("百度地图返回空结果")
              reject(new Error("获取地址失败"))
              return
            }

            var addr = ''
            if (result.surroundingPois && result.surroundingPois.length > 0) {
              addr = result.address + result.surroundingPois[0].title + "附近"
            } else {
              addr = result.address || ""
            }

            if (addr) {
              this.FormData.sup_addr = addr
              console.log("成功获取地址:", addr)
              this.$toast("已获取地址描述")
              resolve(addr)
            } else {
              console.warn("未获取到有效地址")
              reject(new Error("未获取到有效地址"))
            }
          }, (error) => {
            console.error("百度地图API调用失败:", error)
            reject(error)
          })

          // 添加超时处理
          setTimeout(() => {
            console.warn("获取地址超时")
            reject(new Error("获取地址超时"))
          }, 10000)
        })

      } catch (error) {
        console.error("获取地址过程出错:", error)
        this.$toast("获取地址失败: " + error.message)
      }
    },
    initOtherPhoto(sup_other_photo){
      if(sup_other_photo){
          this.FormData.sup_other_photo = sup_other_photo.split(",")
          if (this.FormData.sup_other_photo.length != 0) {
              this.FormData.sup_other_photo.map(async (one_other_photo,index)=>{
              this.FormData.sup_other_photo[index] =  await ImageUtil.image2Base64(this.obsPrefix + one_other_photo)
            })
          }
          }else{
            this.FormData.sup_other_photo = []
          }
    },
    async takePhoto() {
      let photo = await TakePhoto.takePhotos()
      photo = await ImageUtil.compress(photo)
      this.FormData.sup_door_photo = photo
    },
    async takeOtherPhoto(){
      let photo = await TakePhoto.takePhotos()
      photo = await ImageUtil.compress(photo)
      this.FormData.sup_other_photo.push(photo)
    },
    isBase64(imageUrl) {
      console.log(ImageUtil.isBase64(imageUrl))
      return ImageUtil.isBase64(imageUrl)
    },
    onInputClientName() {
      this.FormData.py_str = pinyinCode(this.FormData.sup_name);
    },
    // selectCustomergroup() {
    //   this.showCustomerGroup = true;
    //   let params = {};
    //   GetGroup(params).then((res) => {
    //     if (res.result === "OK") {
    //       this.customerGroupList = res.data;
    //     }
    //   });
    // },
    handleInvite() {
      // this.$refs.weChatSaleSheetQrCodeRef.handleOpenDialog()
      this.$refs.weChatSaleSheetQrCodeRef.handleGenerateWxQrCode()
    },
    // showEditGroup(type, item) {
    //   this.showCustomerSeller = tru
    // },
    hideAddGroup() {
      this.showCustomerGroup = false
    },
    showEditGroup(type, item) {
      this.showCustomerGroup = true
      if (type == 'add') {
        this.isNewGroup = true
        this.groupTitle = '添加渠道'
      } else {
        this.isNewGroup = false
        this.groupTitle = '编辑渠道'
        this.$nextTick(() => {
          this.$refs.handleGroup.editGroup(item)
        })
      }
    },
    getAddGroup(group) {
      this.$refs.selectClientGroup.startNewPage()
      if (this.isNewGroup) {
        Toast.success("添加成功")
      } else {
        Toast.success("修改成功")
      }
    },
    hideAddRank() {
      this.showCustomerRank = false
    },
    showEditRank(type, item) {
      this.showCustomerRank = true
      if (type == 'add') {
        this.isNewRank = true
        this.rankTitle = '添加客户等级'
      } else {
        this.isNewRank = false
        this.rankTitle = '编辑客户等级'
        this.$nextTick(() => {
          this.$refs.handleRank.editRank(item)
        })
      }
    },
    getAddRank(rank) {
      this.$refs.selectRank.startNewPage()
      if (this.isNewRank) {
        Toast.success("添加成功")
      } else {
        Toast.success("修改成功")
      }
    },
    itemDel(item, type) {
      let target = ""
      switch (type) {
        case "group":
          target = "渠道"
          break
        case "rank":
          target = "客户等级"
          break
        case "seller":
          target = "业务员"
          break
        case "pay":
          target = "pay"
          break
        case "arrears":
          target = "arrears"
          break
        default:
          target = ""
          break
      }
      Dialog.confirm({
        title: item.name,
        message: `确认删除该${target}吗？`,
        width: "320px"
      })
        .then(() => {
          let params = {
            gridID: "gridItems",
            operKey: this.$store.state.operKey,
            rowIDs: item.id
          }
          console.log("删除渠道", params)
          if (type === 'group') {
            DeleteGroup(params).then(res => {
              console.log(res)
              if (res.result === 'OK') {
                this.$refs.selectGroup.startNewPage()
                Toast.success("删除成功");
              } else if (res.result === "Error") {
                Toast.fail(res.msg);
              }
            }).catch(err => {
              console.log(err)
            })
            return
          }
          if (type === 'rank') {
            DeleteRank(params).then(res => {
              console.log(res)
              if (res.result === 'OK') {
                this.$refs.selectRank.startNewPage()
                Toast.success("删除成功");
              } else if (res.result === "Error") {
                Toast.fail(res.msg);
              }
            }).catch(err => {
              console.log(err)
            })
            return
          }
        })
    },
    onConfirmCustomerChannel(value) {
      this.showCustomerGroup = false;
      this.FormData.sup_group = value.group_id;
      this.FormData.group_name = value.group_name;
    },
    onCancelCustomerChannel() {
      this.showCustomerGroup = false;
      this.FormData.sup_group = "";
      this.FormData.group_name = "";
    },
    selectCustomerRank() {
      this.showCustomerRank = true;
      let params = {};
      GetRank(params).then((res) => {
        if (res.result === "OK") {
          this.customerRankList = res.data;
        }
      });
    },
    saveRegionNode(regionObj) {
      this.originTreeData = this.$refs.selectTreeRef.$refs.tree.getTreeList()
      this.$refs.selectTreeRef.getListData((_that) => {
        _that.$refs.tree.handleAddTreeNode(regionObj.region_id)
      })
      // console.log("saveRegionNode",regionObj)
    },
    editRegionNode(regionObj) {
      this.originTreeData = this.$refs.selectTreeRef.$refs.tree.getTreeList()
      // console.log("编辑节点",regionObj)
      this.$refs.selectTreeRef.getListData((_that) => {
        _that.$refs.tree.handleAddTreeNode(regionObj.region_id)
      })
    },
    closeAddOrEditRegion() {
      this.showAddOrEditRegion = false
    },
    motherRegionSelected(motherRegion) {
      this.showCustomerArea = false
      this.handleCloseMotherRegion()
      this.$refs.handleRegion.onMotherRegionSelect(motherRegion)
    },
    onConfirmCustomerRank(value) {
      this.showCustomerRank = false;
      this.FormData.rank_name = value.rank_name;
      this.FormData.sup_rank = value.rank_id;
    },
    onCancelCustomerRank() {
      this.showCustomerRank = false;
      this.FormData.rank_name = "";
      this.FormData.sup_rank = "";
    },
    getEditRegion() {
      this.showHandleRegionOptions = true
      this.nowSelectedRegion = this.$refs.selectTreeRef.$refs.tree.getChecked()[0]
    },
    onRegionSelected(objs) {
      // this.showCustomerArea = false //objs.isShow;
      console.log("多选", objs)
      if (objs.length > 0) {
        if (objs[0].id === this.rootNode.id) {
          Toast.fail(`${this.rootNode.name}片区不可选`)
          return
        }
        if (!objs[0].lastRank) {
          if (objs[0].path) {
            Toast.fail("父级片区不可选")
            return
          }
        }
        this.FormData.region_name = objs[0].name;
        this.FormData.other_region_name = objs[0].pathName
        this.FormData.region_id = (objs[0].id).toString();
        this.FormData.other_region = objs[0].path;
        // console.log(this.FormData)
      } else {
        this.FormData.region_id = "";
        this.FormData.region_name = "";
      }
      this.$refs.selectTreeRef.handleCancel()
      // if (objs.regionID) {
      //   this.FormData.region_name = objs.regionName;
      //   this.FormData.region_id = objs.regionID;
      //   this.FormData.other_region = objs.path;
      // } else {
      //   this.FormData.region_id = "";
      //   this.FormData.region_name = "";
      // }
    },
    treeCancel(e) {
      this.showCustomerArea = false
    },
    onSelect(action, flag) {
      console.log("action", action)
      this.showHandleRegionOptions = false
      this.addForAll = flag
      if (action.text === '编辑片区') {
        var canEdit = hasRight("info.infoRegion.edit")
        console.log("xxxxxxxxxxxxxxxxxxxx",canEdit)
    // 判断是否有权限
    if (!canEdit) {
      // 如果没有权限，直接返回，禁用点击
      Toast.fail('你没有权限操作此项');
      return;
    }
        this.regionEdit()
      } else if (action.text === '添加同级片区') {
        this.regionAdd("same level")
      } else if (action.text === '添加下级片区') {
        this.regionAdd("lower level")
      } else if (action.text === "删除片区") {
        this.regionDelete(this.nowSelectedRegion.id, this.nowSelectedRegion.name)
      }
      // console.log("item",item)
    },
    regionEdit() {
      this.isNewRecord = false
      this.regionTitle = '编辑片区'
      // this.showAddOrEditRegion=!this.showAddOrEditRegion
      this.showAddOrEditRegion = true
    },
    regionAdd(type) {
      if (type === "same level") {
        this.regionTitle = '添加同级片区'
      } else {
        this.regionTitle = '添加下级片区'
      }
      this.isNewRecord = true
      // this.showAddOrEditRegion=!this.showAddOrEditRegion
      this.showAddOrEditRegion = true
    },
    regionDelete(id, name) {
      this.DeleteRegionObj.deleteRegion = !this.DeleteRegionObj.deleteRegion,
        this.DeleteRegionObj.operKey = this.$store.state.operKey,
        this.DeleteRegionObj.region_id = id.toString(),
        this.DeleteRegionObj.region_name = name
    },
    async deleteRegionConfirm() {
      await DeleteRegion({
        class_id: this.DeleteRegionObj.region_id,
        operKey: this.DeleteRegionObj.operKey
      }).then(async res => {
        console.log(res)
        if (res.result === 'OK') {
          console.log("res.region_id", res.region_id)
          this.originTreeData = this.$refs.selectTreeRef.$refs.tree.getTreeList()
          this.$refs.selectTreeRef.getListData((_that) => {
            _that.$refs.tree.handleAddTreeNode()
          })
          // this.$refs.tree.handleDeleteTreeNode(Number(res.region_id))
        } else {
          await Toast.fail(res.result)
        }
      })
    },
    //打开弹窗之前open
    beforeOpenEditRegion() {
      if (!this.isNewRecord) {
        this.$nextTick(() => {
          this.$refs.handleRegion.editRegion(this.isNewRecord)
        })
      }
      else {
        this.$nextTick(() => {
          this.$refs.handleRegion.addRegion(this.isNewRecord)
        })
      }
    },
    closedEditRegion() {
      this.$nextTick(() => {
        this.$refs.handleRegion.clearRegionEdit()
      })
    },
    onSelectRegion() {
      // this.popupRegions = true
      console.log("this.$refs.selectTreeRef", this.$refs.selectTreeRef)
      this.$refs.selectTreeRef.handleInputClick()
    },
    getRootNode(node) {
      console.log("this.rootNode", node)
      this.rootNode = node
    },
    async saveCustomerProfile(approveFlag) {

      const that_ = this;
      let check = true;
      let formData = this.FormData;
      if (!this.canEdit && formData.supcust_id) {
        Toast("暂无编辑权限")
        return
      }
      isEmpty(formData.sup_name, "请输入客户名称");

      if (this.bossNecessary) {
        isEmpty(formData.boss_name, "请输入老板姓名");
      }
      if (this.mobileNecessary) {
        isEmpty(formData.mobile, "请输入联系电话");
      }
      if (this.groupNecessary) {
        isEmpty(formData.group_name, "请选择客户渠道");
      }
      if (this.regionNecessary) {
        isEmpty(formData.region_name, "请选择片区");
      }
      if (this.levelNecessary) {
        isEmpty(formData.rank_name, "请选择客户等级");
      }
      if (this.doorPicNecessary){
        isEmpty(formData.sup_door_photo, "请添加门头照图片");
      }
      if (this.clientLocationNecessary){
        isEmpty(formData.sup_addr, "请添加客户地址")
      }

      this.$store.commit('customerPageOption', {
        type: formData.supcust_id ? 'update' : 'create',
        formData: formData
      })
      function isEmpty(value, info) {
        if (!value) {
          Toast.fail(info);
          check = false;
        }
      }
      // if (this.FormData.sup_door_photo) {
      //   const isObsUrl = !this.isBase64(this.FormData.sup_door_photo)
      //   if (isObsUrl) {
      //     this.FormData.sup_door_photo = await ImageUtil.image2Base64(this.obsPrefix + this.FormData.sup_door_photo)
      //   }
      // }

      if (check) {
        console.log( this.FormData.sup_other_photo)
        let params = {
          operKey: this.$store.state.operKey,
          supcust_id: this.FormData.supcust_id,
          sup_name: this.FormData.sup_name,
          sup_alias:this.FormData.sup_alias,
          py_str: this.FormData.py_str,
          supcust_no: this.FormData.supcust_no,
          boss_name: this.FormData.boss_name,
          mobile: this.FormData.mobile,
          sup_addr: this.FormData.sup_addr,
          region_id: this.FormData.region_id,
          other_region: this.FormData.other_region,
          sup_group: this.FormData.sup_group,
          sup_rank: this.FormData.sup_rank,
          acct_type: this.FormData.acct_type,
          retail_wholesale_flag:this.FormData.retail_wholesale_flag,
          license_no: this.FormData.license_no,
          charge_seller: this.FormData.charge_seller,
          supcust_remark: this.FormData.supcust_remark,
          addr_lat: this.FormData.addr_lat,
          addr_lng: this.FormData.addr_lng,
          status: this.FormData.status,
          sup_door_photo: this.FormData.sup_door_photo,
          sup_other_photo: this.FormData.sup_other_photo,
          approve_status: "",
          approve_flag: "",
          approve_brief: "",
          receiverId: "",
          flow_id: "",
          msg_id: "",
          relate_store: this.FormData.relate_store,
          mall_client_payment_type: this.mallClientPaymentType.join(','),
        }
        if (approveFlag) {//审核
          params.approve_brief = this.approve_brief
          params.flow_id = this.editLog.flow_id
          params.receiverId = this.editLog.oper_id
          params.msg_id = this.msgId
          params.approve_status = "null"
          if (approveFlag.approve) {
            params.approve_flag = this.editLog.oper_action == 'CREATE' ? 'APPROVED_FROM_CREATE' : 'APPROVED_FROM_EDIT'
          } else {
            params.approve_flag = this.editLog.oper_action == 'CREATE' ? 'REFUSED_FROM_CREATE' : 'REFUSED_FROM_EDIT'
            //根据diff_describe把client档案恢复到原来的数据
            for (let key in this.diff_describe) {
              params[key] = this.diff_describe[key].oldValue
            }
          }
        } else {//新建&&编辑
          params.approve_status = "wait approve"
          if (this.FormData.supcust_id == "") {
            params.approve_flag = "CREATE"
          } else {
            params.approve_flag = "EDIT"
          }
        }

        // if (!approveFlag) {
        //   this.$store.commit('customerPageOption', {
        //     type: 'save',
        //     formData: this.FormData
        //   })
        // }


        if (this.approveAuthority && !approveFlag) {
          params.approve_status = "null"
          if (this.FormData.supcust_id == "") {
            params.approve_flag = "CREATE_AND_APPROVED"
          } else {
            params.approve_flag = "EDIT_AND_APPROVED"
          }
          this.checkData(params)
          // Dialog.confirm({
          //   title: '是否直接审核',
          //   message: '要审核吗?',
          //   confirmButtonText: '确定',
          //   cancelButtonText: '取消',
          //   width: "320px"
          // }).then(() => {
          //   params.approve_status = "null"
          //   if (this.FormData.supcust_id == "") {
          //     params.approve_flag = "CREATE_AND_APPROVED"
          //   } else {
          //     params.approve_flag = "EDIT_AND_APPROVED"
          //   }
          //   this.SaveClientsInfo(params)
          // }).catch(() => {
          //   this.SaveClientsInfo(params)
          // });
        } else {
          this.checkData(params)
        }
      }
    },
    checkData(params){
      CheckData(params).then((res)=>{
        if(res.result ==="OK"){
          this.SaveClientsInfo(params)
        }else{
          Dialog.confirm({
            title: res.msg,
            message: '是否继续?',
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            width: "320px"
          }).then(() => {

            this.SaveClientsInfo(params)
          })
        }
      })
    },
    SaveClientsInfo(params) {
      SaveSupcustsInfo(params).then((res) => {
        if (res.result !== "OK" || res.msg !== "") {
          Toast.fail(res.msg)
          return
        }
        if (!this.FormData.supcust_id) {
          this.FormData.supcust_id = res.new_id
          this.$store.commit('customerPageOption', {
            type: 'create',
            formData: null
          })
          if (window.g_fromPage && window.g_fromPage.addRow) {
            window.g_fromPage.addRow(this.FormData)
          }
          if (this.canSyncTicketSys) {
            const ticketAccess = new TicketAccess()
            ticketAccess.funAddSupcustToPiaoZhengTong(this.FormData)
          }
        }
        this.FormData.supcust_id = res.new_id;
        let source = this.$route.query.source;

        Toast.success("保存成功");
        this.jumpPathInfoBySource(source, res.new_id, params.sup_name,res.sup_door_photo);
      });
    },
    jumpPathInfoBySource (source, new_id, sup_name,sup_door_photo) {
      if (source === "VisitUser") {
        window.myReplacePage(this.$router, {
          path: "/Visit",
          query: { shop_id: new_id, shop_name: sup_name },
        });
      }
      else if (source === "SaleSheet") {
        //  window.myReplacePage(this.$router,{path:'/SaleSheet',query:{sheetType:'X'}})
        myGoBack(this.$router);
      }
      else if (source === "CustomerArchives_select") {
        window.myReplacePage(this.$router, {
          path: "/CustomerArchives",
          query: { shop_id: new_id, shop_name: sup_name ,sup_door_photo:sup_door_photo},
        });
      }
      else {
        myGoBack(this.$router);
      }
    },
    async loadPositionInfo() {
      console.log("开始获取位置信息...")

      try {
        const geoData = await Position.getPosition({
          getAddr: true,
          message: "需要定位权限来获取客户位置",
          key: "positionToCustom",
          timeout: 15000  // 增加超时时间
        })

        console.log("定位结果:", geoData)

        if(geoData.result === 'error'){
          console.error("定位失败:", geoData.msg)
          this.$toast(geoData.msg)
          return
        }

        // 设置坐标
        this.FormData.addr_lng = geoData.longitude;
        this.FormData.addr_lat = geoData.latitude;

        // 设置地址（如果获取到了）
        if (geoData.address && !this.FormData.sup_addr) {
          this.FormData.sup_addr = geoData.address;
          console.log("设置地址:", geoData.address)
        } else if (!geoData.address) {
          console.warn("未获取到地址描述，尝试手动获取...")
          // 如果没有获取到地址，尝试手动获取
          this.fillAddressFromBaidu(geoData.longitude, geoData.latitude)
        }

        // 初始化地图
        this.initMap(geoData.longitude, geoData.latitude);
        this.$toast("定位成功")

      } catch (error) {
        console.error("定位过程出错:", error)
        this.$toast("定位失败，请重试")
      }
    },

    // 测试地址获取功能
    async testAddressGet() {
      if (!this.FormData.addr_lng || !this.FormData.addr_lat) {
        this.$toast("请先进行定位")
        return
      }

      console.log("测试地址获取:", this.FormData.addr_lng, this.FormData.addr_lat)
      this.$toast("正在获取地址...")

      try {
        await this.fillAddressFromBaidu(this.FormData.addr_lng, this.FormData.addr_lat)
      } catch (error) {
        console.error("测试地址获取失败:", error)
        this.$toast("地址获取失败: " + error.message)
      }
    },
    async handleApprove(flag) {
      if (this.approveAuthority) {
        if (flag) {
          this.saveCustomerProfile({ approve: true })
        } else {
          this.saveCustomerProfile({ approve: false })
        }
      } else {
        Toast("暂无审核权限")
      }

    }
  },
};
</script>

<style lang="less" scoped>
#container {
  overflow: hidden;
  width: 100%;
  height: 300px;
  margin: 0;
  font-family: "微软雅黑";
}

@flex_acent_jc: {
  display: flex;
  align-items: center;
  justify-content: center;
}

;

@flex_acent_jb: {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

;

/deep/.van-cell {
  font-size: 15px;
}

/deep/ .van-cell:last-child::after {
  display: block;
}

.icon_filter {
  font-size: 15px;
}

.public_box2_t {
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;

}

.change-log-box {
  padding: 10px 0;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow: auto;

  .change-log-items {
    margin-bottom: 185px;

    &>span {
      display: flex;
      padding: 8px;
      text-align: left;
    }

    .change-log-item {
      position: relative;
      padding: 10px;
      border-bottom: 1px solid #eee;
      display: flex;
      align-items: center;
      width: 100%;
      box-sizing: border-box;
    }
  }

  .approve-mod {
    position: absolute;
    bottom: 0;
    padding: 10px;
    width: 100%;
    box-sizing: border-box;
    background-color: #fff;

    .approve-btns {
      padding: 10px 0;
      display: flex;
      justify-content: space-between;

      button {
        padding: 5px;
        border-radius: 8px;
        font-size: 14px;
        color: #fff;
      }
    }

    .approve-info {
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      color: #aaa;
    }
  }

}

.cus_icon {
  color: #646566;
  font-size: 26px;
}

.acct_type_wrapper {
  display: flex;
  .acct_type_content {
    color: #646566;
    font-size: 16px;
    line-height: 24px;
    white-space: nowrap;
  }
}
.face-container{
  display: flex;
  flex-direction: row;
}

.exhibition {
  // clear: both;
  padding-left: 10px;

  li {
    width: 79px;
    height: 79px;
    margin-right: 10px;
    margin-bottom: 10px;
    border: 1px solid transparent;
    font-size: 30px;
    color: #cccccc;
    position: relative;
    border-radius: 5px !important;
    border-color: #eee !important;

    img {
      width: 100%;
      height: 100%;
    }

    .exhibition_deli {
      position: absolute;
      right: -15px;
      top: -10px;
      color: #ee0a24;
      font-size: 20px;
      width: 30px;
      height: 30px;
    }
  }

  .iconfont {
    line-height: 8px;
  }

  .address-fld {

    .van-cell__title,
    .van-field__label {
      width: 40px;
    }
  }
}

.popover-wrapper {
  width: 100%;
  display: flex;
  font-size: 20px;
  flex-direction: column;
  box-sizing: border-box;
  justify-content: flex-start;

  .popover-item {
    border-bottom: 1px solid #d8cbcb;
    height: 50px;
    display: flex;
    padding: 0 10px;
    box-sizing: border-box;
    align-items: center;
    justify-content: flex-start;
  }

  .noline {
    border-bottom: 0;
  }

}
.choose_wrapper {
  display: flex;
  justify-content: space-between;
  padding: 12px 30px;
  position: relative;

  .choose_content {
    width: 100px;
    margin-right: 10px;
    text-align: left;
    color: #646566;
    font-size: 16px;
    line-height: 24px;
  }
}
</style>