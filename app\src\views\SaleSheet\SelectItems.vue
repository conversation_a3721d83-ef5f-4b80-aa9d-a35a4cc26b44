<template>
  <div class="pages">
    <!-- <van-nav-bar title="选择商品" left-arrow @click-left="goBackWithPromotions"/> -->
    <div class="serchWrapper">
      <div class="go_back" @click="goBackWithPromotions">
        <van-icon name="arrow-left" />
      </div>
      <div class="search_content">
        <van-search id="txtSearch" v-model="queryCondition.searchStr" left-icon placeholder="名称/条码/首拼"
          @input="onSearchStrChange"
          @click="onSearchStrClick">
          <template #right-icon>
            <!-- <van-icon name="search" /> -->
            <i class="iconfont">&#xe63c;</i>
          </template>
        </van-search>
      </div>
      <div class="search_layout" @click="btnScanBarcode_click" style="align-items: center;">
        <svg width="23px" height="23px" fill="#555" style="vertical-align: middle;">
          <use xlink:href="#icon-barcodeScan"></use>
        </svg>
      </div>
      <div class="search_layout" style="display: flex; justify-content: center; align-items: center;">
        <div class="iconfont icon_right" @click="orderByOptions = !orderByOptions"
        style="width:23px; height:23px; font-size: 20px; display: flex; align-items: center; vertical-align: middle; margin-top:4px;">
          &#xe690;
        </div>
      </div>
    </div>
    <div class="class_box">
      <div class="class_box_l">
        <van-skeleton title :row="10" :loading="classTreeloading"></van-skeleton>
        <SelectItems_Classes ref="classTree" :sheet="sheet" @getItemClassFinish="getItemClassFinish"
          :isOftenItemList="isOftenItemList" :sheetType="queryCondition.sheetType" @onClassSelected="onClassSelected" />
      </div>
      <div class="class_box_r">
        <van-skeleton title :row="15" :loading="itemTreeloading"></van-skeleton>
        <SelectItems_Items :canSeeStock="canSeeStock" ref="itemList" :sheet="sheet"
          :salePromotionCombines="salePromotionCombines" :salePromotionSecKills="salePromotionSecKills"
          :salePromotionFullDiscs="salePromotionFullDiscs" :salePromotionFullGifts="salePromotionFullGifts"
          :salePromotionCashPrizes="salePromotionCashPrizes" @handleItemClik="handleItemClik"
          @handleMultiSelectItem="handleMultiSelectItem" @handleOftenItemList="handleOftenItemList"
          @onNewQuery="onItemsQueried" @addNewTypeClass="addNewTypeClass"/>
      </div>
    </div>
    <!-- 底部操作区 -->
    <div class="class_footers">
      <div class="class_footer_left" @click="onStock" :style="{ 'color': queryCondition.showStockOnly ? '#000' : '#aaa' }">
        <div class="footer_left_top">
          <div>只看有库存(可用)</div>
          <div>
            <van-icon v-if="queryCondition.showStockOnly" name="checked" color="#000" />
            <div v-else class="footer_left_icon"></div>
          </div>
        </div>
        <div class="footer_left_bottom">

        </div>
      </div>
      <div class="class_footer_center">
        共{{ itemCount }}条
        <!-- <van-badge
          v-if="(shoppingCar.length ? shoppingCar.length : 0) !== 0"
          :content="shoppingCar.length ? shoppingCar.length : 0">
            <van-icon name="cart-o" color="#000" size="30" />
        </van-badge>
        <van-icon v-else name="cart-o" color="#aaa" size="30" /> -->
      </div>
      <div class="class_footer_right">
        <van-badge v-if="(shoppingCar.length ? shoppingCar.length : 0) !== 0"
          :content="shoppingCar.length ? shoppingCar.length : 0">
          <button class="selectOkBtn" style="margin-bottom: 4px;" @click="handleSelectOkBtnClick">选好了</button>
        </van-badge>
        <button v-else class="selectOkBtn" style="margin-bottom: 4px;" @click="handleSelectOkBtnClick">选好了</button>
      </div>
    </div>
    <van-popup class="addSheetRowOne" v-if="!multiSelectItemShow" v-model="popupAddSheetRow" duration="0.12"
      position="bottom" @close="addSheetRowClose">
      <div class="add_goods_wrapper">
        <div class="goods_attr_wrapper">
          <transition name="myslide-left">
            <div v-show="attrShowFlag" class="goods_attr" key="attrListAdd">
              <AttrSelect ref="arrtSelectToAddSheetRow" v-show="attrShowFlag" @handleAttrItemClick="handleItemClik" @handleAutoBtnOkClick="handleAutoBtnOkClick"
                key="attrlist_list" />
            </div>
          </transition>
          <transition name="myslide-right">
            <div v-show="!attrShowFlag" class="layout_content" @click="popupAddSheetRow = false" key="noAttrList"></div>
          </transition>
        </div>
        <div class="goods_add_wrapper" id="goodsAddWrapper">
          <div class="class_add_goods">
            <AddSheetRow ref="addSheetRow" :sheet="sheet" :stock="stock" :multiSelectFlag="multiSelectFlag" @handleHC="handleHC"
              @onItemClick="handleItemClik" @onSheetRowAdd_Attr="onSheetRowAdd_Attr"
              @popupAddSheetRowFalse="popupAddSheetRowFalse" @handleAttrShowFlagChange="handleAttrShowFlagChange"
              @onSheetRowAdd_OK="onSheetRowAdd_OK" />
          </div>
        </div>
      </div>
    </van-popup>
    <van-popup v-model="multiSelectItemShow" position="bottom" :style="{ height: '100vh', 'overflow': 'hidden' }">
      <MultiSelect ref="multiSelect" :sheet="sheet" :queryCondition="queryCondition" @onSheetRowAdd_OK="onSheetRowAdd_OK"
        @popupAddSheetRowFalse="popupAddSheetRowFalse" :onload_remarks="onload_remarks" @addFinish="addFinish"
        @clearShoppingCarAndGoBack="clearShoppingCarAndGoBack" :canSeeStock="canSeeStock"></MultiSelect>
    </van-popup>
    <van-popup class="van_popup" duration="0.4" v-model="orderByOptions" position="right" safe-area-inset-bottom
      :style="{ width: '85%', height: '100%' }">
      <van-nav-bar title="其他" />

      <!-- 复选框已废弃 -->
      <van-checkbox-group v-if="false" v-model="selectItemOrderResult">
        <van-cell v-for="(item, index) in selectItemOrderByOptions" :key="index" :name="item.key" clickable
          :title="item.text" @click="toggle(index)">
          <template #right-icon>
            <van-checkbox :name="item.key" ref="checkboxesOrderByOptions" />
          </template>
        </van-cell>
      </van-checkbox-group>
      <!-- <van-cell-group>
        <van-radio-group v-model="selectItemOrderResult">
          <van-cell v-for="(item, index) in selectItemOrderByOptions"  clickable :key="index"
            :title="item.text" @click = "selectItemOrderResult = item.value">
            <template #right-icon>
              <van-radio :name= "item.value"/>
            </template>
          </van-cell>
        </van-radio-group>
      </van-cell-group> -->

      <!-- <van-cell name="sUnitPriceCheck" title="显示小单位">
        <template #right-icon>
          <van-checkbox v-model="sUnitPriceCheck" @change="handleShowSunitPrice" />
        </template>
      </van-cell> -->
      <van-cell center title="显示小单位" class="sUnit-switch">
        <template #right-icon>
          <van-switch v-model="sUnitPriceCheck" 
           @change="handleShowSunitPrice"/>
        </template>
      </van-cell>
      <!-- 该UI已弃用 -->
      <div class="choose_wrapper" v-if = "false">
         <div class="choose_text">排序方式</div>
           <van-dropdown-menu>
             <van-dropdown-item v-model="sort_type" :options="sortOption" />
           </van-dropdown-menu>
      </div>
      <div class="choose_wrapper">
         <div class="choose_text">首选排序</div>
           <van-dropdown-menu>
             <van-dropdown-item v-model="sortFld1" :options="sortFld1Options" 
             @change="onSortFld1Change" 
             class="custom-dropdown-item" 
             :style="{ '--custom-van-cell-background-color': '#d3d3d3' }"/>
           </van-dropdown-menu>
      </div>
      <div class="choose_wrapper">
         <div class="choose_text">次选排序</div>
           <van-dropdown-menu>
             <van-dropdown-item v-model="sortFld2" :options="sortFld2Options" 
             :disable="!sortFld1" @change="onSortFld2Change"
             class="custom-dropdown-item"
             :style="{ '--custom-van-cell-background-color': '#d3d3d3' }"/>
           </van-dropdown-menu>
      </div>
      <div class="choose_wrapper">
         <div class="choose_text">末选排序</div>
           <van-dropdown-menu>
             <van-dropdown-item v-model="sortFld3" :options="sortFld3Options" 
             :disable="!sortFld1 || !sortFld2" @change="onSortFld3Change"
             class="custom-dropdown-item"
             :style="{ '--custom-van-cell-background-color': '#d3d3d3' }"/>
           </van-dropdown-menu>
      </div>
      <div class="btnWrapper">
        <button class="selectOkBtn" @click="saveOrderByOption">确定</button>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { NavBar, Search, Icon, Button, Popup, Toast, Badge, SwipeCell, Skeleton, Checkbox, CheckboxGroup, RadioGroup, Radio, Cell,DropdownMenu,DropdownItem,Switch } from "vant";
import SelectItems_Classes from "./SelectItems_Classes"
import SelectItems_Items from "./SelectItems_Items"
import LocalStorageManager from "../../utils/localStorageManager"
import MultiSelect from "./SaleSheetMultiSelect"
import AddSheetRow from "./AddSheetRow"
import AttrSelect from "./SaleSheetAttrSelect"
export default {
  name: 'SelectItems',
  provide() {
    return {
      queryCondition: this.queryCondition
    }

  },
  data() {
    return {
      name: '',
      value: '',
      orderByOptions: false,
      selectItemOrderByOptions: [
        { text: '有库存商品在前', value: 'stockFirst' }
      ],
      selectItemOrderResult: [],
      queryCondition: { sheetType: '', searchStr: '', branchID: '', classID: '', showStockOnly: false },
      popupAddSheetRow: false,
      addingItem: {},
      onload_remarks: [],
      submitBtn: 0,
      SelectItems_Itemss: [],
      backToList: [],
      itemListAccumulation: [],
      itemListDatas: [],
      itemListDatasSum: [],
      sheet: {},
      stock: {},
      itemCount: 0,
      curStock: {},
      sort_type:"default",
      sortFld1:"order_index",
      sortFld2:"recent_create",
      sortFld3:"none",
      sortFld1Options: [
        { text: '商品名称', value: 'item_name' },
        { text: '序号', value: 'order_index' },
        { text: '后创建在前', value: 'recent_create' },
        { text: '有库存在前',  value: 'stock' },
        { text: '无库存在前',  value: 'no_stock' },
        { text: '库存多在前',  value: 'more_stock' },
        { text: '库存少在前',  value: 'less_stock' },
        /* { text: '价格高在前', value: 'higherPriceFirst' },
        { text: '价格低在前', value: 'lowerPriceFirst' } */
      ],
      canSeeStock: true,
      orderItemSelectFlag: false,
      isOftenItemList: [],
      multiSelectItemShow: false,
      shoppingCar: [],
      attrShowFlag: false,
      multiSelectFlag: false,
      classTreeloading: true,
      itemTreeloading: true,
      salePromotionCombines: [],
      salePromotionSecKills: [],
      salePromotionFullDiscs: [],
      salePromotionFullGifts: [],
      salePromotionCashPrizes: [],
      sUnitPriceCheck:false
    }
  },
  components: {
    "van-nav-bar": NavBar,
    "van-search": Search,
    "van-icon": Icon,
    "van-button": Button,
    "SelectItems_Classes": SelectItems_Classes,
    "SelectItems_Items": SelectItems_Items,
    "van-popup": Popup,
    "AddSheetRow": AddSheetRow,
    "van-badge": Badge,
    "van-swipe-cell": SwipeCell,
    "van-cell": Cell,
    "MultiSelect": MultiSelect,
    "AttrSelect": AttrSelect,
    "van-skeleton": Skeleton,
    "van-checkbox": Checkbox,
    "van-checkbox-group": CheckboxGroup,
    "van-radio-group": RadioGroup,
    "van-radio": Radio,
    "van-switch": Switch,
    "van-dropdown-menu":DropdownMenu,
    "van-dropdown-item":DropdownItem

  },
  /*
  beforeRouteEnter(to, from, next) {
    
    next()
},*/
  mounted() {
    // 检查localStorage配额并清理
    //this.checkAndCleanLocalStorage();

    this.sUnitPriceCheck =  localStorage.getItem('sUnitPriceShow')=='true'?true:false
   // this.sheet = this.$store.state.currentSheet
   // this.sheet= window.g_curPage.myParams.sheet
   this.sheet=this.$route.params.sheet
    

    /* selectItemOrderResult,sortType参数已弃用 */
    this.selectItemOrderResult = this.$store.state.selectItemOrderResult
    this.sort_type = this.$store.state.queryItemSortType ?? "default"
    this.queryCondition.sortType = this.sort_type

    var sortFld1Value = "";
    var sortFld2Value = "";
    var sortFld3Value = "";
    if (',X,XD,'.indexOf(this.sheet.sheetType)>=0){
      sortFld1Value = localStorage.getItem('saleQueryItemSortFld1')
      sortFld2Value = localStorage.getItem('saleQueryItemSortFld2')
      sortFld3Value = localStorage.getItem('saleQueryItemSortFld3')
    } else {
      sortFld1Value = localStorage.getItem('buyQueryItemSortFld1')
      sortFld2Value = localStorage.getItem('buyQueryItemSortFld2')
      sortFld3Value = localStorage.getItem('buyQueryItemSortFld3')
    }
    if(sortFld1Value){
      this.sortFld1 = sortFld1Value
      if(sortFld2Value) this.sortFld2 = sortFld2Value
      if(sortFld3Value) this.sortFld3 = sortFld3Value
    
    }
    else if(this.sort_type){
        if (this.sort_type == "default") {
          this.sortFld1 = "order_index"
          this.sortFld2 = "recent_create" 
        }
        else if (this.sort_type == "item_name" || this.sort_type == "order_index"){
          this.sortFld1 = this.sort_type
        }
    }
     
    var query = this.$route.query
    
    this.setQueryConditionByQueryStr()
   
    // this.showStockOnly= this.$store`.state.saleShowStock||false;
    if(',X,XD,'.indexOf(this.sheet.sheetType)>=0){
      if(this.$store.state.saleShowStockOnly=='')
         this.$store.state.saleShowStockOnly=this.$store.state.showStockOnly || false
    
      this.queryCondition.showStockOnly = this.$store.state.saleShowStockOnly || false;
      //this.queryCondition.showStockOnly = this.$store.state.showStockOnly
    }
   
   /*  this.selectItemOrderByOptions.forEach(item => {
      delete this.queryCondition[item.key]
    }) */
    /* this.selectItemOrderResult.forEach(item => {
          this.queryCondition[item] = item
        }) */
    this.queryData()
    if (this.$store.state.trade_type == "HC") {
      this.$store.commit("trade_type", "X")
    }
    this.$nextTick(() => {
      if (this.$store.state.shoppingCarObj[this.sheet.sheetType]) {
        this.shoppingCar = this.$store.state.shoppingCarObj[this.sheet.sheetType]
      } else {
        this.shoppingCar = []
      }
    })
    let that = this
    this.classTreeloading = true
    this.itemTreeloading = true
    window.sayCode = function (result) {
      that.pageSayCode(result)
    }

    // Load promotions from route
    console.log('this.$route.params:', this.$route.params)
    
    this.onload_remarks = this.$route.params.onload_remarks

    const data = this.$route.params.data
    if (data.salePromotionCombines) {
      this.salePromotionCombines = data.salePromotionCombines
    }
    if (data.salePromotionSecKills) {
      this.salePromotionSecKills = data.salePromotionSecKills
    }
    if (data.salePromotionFullDiscs) {
      this.salePromotionFullDiscs = data.salePromotionFullDiscs
    }
    if (data.salePromotionFullGifts) {
      this.salePromotionFullGifts = data.salePromotionFullGifts
    }
    if (data.salePromotionCashPrizes) {
      this.salePromotionCashPrizes = data.salePromotionCashPrizes
    }
    
    this.classTreeloading = true
    this.itemTreeloading = true
  },
  activated() {
    this.selectItemOrderResult = this.$store.state.selectItemOrderResult

    
    //history.pushState(null, null, document.URL);

    var query = this.$route.query
    if (this.sheet && this.sheet.supcust_id) {
      // var a = query.searchStr && query.searchStr!=this.queryCondition.searchStr
      // var b = query.sheet.supcust_id !=  this.queryCondition.supcustID

      if ((query.searchStr && query.searchStr != this.queryCondition.searchStr) || this.sheet.supcust_id != this.queryCondition.supcustID) {
        this.setQueryConditionByQueryStr()
        this.queryCondition.classID = ''
        this.$refs.classTree.clearSelection()

        //this.queryCondition.showStockOnly = this.$store.state.showStockOnly
        /* this.selectItemOrderByOptions.forEach(item => {
          delete this.queryCondition[item.key]
        }) */
        /* this.selectItemOrderResult.forEach(item => {
          this.queryCondition[item] = item
        }) */
        this.queryData()
        if (this.$store.state.trade_type == "HC") {
          this.$store.commit("trade_type", "X")
        }
      }
    }
    this.$nextTick(() => {
      if (this.$store.state.shoppingCarObj[this.sheet.sheetType]) {
        this.shoppingCar = this.$store.state.shoppingCarObj[this.sheet.sheetType]
      } else {
        this.shoppingCar = []
      }
    })
    let that = this
    window.sayCode = function (result) {
      that.pageSayCode(result)
    }


    // this.setQueryConditionByQueryStr()
    // this.queryCondition.classID=''
    // this.$refs.classTree.clearSelection()
    // this.queryData()


  },
  watch: {
    '$store.state.currentSheet': function () { //监听vuex中userName变化而改变header里面的值
      //this.sheet = this.$store.state.currentSheet;
    },
    'queryCondition.showStockOnly': {
      handler: function (val, oldVal) {
        if(',X,XD,'.indexOf(this.sheet.sheetType)>=0){
          this.$store.state.saleShowStockOnly = val
        }
      },
      deep: true
    },
    multiSelectItemShow: {
      handler: function (val, oldVal) {
        if (val == false) {
          this.$refs.itemList.handleShoppingCarIsActive()
          //this.$refs.multiSelect.handleAttrShowFlagChange({});
          //this.$refs.multiSelect.$refs.swipe.prev();
          //this.$store.commit('activeSelectItem',{})
        }
      },
      deep: true
    },
    popupAddSheetRow: {
      handler: function (val, oldVal) {
        if (val == false) {
          //this.$store.commit('activeSelectItem',{})
          // console.log("嗯ee")
          this.$emit("handleSelectedSheetRowsEmitEvent")
          this.$refs.addSheetRow.closeshowPopoverFlag()
        }
      },
      deep: true
    }
  },
  beforeRouteLeave(to, from, next) {
    to.query.iSreades = false
    to.query.backToList = this.backToList
    //this.$store.commit('currentSheet',this.sheet);
    next()
  },
  computed: {
    sortFld2Options()
    {
      let filteredOptions = [...this.sortFld1Options, { text: '无', value: 'none' }]

      return this.optionsFilter(filteredOptions, this.sortFld1);
    },
    sortFld3Options()
    {
      let filteredOptions = [...this.sortFld2Options]
      return this.optionsFilter(filteredOptions, this.sortFld2);
    }
  },
  methods: {
    // 检查并清理localStorage配额
    checkAndCleanLocalStorage() {
      try {
        // 使用LocalStorageManager检查使用情况
        const usage = LocalStorageManager.getUsageInfo();
        console.log('localStorage使用情况:', usage);

        //this.cleanLocalStorageData();

        if (LocalStorageManager.needsCleanup()) {
          console.warn('localStorage使用量过高，开始智能清理...');
          this.cleanLocalStorageData();
        }
      } catch (e) {
        if (e.name === 'QuotaExceededError' || e.message.includes('quota')) {
          console.error('localStorage配额已满，强制清理');
          this.cleanLocalStorageData();
        }
      }
    },

    // 智能清理localStorage数据 - 解决缓存滥用
    cleanLocalStorageData() {
      try {
        // 先分析缓存滥用情况
        const analysis = LocalStorageManager.getCacheAbuseAnalysis();
        console.log('缓存滥用分析:', analysis);

        // 根据严重程度选择清理策略
        const cleanupOptions = {
          keepRecentSheets: analysis.severity === 'high' ? 1 : 2,  // 严重时只保留1个
          keepUserSettings: true,
          keepPrintSettings: true,
          keepLoginInfo: true,
          aggressiveMode: analysis.severity === 'high'  // 严重时启用激进模式
        };

        // 执行智能清理
        LocalStorageManager.smartCleanup(this.$store, cleanupOptions);

      } catch (e) {
        console.error('智能清理失败，执行紧急清理:', e);
        try {
          // 紧急清理
          LocalStorageManager.emergencyCleanup(this.$store);
          Toast.success('紧急清理完成');
        } catch (emergencyError) {
          console.error('紧急清理也失败:', emergencyError);
          Toast.fail('清理失败，请手动清理缓存');
        }
      }
    },


    //传给SelectItems_Items组件的自定义事件，用来添加交易方式类别
    addNewTypeClass(newClasses){
    //加延迟的目的是等待class组件中拿到classDataAttr，否则这边会报错
    setTimeout(() => {
        newClasses.forEach(item=>{
         this.$refs.classTree.classDataAttr[0].subNodes.splice(2,0,item)
      })
    }, 100);
       
    },
    handleShowSunitPrice() {
      localStorage.setItem('sUnitPriceShow', this.sUnitPriceCheck)
      console.log(this.sUnitPriceCheck)
      this.$refs.itemList.handleShowSunitPrice(this.sUnitPriceCheck)
    },
    goBackWithPromotions() {
      const params = {
        salePromotionCombines: this.salePromotionCombines,
        salePromotionSecKills: this.salePromotionSecKills,
        salePromotionFullDiscs: this.salePromotionFullDiscs,
        salePromotionFullGifts: this.salePromotionFullGifts,
        salePromotionCashPrizes: this.salePromotionCashPrizes
      }
      
      this.myGoBack(this.$router, params) // 路由返回
    },
    /* toggle(index) {
      this.$refs.checkboxesOrderByOptions[index].toggle();
    }, */
    optionsFilter(filteredOptions, excludeSort){
      switch (excludeSort) {
        case 'item_name':
          return filteredOptions.filter(option => option.value !== 'item_name');
        case 'order_index':
          return filteredOptions.filter(option => option.value !== 'order_index');
        case 'recent_create':
          return filteredOptions.filter(option => option.value !== 'recent_create');
        case 'stock':
        case 'no_stock':
          return filteredOptions.filter(option => !['stock', 'no_stock'].includes(option.value));
        case 'more_stock':
        case 'less_stock':
          return filteredOptions.filter(option => !['more_stock', 'less_stock'].includes(option.value));
        /* case 'higher_price':
        case 'lower_price':
          return filteredOptions.filter(option => !['higherPrice', 'lowerPrice'].includes(option.value)); */
        default:
          return filteredOptions;
      }
    },
    saveOrderByOption() {
      /* this.selectItemOrderByOptions.forEach(item => {
        delete this.queryCondition[item.key]
      }) */
      /* this.selectItemOrderResult.forEach(item => {
        this.queryCondition[item] = item
      }) */
      /* selectItemOrderResult,sort_type参数已弃用 */
      this.$store.commit('selectItemOrderResult', this.selectItemOrderResult)
      this.$store.commit("queryItemSortType",this.sort_type)

      if (',X,XD,'.indexOf(this.sheet.sheetType)>=0){
        localStorage.setItem('saleQueryItemSortFld1', this.sortFld1)
        localStorage.setItem('saleQueryItemSortFld2', this.sortFld2)
        localStorage.setItem('saleQueryItemSortFld3', this.sortFld3)
      } else {
        localStorage.setItem('buyQueryItemSortFld1', this.sortFld1)
        localStorage.setItem('buyQueryItemSortFld2', this.sortFld2)
        localStorage.setItem('buyQueryItemSortFld3', this.sortFld3)
      }
      
      this.orderByOptions = false
      this.queryData()
    },
    queryData(invokeBy) {
      try{
        //sort_type参数已弃用
        this.queryCondition.sortType = this.sort_type

        this.queryCondition.sortFld1 = this.sortFld1
        this.queryCondition.sortFld2 = this.sortFld2
        this.queryCondition.sortFld3 = this.sortFld3
        this.$store.commit('clearPartOfSheetState', {})
        this.$refs.itemList.newQuery(this.queryCondition, invokeBy)
      }
      catch(e){
        console.error('查询异常1:', e);

        // 处理localStorage配额超限错误
        if (e.name === 'QuotaExceededError' ||
            (e.message && e.message.includes('quota has been exceeded'))) {
          console.warn('检测到localStorage配额超限，自动清理...');
          this.cleanLocalStorageData();
          Toast.fail('存储空间已满已自动清理，请重新操作');
        } else if (e.message && e.message.includes('quota')) {
          Toast.fail('存储空间不足，请清理缓存');
        } else {
          alert('查询异常1:' + e.message);
        }
      }
      
    },
    pageSayCode(result) {
      this.queryCondition.searchStr = result;
      this.onSearchStrChange(result)
    },
    getItemClassFinish(val) {
      console.log('getItemClassFinish被调用了'+val);
      this.classTreeloading = !val
    },
    onClassSelected(classID) {
      try{
        this.queryCondition.classID = classID
        if (classID == "-1") {//点常用时没有必要用拼音检索
        //  this.queryCondition.searchStr = '' 
        }

        this.canSeeStock = window.hasBranchOperRight(this.queryCondition.branchID, 'query_stock')
        this.queryData("class")
      }
      catch(e){
        alert('查询异常' + e.message)
      }
      
    },
    setQueryConditionByQueryStr() {
      
     // if (query.sheet) {
        this.queryCondition.sheetType = this.sheet.sheetType
        this.queryCondition.branchID = this.sheet.branch_id
        this.queryCondition.branch_name = this.sheet.branch_name
        this.queryCondition.supcustID = this.sheet.supcust_id
        this.queryCondition.sup_name = this.sheet.sup_name
     // }
     var query = this.$route.query
      this.queryCondition.searchStr = query.searchStr
      this.queryCondition.querySearchStr = query.searchStr

    },
    /*
    onSearchCompositionStart(){
     this.isComposingChinese = true
     if (this.inputTimer) clearTimeout(this.inputTimer)
      this.inputTimer = 0
    },
    onSearchCompositionEnd(){
     this.isComposingChinese = false
    },*/
    onSearchStrChange(value) {
      console.log(value)
      var reg = new RegExp("'", "g")
      value = value.replace(reg, "")
      this.queryCondition.searchStr = value

      if (this.inputTimer) clearTimeout(this.inputTimer)
      this.inputTimer = 0
      if (!this.preSearchStr) this.preSearchStr = ''
      // if(this.queryCondition.searchStr ==this.preSearchStr) return
      this.preSearchStr = this.queryCondition.searchStr
      this.inputTimer = setTimeout(() => {
        this.inputTimer = 0
        if (this.queryCondition.classID == "-1") {
          this.queryCondition.classID = ''
          this.$refs.classTree.cancelSelect()
        }
        this.queryData("searchStr")
      }, 500)
    },
    onSortFld1Change(value)
    {
      this.checkAndUpdateSortFld(value, 'sortFld2');
      this.checkAndUpdateSortFld(value, 'sortFld3');
      this.sortFld1 = value;
    },
    onSortFld2Change(value)
    {
      this.checkAndUpdateSortFld(value, 'sortFld3');
      this.sortFld2 = value;
    },
    onSortFld3Change(value)
    {
      this.sortFld3 = value;
    },
    checkAndUpdateSortFld(value, fieldToCheck) {
      const groups = {
          no_stock: ['stock'],
          stock: ['no_stock'],
          more_stock: ['less_stock'],
          less_stock: ['more_stock']
      };
      if (value === this[fieldToCheck] || (groups[value] && groups[value].includes(this[fieldToCheck]))) {
        this[fieldToCheck] = 'none';
      }
    },
    onSearchStrClick() {
      this.queryCondition.searchStr = ''
      this.preSearchStr = ''
    },
    onloadSumBtn() {
      this.submitBtn = this.$store.state.itemList.length;
    },
    onStock() {
      this.queryCondition.showStockOnly = !this.queryCondition.showStockOnly
      this.queryData()
    },
    onItemsQueried(data) {
      this.itemCount = data.itemCount
      this.itemTreeloading = false
    },
    handleOftenItemList(data) {
      this.isOftenItemList = data
    },
    handleItemClik(item, distinctStockFlag = '') {
      // 此处处理某些问题,进行中转
      if (document.activeElement.id == 'txtSearch') {
        document.all.txtSearch.blur();
      }
      this.handleItemToAddSheetRow(item, distinctStockFlag)
    },
    handleAutoBtnOkClick() {
      if(this.attrShowFlag) {
        this.$refs.addSheetRow.btnOK_clicked(null);
      }
    },
    handleItemToAddSheetRow(item, distinctStockFlag = '') {
      console.log(item)
      let that = this
      if (this.queryCondition.searchStr && this.queryCondition.searchStr.length >= 13) {
        item.scanBarcode = this.queryCondition.searchStr
      }
      var sheetItem = {
        itemUnitsInfo: item,
        supcust_id: this.queryCondition.supcustID,
        sup_name: this.queryCondition.sup_name,
        branch_id: this.queryCondition.branchID,
        branch_name: this.queryCondition.branch_name,
        onload_remarks: this.onload_remarks,
        stock: item.stock,
        sheet: this.sheet,
        selectDistinctStockFlag: distinctStockFlag
      }
      that.attrShowFlag = this.$store.state.attrShowFlag
      setTimeout(() => {
        if (!that.attrShowFlag) {
          that.$store.commit("activeSelectItem", item)
          let distinctStock = false

          if (item.mum_attributes) {
            if (!item.mum_attributes.forEach) item.mum_attributes = JSON.parse(item.mum_attributes)
            if (item.mum_attributes.find(attr => attr.distinctStock)) {
              distinctStock = true
            }
          }
          that.$store.commit("distinctStockFlag", distinctStock)
        }
      }, 50);

      this.popupAddSheetRow = true

      setTimeout(() => {
        console.log(sheetItem)
        that.$refs.addSheetRow.loadData(sheetItem)
        that.$store.commit("HRItem", sheetItem)
      }, 51);
    },
    onBtnSunmber() {
      this.submitBtn = this.SelectItems_Itemss.length
    },
    onSheetRowAdd_OK() {
      this.popupAddSheetRow = false
    },
    handleHC() {
      this.popupAddSheetRow = true;
      let data = JSON.parse(JSON.stringify(this.$store.state.HRItem));
      data.itemUnitsInfo.bReturn = false;
      data.itemUnitsInfo.unitPriceRows.forEach(item => {
        if (Math.abs(item.quantity) > 0) {
          item.quantity = Math.abs(item.quantity);
          item.sub_amount = Math.abs(item.sub_amount);
          item.remark = "换出";
          item.remarkid = "";
        }
      })
      data.itemUnitsInfo.unitGiveRows.forEach(item => {
        if (Math.abs(item.quantity) > 0) {
          item.quantity = Math.abs(item.quantity);
          item.sub_amount = Math.abs(item.sub_amount);
          item.remark = "换出";
          item.remarkid = "";
        }
      })
      this.$refs.addSheetRow.loadData(data);
    },
    addSheetRowClose() {
      if (!this.$store.state.btnSaveClick && this.$refs.addSheetRow.trade_type == "HC") {
        Toast('未确认换出商品，将删除已换入商品')
        this.sheet.sheetRows.splice(-1, 2);
      }
    },
    getScanBarResult(unit_type = '') {
      return new Promise((resolve, reject) => {
        const supportFormat =  {
                Code128: true,
                Code39: true,
                Code93: true,
                CodaBar: true,
                DataMatrix: true,
                EAN13: true,
                EAN8: true,
                ITF: true,
                QRCode: false,
                UPCA: true,
                UPCE: true,
                PDF417: true,
                Aztec: true,
              }
        const androidconfig = {
              barcodeFormats:supportFormat,
              beepOnSuccess: true,
              vibrateOnSuccess: false,
              detectorSize: .6,
              rotateCamera: false,
          // preferFrontCamera: false, // iOS and Android
          // showFlipCameraButton: true, // iOS and Android
          // showTorchButton: true, // iOS and Android
          // torchOn: false, // Android, launch with the torch switched on (if available)
          // saveHistory: true, // Android, save scan history (default false)
          // prompt: "Place a barcode inside the scan area", // Android
          // resultDisplayDuration: 1000, // Android, display scanned text for X ms. 0 suppresses it entirely, default 1500
          // // formats: "", // default: all but PDF_417 and RSS_EXPANDED
          // orientation: "portrait", // Android only (portrait|landscape), default unset so it rotates with the device
          // disableAnimations: true, // iOS
          // disableSuccessBeep: false // iOS and Android
        }
        const iosconfig = {
          preferFrontCamera: false, // iOS and Android
          showFlipCameraButton: false, // iOS and Android
          showTorchButton: true, // iOS and Android
          torchOn: false, // Android, launch with the torch switched on (if available)
          disableAnimations: false, // iOS
          disableSuccessBeep: false // iOS and Android
        }
        let barcodeScannerAndroidConfig = {
          preferFrontCamera: false, // iOS and Android
          showFlipCameraButton: true, // iOS and Android
          showTorchButton: true, // iOS and Android
          torchOn: false, // Android, launch with the torch switched on (if available)
          saveHistory: true, // Android, save scan history (default false)
          prompt: "Place a barcode inside the scan area", // Android
          resultDisplayDuration: 1000, // Android, display scanned text for X ms. 0 suppresses it entirely, default 1500
          // formats: "", // default: all but PDF_417 and RSS_EXPANDED
          orientation: "portrait", // Android only (portrait|landscape), default unset so it rotates with the device
          disableAnimations: true, // iOS
          disableSuccessBeep: false, // iOS and Android
          barcodeFormats:supportFormat
        }

        const config = isiOS ? iosconfig : (typeof cordova.plugins.mlkit?.barcodeScanner == 'undefined' ? androidconfig : barcodeScannerAndroidConfig)
        const plugin = isiOS || typeof cordova.plugins.mlkit?.barcodeScanner == 'undefined' ? cordova.plugins.barcodeScanner : cordova.plugins.mlkit.barcodeScanner
        if (isiOS) {
          plugin.scan(
            async (result) => {
              const res = { unit_type, code: result.text, format: result.format }
              resolve(res)
            },
            async (res) => {
              reject(res)
            },
            config
          );

        } else {
          const useOldPlugin = typeof cordova.plugins.mlkit?.barcodeScanner == 'undefined'
          console.log(useOldPlugin)
          if (useOldPlugin) {
            plugin.scan(

              async (result) => {
                const res = { unit_type, code: result.text, format: result.format }
                resolve(res)
              },
              async (res) => {
                reject(res)
              },
              config
            );
          } else {
            plugin.scan(
              config,
              async (result) => {
                const res = { unit_type, code: result.text, format: result.format }
                resolve(res)
              },
              async (res) => {
                reject(res)
              }
            );
          }
        }
      })
    },
    async btnScanBarcode_click() {
      if (this.sheet.approve_time) return
      const result = await this.getScanBarResult()
      /*const SUPPORT_CODE_TYPE=['EAN_13',"EAN_8","ITF"]
      if (SUPPORT_CODE_TYPE.indexOf(result.format)===-1) {
        this.$toast("请扫描8、13或14位条码")
        return
      }*/
      // this.searchStr = result.code;
      // this.btnClassView_click();
      this.pageSayCode(result.code)
    },
    onSheetRowAdd_Attr(obj) {
      // 属性下一个输入
      let trade_type = this.$store.state.trade_type
      if (trade_type !== "HR") {
        this.$refs.arrtSelectToAddSheetRow.nextItemClick(obj)
      }
    },
    /**
     * 多选操作
     *  */
    handleMultiSelectItem(obj) {
      obj.joinTime = new Date().getTime()  // 作为列表key使用
      this.$store.commit('shoppingCarObj', {
        sheetType: this.sheet.sheetType,
        item: obj
      })
      this.shoppingCar = []
      this.shoppingCar = this.$store.state.shoppingCarObj[this.sheet.sheetType]
      this.$forceUpdate()
    },
    //选用了
    handleSelectOkBtnClick() {
      if (this.shoppingCar.length === 0) {
        this.goBackWithPromotions()
        this.addFinish()
      } else {
        this.$store.commit('multiSelectOpenFlag', true)
        this.handleMultiBtnClick()
      }

    },
    handleMultiBtnClick() {
      this.multiSelectItemShow = true
      this.$store.commit("attrShowFlag", false)
      setTimeout(() => {
        this.$store.commit("shoppingCarFinish", false)
        this.$refs.multiSelect.handleFirstSelect()
      }, 350);
    },
    handleMultiItemClick(obj) {
      this.handleItemClik(obj)
    },
    addFinish() {
      this.$store.commit("shoppingCarObj", {
        clearFlag: true,
        sheetType: this.sheet.sheetType
      })
      this.shoppingCar = []
      this.multiSelectItemShow = false
    },
    handleAttrShowFlagChange() {
      if (!this.attrShowFlag) {
        this.$refs.arrtSelectToAddSheetRow.initData(
          () => {
            this.attrShowFlag = !this.attrShowFlag
            this.$store.commit("attrShowFlag", this.attrShowFlag)
          }
        )

      } else {
        this.attrShowFlag = !this.attrShowFlag
        if (!this.multiSelectFlag) {
          this.popupAddSheetRowFalse()
        } else {
          this.handleItemClik(this.$store.state.activeSelectItem)
        }
      }




      // this.attrShowFlag = !this.attrShowFlag
      // this.$store.commit("attrShowFlag",this.attrShowFlag)
      // if(this.attrShowFlag) {
      //   this.$refs.arrtSelectToAddSheetRow.initData()
      //   // setTimeout(() => {
      //   //   this.$refs.arrtSelectToAddSheetRow.handleFirstItemSelect()
      //   // }, 150);
      // } else {
      //   if(!this.multiSelectFlag) {
      //     this.popupAddSheetRowFalse()
      //   } else {
      //      this.handleItemClik(this.$store.state.activeSelectItem)
      //   }
      // }
    },

    clearShoppingCarAndGoBack() {
      this.goBackWithPromotions()
      this.addFinish()
      this.popupAddSheetRowFalse()
    },
    popupAddSheetRowFalse() {
      this.popupAddSheetRow = false
      this.multiSelectItemShow = false
      this.$store.commit("clearPartOfSheetState", '')
      this.$emit("handleSelectedSheetRowsEmitEvent")
      //this.$emit("handleSelectedSheetRowsEmitEvent")
    }

  }
}
</script>

<style lang="less" scoped>
/deep/ .search_content .van-search .van-field__body {
  width: 95%;
}

/deep/ .van-overlay {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4) !important;
}

@flex_acent_jbw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

;

@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
}

;

@flex_acent_jend: {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

;

.serchWrapper {
  // background-color: #eee;
  background-image: linear-gradient(to bottom, #fff 0%, #eee 100%);
  height: 46PX;
  //border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;

  .go_back {
    flex: 1;
  }

  .search_content {
    flex: 6;

    .iconfont {
      font-size: 12px;
    }
  }

  .search_layout {
    flex: 1;
  }
}

.van-search {
  background: transparent;
  padding: 0;

  .van-search__content {
    background: transparent;
  }

  /deep/.van-field__body {
    border-bottom: 1PX solid #e6e6e6 !important;
    width: 80%;
    margin: auto;
    font-size: 15PX;
  }

  /deep/#txtSearch {
    height: 20PX;
  }

  /deep/.van-field__right-icon {
    height: 24PX;
    line-height: 24PX;
  }
}

.iconfont {
  color: #bbb;
}

.class_box {
  height: calc(100% - 54PX - 46PX - 10PX);
  @flex_acent_jbw();

  .class_box_l {
    width: 30%;
    height: 100%;
    background: #eee;
    overflow-x: hidden;
    overflow-y: auto;
  }

  .class_box_l::-webkit-scrollbar {
    display: none;
  }

  .class_box_r {
    width: 70%;
    height: 100%;
    background: #fff;
  }
}

.multi_item_wrapper {
  width: 100%;
  height: 100%;
  background: #fff;
}

.class_footers {
  padding-top: 5PX;
  height: 40PX;
  border-top: 2PX solid #f2f2f2;
  background: #ffffff;
  display: flex;
  z-index: 1;

  .class_footer_left {
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .footer_left_top {
      width: 100%;
      display: flex;
      justify-content: center;

      .van-icon {
        font-size: 18PX;
      }

      .footer_left_icon {
        width: 15PX;
        height: 15PX;
        border-radius: 50%;
        border: 1PX solid #aaa
      }

      :nth-child(1) {
        font-size: 16PX;
      }

      :nth-child(2) {
        padding: 3PX 5PX;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .footer_left_bottom {
      font-size: 15PX;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .class_footer_center {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #aaa;
    font-size: 15PX;
  }

  .class_footer_right {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;

    .selectOkBtn {
      background-color: #fdd3d4;
      height: 36PX;
      width: 80PX;
      font-size: 16PX;
      margin-bottom: 4PX;
      border-radius: 10PX;
      // font-weight: bolder;
      color: #000;
      padding: 0;
    }
  }
}

.custom_h5 {
  height: 40px;
  line-height: 40px;
  font-size: 16px;
  color: #333333;
  background: #ffffff;
  color: #333333;
  margin: 0px;
  position: relative;

  .icon_h5 {
    position: absolute;
    height: 40px;
    width: 40px;
    right: 0;
    top: 0;
    display: block;
    @flex_a_j();
    font-size: 20px;
  }
}

.addSheetRowOne {
  background-color: transparent !important;
  height: 100%;
  width: 100%;
  overflow: hidden;

  .add_goods_wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: flex-end;

    .goods_attr_wrapper {
      background-color: transparent;
      display: flex;
      width: 100%;
      flex: 1;
      flex-direction: column;
      overflow-y: hidden;

      .goods_attr {
        background-color: #fff;
        height: 100%;
      }

      .layout_content {
        width: 100%;
        flex: 1;
      }
    }

    .goods_add_wrapper {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      justify-content: flex-end;

      .class_add_goods {
        width: 100%;
        height: 100%;
        background-color: #fff;
        //padding-top: 5px;
        padding: 5px 0 0;
      }
    }
  }
}

.myslide-right-enter-active,
.myslide-right-leave-active,
.myslide-left-enter-active,
.myslide-left-leave-active {
  transition: all 0.4s ease;
}

.myslide-right-enter {
  transform: translateX(-100%);
}

.myslide-right-leave-active {
  transform: translateX(0%);
}

.myslide-left-enter {
  transform: translateX(100%);
}

.myslide-left-leave-active {
  transform: translateX(100%);
}
.sUnit-switch{
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #ddd;
  width: 95%;
  margin: 0 auto;
}
.choose_wrapper{
  display: flex;
  //flex-direction: row;
  align-items: center;
  //justify-content: space-between;
  justify-content: flex-start;
  padding: 5px 20px;
  border-bottom: 1px solid #ddd;
  width: 80%;
  margin: 0 auto;
}
.choose_text {
  font-size: 16px; 
  margin-right: 10px;
  white-space: nowrap;
}

.custom-dropdown-item {
/*   min-width: 150px;  
  min-height: 20px;  */
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 0;
}
/* .custom-dropdown-item .van-dropdown-item__option {
            background-color: #d3d3d3 !important; 
}
.custom-dropdown-item .van-dropdown-item_option--active {
    background-color: #a9a9a9 !important; 
} */
.van-dropdown-menu {
  flex: 1;
}
.van-dropdown-item {
  width: 100%;
  border: none;
  padding: 5px 10px;
  margin: 0; 
  border-radius: 8px;
}
.van-dropdown-item--down {
  width: 100%;
  border: none;
  padding: 5px 10px;
  margin: 0; 
  border-radius: 8px;
}
::v-deep .custom-dropdown-item .van-cell {
  background-color:  #f0f0f0 !important;
}
::v-deep .van-dropdown-menu .custom-dropdown-item .van-dropdown-item--down .van-overlay {
    background-color:  transparent !important;
}
::v-deep .van-dropdown-menu .van-dropdown-menu__bar .van-dropdown-menu__title::after {
  display: none;
}
.van_popup {
  display: flex;
  flex-direction: column;
  width: 100%;

  .btnWrapper {
    width: 100%;
    box-sizing: border-box;
    padding: 0 20px;
    display: flex;
    position: fixed;
    bottom: 50px;
    justify-content: flex-end;

    .selectOkBtn {
      background-color: #fdd3d4;
      height: 36px;
      width: 80px;
      border-radius: 10px;
      // font-weight: bolder;
      // color: #000;
      padding: 0;
    }
  }
}
</style>
