
<template>
  <div ref="pages" class="pages" id="pages" style="">
    <my-preview-image v-if="showImagePreviewFlag" @closePreviewEvent="showImagePreviewFlag = false"
      :images="[previewItemImageUrl]"></my-preview-image>
    <!-- 标题栏start -->
    <div ref="publicBox2" @click='title_click'>
      <van-nav-bar left-arrow safe-area-inset-top :title="
      sheet.sheetType == 'X' ? '销售单':
          sheet.sheetType == 'T' ? '退货单':
            sheet.sheetType == 'XD'? '销售订单':
              sheet.sheetType == 'TD'? '退货订单':
                sheet.sheetType == 'CG'? '采购单':
                  sheet.sheetType == 'CT'? '采购退货单':
                    sheet.sheetType == 'CD'? '采购订单':
                      sheet.sheetType == 'DH'? '定货会':
                        sheet.sheetType == 'BQ'? '标签打印单': ''" @click-left.stop="stayOrLeave">
        <template #right>
          <!-- <svg style="margin:2px 2px 0 20px " @click="downloadPic" width="30px" height="30px" stroke-width="1.3" class="black">
            <use :xlink:href="'#icon-download'"></use>
          </svg> -->
          <div>
            <!-- <yj-dot-menu @menuGo="menuGo" :menuVals="[{name:'历史单据',url:'/ViewSheets?supcustId='+sheet.supcust_id+'&&supcustName='+sheet.sup_name+'&&startDate='+calcThreeMonthAgo+'&&sheetTabType='+getSheetTypeTab()}]"></yj-dot-menu> -->

            <yj-dot-menu @menuGo="menuGo" :menuVals="[{name:'历史单据',url:'/ViewSheetAll?queryParams='+JSON.stringify({startDate:calcThreeMonthAgo,endDate:calcNowDate,customerInfo: [{ids:sheet.supcust_id,titles:sheet.sup_name}],})+'&&sheetTabType='+getSheetTypeTab()+'&timestamp='+Date.now()}]"></yj-dot-menu>
          </div>

          <svg style="margin:2px 2px 0 20px " @click.stop="submitSaleSheet" width="30px" height="30px" stroke-width="1.3" class="black">
            <use :xlink:href="'#icon-plane'"></use>
          </svg>
        </template>
      </van-nav-bar>
    </div>

    <!-- 标题栏end -->
    <!-- 中间内容 -->
    <!--    <div ref="publicBox3" class="pubisLoadingSheetlic_box3" :style="{height:publicBox3Height}">-->
    <div ref="publicBox3" class="public_box3">
      <!-- 单据头部信息 -->
      <div class="public_query">
        <div class="public_query_title" v-if="sheet.sheet_no">
          <!--<div class="public_query_titlet"  >
            欠款:{{sheet.left_amount}}
          </div>-->
          <div style="padding-top: 5px;padding-left: 25px;" class="public_query_title_t sheet_info">
            <span>{{ sheet.sheet_no }}</span>
          </div>

          <div v-if="sheet.receive_addr"
          style="padding-top: 5px;padding-left: 25px;text-align: left;"class="public_query_title_t sheet_info">
            收货地址: {{ sheet.receive_addr_desc }}
          </div>

          <div style="padding-top: 5px;padding-left: 25px;text-align: left;" v-if="sheet.happen_time"
            class="public_query_title_t sheet_info">
            <span>交易时间: {{ sheet.happen_time }}</span>
          </div>
          <div style="padding-top: 5px;padding-left: 25px;text-align: left;" v-if="sheet.make_time"
            class="public_query_title_t sheet_info">
            <span>制单:{{ sheet.maker_name }} {{ sheet.make_time }}</span>
          </div>
          <div style="padding-top: 5px;padding-left: 25px;text-align: left;" v-if="sheet.approve_time"
            class="public_query_title_t sheet_info">
            <span>审核:{{ sheet.approver_name }} {{ sheet.approve_time }}</span>
          </div>

        </div>
        <div class="public_query_titleSrc">
          <div class="public_query_wrapper">
            <!-- 客户/供应商/仓库/定货会账户 -->
            <div class="public_query_titleSrc_item" style="width:100%">
              <van-icon name="user-o" @click="onSelectClient" />
              <input type="text" style=" width: calc(100% - 60px);" v-model="sheet.sup_name"
                :placeholder="sheet.sheetType == 'CG' || sheet.sheetType == 'CT' || sheet.sheetType == 'CD' ? '供应商' : '客户'" readonly
                @click="onSelectClient" />
            </div>

          </div>

          <div class="public_query_wrapper" style="margin-top:0px">
            <div class="public_query_titleSrc_item" style="width:100%" v-if="sheet.sheetType !== 'DH'">
              <van-icon name="wap-home-o" @click="onSelectBranch" />
              <input type="text" style=" width: calc(100% - 60px);" v-model="sheet.branch_name" placeholder="仓库" readonly
                @click="onSelectBranch" />
            </div>
            <div class="public_query_titleSrc_item" style="width:100%" v-else>
              <van-icon name="paid" @click="onSelectOrderAccount" />
              <input type="text" style=" width: calc(100% - 60px);" v-model="sheet.prepay_sub_name" placeholder="定货会账户"
                readonly @click="onSelectOrderAccount" />
            </div>
          </div>

          <div v-if="canAppSelectSeller" class="public_query_wrapper">
            <div class="public_query_titleSrc_item" style="width:100%;display:flex;align-items: center;"
              v-if="',X,T,XD,TD,'.indexOf(',' + sheet.sheetType + ',') >= 0">

              <svg class="icon black query_icon" stroke-width="1.3">
                <use :xlink:href="'#icon-seller'" fill="#aaa"></use>
              </svg>
              <select-one class="selectOne" style=" width: calc(100% - 60px);" placeholder="业务员" :hasClearBtn="false"
                :target="'seller'" :formObj="sheet" :formFld="'seller_id'" :formNameFld="'seller_name'" />
            </div>
          </div>
          <div class="public_query_wrapper"
            v-if="(canAppSelectSender || sheet.senders_name) && ',X,T,XD,TD,'.indexOf(',' + sheet.sheetType + ',') >= 0">
            <div class="public_query_titleSrc_item" style="width:100%;display:flex"
              v-if="',X,T,XD,TD,'.indexOf(',' + sheet.sheetType + ',') >= 0">

              <svg class="icon black query_icon" @click="onSelectSender" stroke-width="1.3">
                <use :xlink:href="'#icon-sender'" fill="#aaa"></use>
              </svg>
              <input type="text" style=" width: calc(100% - 73px);" v-model="sheet.senders_name" placeholder="送货员"
                readonly @click="onSelectSender" />
            </div>
          </div>

          <div v-if="canAppSelectGetter && ',X,T,DH,'.indexOf(',' + sheet.sheetType + ',') >= 0"
            class="public_query_wrapper">
            <div class="public_query_titleSrc_item" style="width:100%;display:flex;align-items: center;">
              <svg class="icon black query_icon" stroke-width="1.3">
                <use :xlink:href="'#icon-getter'" fill="#aaa"></use>
              </svg>
              <select-one class="selectOne" style=" width: calc(100% - 60px);" placeholder="收款人"
                :disabled="sheet.approve_time ? true : false" :hasClearBtn="false" :target="'worker'" :formObj="sheet"
                :formFld="'getter_id'" :formNameFld="'getter_name'" />
            </div>
          </div>
          <div v-if="canAppInputSendTime && (sheet.sheetType == 'XD' || sheet.sheetType == 'X')" class="public_query_wrapper">
            <!-- 客户/供应商/仓库/定货会账户 -->
            <div class="public_query_titleSrc_item" style="width:100%">
              <van-icon name="clock-o" />
              <input type="text" style=" width: calc(100% - 60px);" v-model="sheet.send_time" placeholder="送货时间" readonly
                @click="onInputSendTime" />
            </div>

          </div>

          <div class="clientInfo"
            v-show="sheet.supcust_id && sheet.sup_name && (prepayInfo !== '' || arrearsInfo !== '' || dispInfo !== '' || sheet.acct_cust_name !== '' || paidAmountInfo)">
            <span class="prepayInfoSpan">
              <span class="acct-cust-name"
                v-if="',X,XD,'.indexOf(',' + sheet.sheetType + ',') >= 0 && sheet.acct_cust_name">结算单位：{{
                  sheet.acct_cust_name }}</span>
              <span class="arrearsInfoSpan" v-if="arrearsInfo" @click="goGetArrearsSheet">{{ arrearsInfo }}</span>
              <template v-if="prepayInfo !== ''"> {{ prepayInfo }}</template>
              <template v-if="dispInfo !== ''"> {{ dispInfo }}</template>
              <template v-if="paidAmountInfo && !isSharingSheetImage"> {{ paidAmountInfo }}</template>
 
            </span>
          </div>
        </div>
      </div>

      <div v-if="sheet.acct_type_name || sheet.acct_way_name"
        style="width:95%;color:#888;font-size:14px;padding-left:20px;padding: right 5px;text-align:left;">
        结算方式: {{ sheet.acct_type_name }} {{ sheet.acct_way_name }}
      </div>
      <div v-if="sheet.sup_addr"
        style="width:95%;color:#888;font-size:14px;padding-left:20px;padding: right 5px;text-align:left;">
        地址: {{ sheet.sup_addr }}
      </div>

      <div v-if="sheet.make_brief" class="sheet-mark-brief-wrapper">
        注: {{ sheet.make_brief }}
      </div>
      <!-- v-if="" -->
      <ConcaveDottedCenter />
      <div class="approved_reded">
        <div class="sheet_state approved" v-if="sheet.approve_time && (sheet.red_flag == '' || sheet.red_flag == '0')">
          <img src="../../assets/images/approved.png" />
        </div>
        <div class="sheet_state reded" v-if="sheet.red_flag == '1'">
          <img src="../../assets/images/reded.png" />
        </div>
      </div>

      <!--<assembly-query ref="sheetHead" @bindWarehouseSelect="onBranchSelected" :bindSheetTitle="sheet"></assembly-query>-->
      <div v-if="$store.state.operSetting.fontSize < 1 && appSheetItemShowStyle == 'compact'" style="padding:0 10px">
        <div class="title" style="margin-top: 6px; color: #aaa;padding: 5px 10px;">
          <van-row>
            <van-col style="text-align: left;display:flex;justify-content:space-between;" span="12">
              <div>品名</div>
              <div>
                <svg fill="#aaa" @click="onSortByItemName">
                  <use xlink:href="#icon-sort"></use>
                </svg>
              </div>
            </van-col>
            <van-col style="text-align: right;" span="4">单价</van-col>
            <van-col style="text-align: right;" span="4">数量</van-col>
            <van-col style="text-align: right;" span="4">金额</van-col>
          </van-row>

        </div>
        <div :class="sheet.sheet_no ? 'sales_box_list_big' : 'sales_box_list'">
          <div class="sales_list">
            <div class="sales_list_boxs">
              <ul class="sales_ul">
                <SlickList axis="y" v-model="sheet.sheetRows" :pressDelay="500" helperClass="helper-class-slick-item"
                  :disabled="sheet.approve_time || sheet.red_flag === '1'">
                  <SlickItem v-for="(item, index) in sheet.sheetRows" :key="index" :index="index">
                    <van-swipe-cell>
                      <div @click="onRowEdit(item, index)" style="display: flex;align-items: center; width: 100%">
                        <van-image v-if="item.showImages && item.showImages.tiny !== ''" class="sales_img"
                          :src="item.showImages.tiny" />
                        <div class="sales_ul_li"
                          style="border-bottom-style: solid;display: flex;flex-direction: column;justify-content: center;height: fix-content;padding: 10px 5px 10px 5px;">
                          <van-row style="width: 100%">
                            <van-col span="12" style="height: 100%">
                              <!-- <span class="selectSheetRowStyle" v-if="item.order_sub_id">定</span> -->
                              <span class="selectSheetRowStyle"
                                v-if="item.isSpecialPrice && item.isSpecialPrice.toString().toLowerCase() === 'true' && !item.order_sub_id">特</span>
                              <span v-else-if="item.disp_flow_id !== undefined && item.disp_flow_id !== ''"
                                class="selectSheetRowStyle"
                                :style="selectSheetRowConf['CL'].style">{{ selectSheetRowConf['CL'].showMsg }}</span>
                              <span v-else-if="(sheet.sheetType == 'X' || sheet.sheetType == 'XD' || (sheet.sheetType == 'CG' && item.trade_type === 'CT')) && item.trade_type
                                && item.trade_type !== 'X'
                                && item.trade_type !== 'XD'
                                && item.trade_type !== 'TD'
                                " class="selectSheetRowStyle"
                                :style="selectSheetRowConf[item.trade_type].style">{{ selectSheetRowConf[item.trade_type].showMsg
                                }}</span>
                              {{ item.item_name }}
                              <template v-if="item.attr_qty">
                                {{ HandleNoStockAttrSplitShow(item.attr_qty) }}
                              </template>
                            </van-col>

                            <van-col span="4" style="height:100%;" class="price-col">
                              {{ canSeePrice ? toMoney(item.real_price, 4) : '' }}
                              <div v-if="!inPrivateMode">
                                <span style="color:#aaa"
                                  v-if='appShowDiscount && item.orig_price && item.real_price != item.orig_price'>{{
                                    canSeePrice ?
                                    toMoney((item.real_price / item.orig_price) * 100, 1) + '%' : '' }}</span>
                                <del style="color:#aaa"
                                  v-if='appShowDiscount && item.orig_price && item.real_price != item.orig_price'>{{
                                    canSeePrice ? toMoney(item.orig_price) : '' }}</del>

                              </div>
                            </van-col>
                            <van-col span="4" style="display:flex;justify-content: center;">{{
                              toMoney(item.quantity, 3)
                            }}<span style="width:1px"></span>{{ item.unit_no }}</van-col>
                            <van-col span="4">{{ canSeePrice ? toMoney(item.sub_amount) : ''
                            }}</van-col>
                          </van-row>
                          <div
                            v-if='item.real_price != item.sys_price && !inPrivateMode && appPriceBeforeChange'
                            style="display:flex;width:100%;flex-wrap:wrap">
                            <div style="width:45%;"></div>
                            <div class="sys_price">改前价:{{ toMoney(item.sys_price) }}</div>
                          </div>
                          <div style="display:flex;width:100%;flex-wrap:wrap">
                            <div class="other-info" v-if="appSheetUseAssistQty">
                              辅助数量：{{ calcAssistQty(item) }}
                            </div>
                            <div v-if="item.remark" class="other-info" style="color:#f88;">{{ item.remark }}</div>
                            <div v-if="appSheetShowItemSpec && item.item_spec" class="other-info">{{ item.item_spec }}
                            </div>
                            <div v-if="item.sn_code" class="other-info">
                              {{ (appUseSn && item.sn_code) ? "序列号:" + item.sn_code : "" }}
                            </div>
                            <div v-if="item.virtual_produce_date && isShowVirtualProduceDate && !item.produce_date"
                              class="other-info">
                              {{ item.virtual_produce_date ? "产期:" + item.virtual_produce_date :
                                "" }}
                            </div>
                            <div v-if="item.valid_days" class="other-info">
                              {{ (appUseVirtualProduceDate && item.valid_days) ? "保质期:" + item.valid_days : "" }}
                            </div>
                            <div v-if="item.sn_code" class="other-info">
                              {{ (appUseSn && item.sn_code) ? "序列号:" + item.sn_code : "" }}
                            </div>
                            <div class="other-info">
                              <template
                                v-if="item.s_barcode && (appShowBarcode == 'sbarcode' || (appShowBarcode == 'ubarcode' && item.unit_no == item.s_unit_no))">{{
                                  "小:" + item.s_barcode }}
                              </template>
                              <template
                                v-if="item.b_barcode && (appShowBarcode == 'bbarcode' || (appShowBarcode == 'ubarcode' && item.unit_no == item.b_unit_no))">{{
                                  "大:" + item.b_barcode }}
                              </template>
                              <template
                                v-if="item.m_barcode && (appShowBarcode == 'mbarcode' || (appShowBarcode == 'ubarcode' && item.unit_no == item.m_unit_no))">{{
                                  "中:" + item.m_barcode }}
                              </template>
                            </div>

                          </div>
                          <div style="display:flex;width:100%;justify-content:space-between">
                            <div v-if="item.branch_id" class="other-info">
                              {{ "仓库:" + item.branch_name }}
                            </div>
                            <div v-if="item.branch_position_name" class="other-info">
                              库位:{{ item.branch_position_name }}
                            </div>
                          </div>
                          <div style="display:flex;width:100%;justify-content:space-between">
                            <div v-if="item.produce_date" class="other-info">
                              {{ item.produce_date ? "产期:" + item.produce_date.slice(0, 10) :
                                "" }}
                            </div>
                            <div v-if="item.batch_no" class="other-info">
                              批次:{{ item.batch_no }}
                            </div>
                          </div>

                          <div v-if="fromDelivery && qtyLoaded" class="item_attr_qty_wrapper">
                            <div v-if="!IsStockEnough(item)" class="item_attr_qty_content"
                              :style="{ 'background-color': !IsStockEnough(item) ? '#f88' : '#000', color: !IsStockEnough(item) ? '#fff' : '#000' }"
                              style="border-color:#1989fa;">
                              库存:{{ GetStockStr(item) }}
                            </div>
                          </div>

                          <!-- 不区分库存信息的展示 -->
                          <div v-if="item.attr_qty && !item.item_id.startsWith('nanoid')" class="item_attr_qty_wrapper">
                            <!--                      &lt;!&ndash; <div class="item_attr_title">属性数量: </div> &ndash;&gt;-->
                            <!--                      <div v-for="(attr , attrIndex) in (typeof item.attr_qty == 'string' ? JSON.parse(item.attr_qty) : item.attr_qty)" :key="attrIndex" class="item_attr_qty_content">-->
                            <!--                       {{attr[Object.keys(attr).filter(key => key.startsWith('optName'))]}}:<span style="font-family: numfont;">{{attr.qty}}</span>{{item.unit_no}}-->
                            <!--                      </div>-->

                            <div v-for="(attr, attrIndex) in  handleToJSON(item)" :key="attrIndex"
                              class="item_attr_qty_content">
                              <!--                       {{attr[Object.keys(attr).filter(key => key.startsWith('optName'))]}}:-->
                              {{ handleAttrNameShow(attr) }}:
                              <span style="font-family: numfont;">{{ attr.qty }}</span>{{ item.unit_no }}
                            </div>
                          </div>

                        </div>
                      </div>
                      <template #right>
                        <van-button v-if="!sheet.approve_time" class="delSheetRow" square type="danger" text="删除"
                          @click="btnRemoveRow_click(index)" />
                      </template>
                    </van-swipe-cell>
                  </SlickItem>

                </SlickList>

                <div class="total_msg">
                  <span v-if="sheetRowCount > 0">共 {{ sheetRowCount }} 行</span>
                  <span v-if="sheetRowCount > 0">{{ sumQuantityUnitConv }}</span>

                </div>
                <div class="actions-wrapper">
                  <DisplayActionsList :msgId="msgId" :displayGiveProofsType="displayGiveProofsType"
                    v-if="sheet.displayGiveProofs && sheet.displayGiveProofs.length > 0" ref="displayActionsListRef"
                    :work-content-type="displayGiveProofsType.key" :work-content-action="sheet.displayGiveProofs"
                    :sheet="sheet" :title="displayGiveProofsType.name" />
                </div>
              </ul>
            </div>
          </div>
        </div>
      </div>
      <div v-else style="padding:0 10px">
        <div class="title" style="margin-top: 6px; color: #aaa">
          <van-row>
            <van-col style="text-align: left;display:flex;justify-content:space-between;" span="12">
              <div>品名</div>
              <div>
                <svg width="24px" height="18px" fill="#aaa" style="margin-top:1px;" @click="onSortByItemName">
                  <use xlink:href="#icon-sort"></use>
                </svg>
              </div>
            </van-col>
            <van-col v-if="canSeePrice" style="text-align: right;" span="12">金额</van-col>
          </van-row>
        </div>
        <div :class="sheet.sheet_no ? 'sales_box_list_big' : 'sales_box_list'">
          <div class="sales_list">
            <div class="sales_list_boxs">
              <ul class="sales_ul">
                <SlickList axis="y" v-model="sheet.sheetRows" :pressDelay="500" helperClass="helper-class-slick-item">
                  <SlickItem v-for="(item, index) in sheet.sheetRows" :key="index" :index="index">
                    <van-swipe-cell>
                      <div style="display:flex;flex-direction:row;">
                      <div v-if="!isNoImagesInAllItems" class="sales_img_wrapper" style="width: 63px;height: 63px;">
                          <img @click="()=>{
                            showImagePreviewFlag = true
                            previewItemImageUrl = (item.showImages && item.showImages.main !== '') ? item.showImages.main : require('@/assets/images/default_good_img.png')
                            }" width="50" height="50" class="sales_img_wrapper" :src="(item.showImages && item.showImages.tiny !== '') ? item.showImages.tiny : require('@/assets/images/default_good_img.png')" fit="resize" /> 
                          <!-- <van-image width="50" height="50" class="sales_img_wrapper" :src="(item.showImages && item.showImages.tiny !== '') ? item.showImages.tiny : require('@/assets/images/default_good_img.png')" fit="resize" /> -->
                        </div>
                      <div @click="onRowEdit(item, index)" style="display: flex;align-items: center; width: 100%">
                        <!-- <div v-if="item.showImages && item.showImages.tiny" class="sales_img_wrapper"
                          style="width: 63px;height: 63px;">
                          <van-image width="50" height="50" class="sales_img_wrapper" :src="item.showImages.tiny" />
                        </div> -->
                        <div class="sales_ul_li"
                          style="border-bottom-style: solid;display: flex;flex-direction: column;justify-content: center;height: fix-content;">
                          <van-row style="width: 100%;">
                            <van-col span="24" style="justify-content:flex-start;height:100%;">
                              <!-- <span class="selectSheetRowStyle" v-if="item.order_sub_id">定</span> -->
                              <span class="selectSheetRowStyle"
                                v-if="item.isSpecialPrice && item.isSpecialPrice.toString().toLowerCase() === 'true' && !item.order_sub_id">特</span>
                              <span v-else-if="item.disp_flow_id !== undefined && item.disp_flow_id !== ''"
                                class="selectSheetRowStyle" :style="selectSheetRowConf['CL'].style">{{
                                  selectSheetRowConf['CL'].showMsg }}</span>
                              <span v-else-if="(sheet.sheetType == 'X' || sheet.sheetType == 'XD') && item.trade_type
                                && item.trade_type !== 'X'
                                && item.trade_type !== 'XD'
                                && item.trade_type !== 'TD'
                                && item.trade_type !== 'CG'
                                " class="selectSheetRowStyle" :style="selectSheetRowConf[item.trade_type].style">{{
    selectSheetRowConf[item.trade_type].showMsg }}</span>
                              <span>{{ index + 1 }}、</span>
                              <span>{{ item.item_name }}
                                <template v-if="item.attr_qty">
                                  {{ HandleNoStockAttrSplitShow(item.attr_qty) }}
                                </template>
                              </span>
                            </van-col>
                          </van-row>
                          <div style="display:flex;width:100%;flex-wrap:wrap">
                            <div v-if="appSheetShowItemSpec && item.item_spec" class="other-info">{{ item.item_spec }}
                            </div>

                            <div class="other-info"
                              v-if="item.s_barcode && (appShowBarcode == 'sbarcode' || (appShowBarcode == 'ubarcode' && item.unit_no == item.s_unit_no))">
                              {{ "小:" + item.s_barcode }} </div>
                            <div class="other-info"
                              v-else-if="item.b_barcode && (appShowBarcode == 'bbarcode' || (appShowBarcode == 'ubarcode' && item.unit_no == item.b_unit_no))">
                              {{ "大:" + item.b_barcode }} </div>
                            <div class="other-info"
                              v-else-if="item.m_barcode && (appShowBarcode == 'mbarcode' || (appShowBarcode == 'ubarcode' && item.unit_no == item.m_unit_no))">
                              {{ "中:" + item.m_barcode }} </div>

                            <div v-if="item.virtual_produce_date && isShowVirtualProduceDate && !item.produce_date"
                              class="other-info">
                              {{ item.virtual_produce_date ? "产期:" + item.virtual_produce_date :
                                "" }}
                            </div>
                            <div v-if="item.valid_days" class="other-info">
                              {{ (appUseVirtualProduceDate && item.valid_days) ? "保质期:" + item.valid_days : "" }}
                            </div>

                          </div>
                          <div style="display:flex;width:100%;justify-content:space-between">
                            <div v-if="item.branch_id" class="other-info">
                              {{ "仓库:" + item.branch_name }}
                            </div>
                            <div v-if="item.branch_position_name" class="other-info">
                              库位:{{ item.branch_position_name }}
                            </div>
                          </div>
                          <div style="display:flex;width:100%;justify-content:space-between">
                            <div v-if="item.produce_date" class="other-info">
                              {{ item.produce_date ? "产期:" + item.produce_date.slice(0, 10) :
                                "" }}
                            </div>
                            <div v-if="item.batch_no" class="other-info">
                              批次:{{ item.batch_no }}
                            </div>
                          </div>

                          <van-row style="width: 100%;flex-wrap: wrap;">
                            <van-col span="8" style="height:100%;align-items:flex-end;" class="price-col">
                              <div style="display: flex;justify-content: space-around;flex-wrap: nowrap;">
                                <span v-if="canSeePrice" style="color:#ccc;">￥</span>
                                <span
                                  :style="{ color: item.sys_price && item.sys_price != item.real_price ? '#f88' : '' }">
                                  {{ canSeePrice ? toMoney(item.real_price, 4) : '' }}
                                </span>
                              </div>
                              <div
                                v-if="!inPrivateMode  && item.sys_price != item.real_price && appPriceBeforeChange"
                                class="sys_price">改前价:{{ toMoney(item.sys_price) }}</div>
                              <div v-if="!inPrivateMode && appPriceBeforeChange && item.orig_price != 0">
                                <span style="color:#aaa"
                                  v-if='appShowDiscount && item.orig_price && item.real_price != item.orig_price'>{{
                                    canSeePrice
                                    ? toMoney((item.real_price / item.orig_price) * 100, 1) + '%' : '' }}</span>
                                <del style="color:#aaa"
                                  v-if='appShowDiscount && item.orig_price && item.real_price != item.orig_price'>{{
                                    canSeePrice ?
                                    toMoney(item.orig_price) : '' }}</del>
                              </div>
                            </van-col>
                            <van-col v-if="canSeePrice" span="2"
                              style="color:#ccc;justify-content:flex-end;">&times;</van-col>
                            <van-col span="7" style="display:flex;">{{ toMoney(item.quantity, 3) }}<span
                                style="width:1px"></span>{{ item.unit_no }}</van-col>
                            <van-col span="7" style="text-align:right;">
                              {{ canSeePrice ? '￥' + item.sub_amount : '' }}
                            </van-col>

                          </van-row>
                          <van-row style="width: 100%;flex-wrap: wrap;">
                             <van-col v-if="seeProfit" span="24" class="profit-info">
                              <div>利润: ￥{{ calculateProfit(item) }}</div>
                            </van-col>
                          </van-row>
                          <van-row style="width: 100%;flex-wrap: wrap;">
                             <van-col v-if="seeProfit" span="24" class="profit-info">
                              <div>利润率: {{calculateProfitMargin(item)}}%</div>
                            </van-col>
                          </van-row>
                          <div style="display:flex;width:100%;flex-wrap:wrap">
                            <div class="other-info" v-if="appSheetUseAssistQty">
                              辅助数量：{{ calcAssistQty(item) }}
                            </div>
                            <div v-if="item.sn_code" class="other-info">
                              {{ (appUseSn && item.sn_code) ? "序列号:" + item.sn_code : "" }}
                            </div>
                            <div v-if="item.remark" class="other-info" style="color:#f88;">{{ item.remark }}</div>
                          </div>

                          <div v-if="fromDelivery && qtyLoaded" class="item_attr_qty_wrapper">
                            <div v-if="!IsStockEnough(item)" class="item_attr_qty_content"
                              :style="{ 'background-color': !IsStockEnough(item) ? '#f88' : '#000', color: !IsStockEnough(item) ? '#fff' : '#000' }"
                              style="border-color:#1989fa;">
                              库存:{{ GetStockStr(item) }}
                            </div>
                          </div>

                          <!-- 不区分库存信息的展示 -->
                          <div v-if="item.attr_qty && !item.item_id.startsWith('nanoid')" class="item_attr_qty_wrapper">
                            <!--                      &lt;!&ndash; <div class="item_attr_title">属性数量: </div> &ndash;&gt;-->
                            <!--                      <div v-for="(attr , attrIndex) in (typeof item.attr_qty == 'string' ? JSON.parse(item.attr_qty) : item.attr_qty)" :key="attrIndex" class="item_attr_qty_content">-->
                            <!--                       {{attr[Object.keys(attr).filter(key => key.startsWith('optName'))]}}:<span style="font-family: numfont;">{{attr.qty}}</span>{{item.unit_no}}-->
                            <!--                      </div>-->

                            <div v-for="(attr, attrIndex) in  handleToJSON(item)" :key="attrIndex"
                              class="item_attr_qty_content">
                              <!--                       {{attr[Object.keys(attr).filter(key => key.startsWith('optName'))]}}:-->
                              {{ handleAttrNameShow(attr) }}:
                              <span style="font-family: numfont;">{{ attr.qty }}</span>{{ item.unit_no }}
                            </div>
                          </div>

                        </div>
                      </div>
                      </div>
                      <template #right>
                        <van-button v-if="!sheet.approve_time" class="delSheetRow" square type="danger" text="删除"
                          @click="btnRemoveRow_click(index)" />
                      </template>
                    </van-swipe-cell>
                  </SlickItem>
                </SlickList>
                <div class="total_msg">
                  <span v-if="sheetRowCount > 0">共 {{ sheetRowCount }} 行</span>
                  <span v-if="sheetRowCount > 0">{{ sumQuantityUnitConv }}</span>
                </div>
                <div class="actions-wrapper">
                  <DisplayActionsList :msgId="msgId" :displayGiveProofsType="displayGiveProofsType"
                    :sheetDisplayActionType="displayGiveProofsType"
                    v-if="sheet.displayGiveProofs && sheet.displayGiveProofs.length > 0" ref="displayActionsListRef"
                    :work-content-type="displayGiveProofsType.key" :work-content-action="sheet.displayGiveProofs"
                    :sheet="sheet" :title="displayGiveProofsType.name" />
                </div>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 中间内容 -->
    <!-- 底部合计 -->
    <div ref="publicBox4" class="total_money">
      <footer ref="salesListFooter" class="sales_list_footer">
        <div v-if="payBillStatusText">
          <span class="payb-status-text">{{ payBillStatusText }}</span>
          <span style="color:#555">支付单号：</span><span class="sales_list_span">{{ sheet.pay_bill_id }}</span>
        </div>
        <div v-if="canSeePrice">
          <span style="color:#555;padding-left:15px">合计: </span><span class="sales_list_span">{{toMoney(sheet.total_amount)
          }}</span>
          <span v-if="seeProfit" style="color:#555;padding-left:15px">利润: </span><span v-if="seeProfit" class="sales_list_span">{{calculateTotalProfit()
          }}</span>
          <span v-if="parseFloat(sheet.total_weight) > 0" style="color:#555;">重: </span><span v-if="parseFloat(sheet.total_weight) > 0" class="sales_list_span">{{toMoney(sheet.total_weight)}}</span>
          <template v-if="xAmount > 0 && tAmount > 0">
            <span style="color:#555">销: </span><span class="sales_list_span">{{ xAmount }}</span>
            <span style="color:#555">退: </span><span class="sales_list_span">{{ tAmount }}</span>
          </template>
          <template v-else-if="xAmount <= 0 && tAmount > 0">
            <span style="color:#555">退: </span><span class="sales_list_span">{{ tAmount }}</span>
          </template>

          <span v-if="sheet.now_disc_amount > 0">
            <span style="color:#555">惠: </span><span class="sales_list_span">{{ sheet.now_disc_amount }}</span>
          </span>
          <span v-if="Number(sheet.left_amount) !== 0">
            <span style="color:#f40">欠: </span><span class="sales_list_span" style="color:#f40">{{
              Number(sheet.left_amount) }}</span>
          </span>
          <span v-if="Number(sheet.payway1_amount) !== 0">
            <span style="color:#555">{{ sheet.payway1_name }}: </span><span class="sales_list_span">{{
              toMoney(sheet.payway1_amount) }}</span>
          </span>
          <span v-if="Number(sheet.payway2_amount) != 0">
            <span style="color:#555">{{ sheet.payway2_name }}: </span><span class="sales_list_span">{{
              toMoney(sheet.payway2_amount) }}</span>
          </span>
          <span v-if="Number(sheet.payway3_amount) != 0">
            <span style="color:#555">{{ sheet.payway3_name }}: </span><span class="sales_list_span">{{
              toMoney(sheet.payway3_amount) }}</span>
          </span>
        </div>
      </footer>
      <div class="sales_footer" style="padding: 0 15px 10px 10px;">
        <div class="footer_input">
          <input ref="inputCodesRef" id="codes" type="text" style="padding-left: 3px;" v-model="searchStr" enterkeyhint = "go"
               @keydown="onSearchInputKeyDown($event)" placeholder="名称/简拼/条码/货号"
            :disabled="sheet.approve_time ? true : false"
            :style="{ width: getSearchInputWidth(), backgroundColor: sheet.approve_time ? '#f2f2f2' : '' }" />
        </div>
        <div class="footer_iconBt" :disabled="sheet.approve_time !== '' || IsSubmiting" @click="btnClassView_click">
          <svg width="35px" height="35px" fill="#F56C6C">
            <use xlink:href="#icon-add"></use>
          </svg>
        </div>
        <div class="footer_iconBt" type="info" @click="btnScanBarcode_click">
          <svg width="30px" height="30px" fill="#555">
            <use xlink:href="#icon-barcodeScan"></use>
          </svg>
        </div>
        <div v-if="((sheet.sheetType === 'CG'||sheet.sheetType === 'X'||sheet.sheetType === 'XD'||sheet.sheetType==='T'||sheet.sheetType==='TD') && allowMobilePicImport)" class="footer_iconBt" type="info">
          <svg width="27px" height="27px" fill="#555" @click="btnUploadImage_click">
            <use xlink:href="#icon-camera"></use>
          </svg>
        </div>
      </div>
    </div>

      <!-- 预收款弹窗 -->
      <van-dialog v-model="showPrePaySheet" title="预收款详情" show-cancel-button  @confirm="btnPrepayDetailAlloc_click" >

          <div style="border-bottom: 1px solid #ccc; margin: 5px 0; width: 100%;"></div>
          <!-- 表头 -->
          <div class="table-header" style="position: sticky; top: 0; z-index: 10;">
            <van-row type="flex" justify="center" class="table-header">
              <van-col span="10">单号</van-col>
              <!-- <van-col span="7">初始金额</van-col> -->
              <van-col span="7">余额</van-col>
              <van-col span="7">使用</van-col>
            </van-row>
          </div>
          <!-- 表体 -->
          <div style="max-height: 70vh; overflow-y: auto;max-height: 40vh;">
          <div style="border-bottom: 1px solid #ccc; margin: 5px 0; width: 100%;"></div>
          <van-row type="flex" justify="center" v-for="(pre, preIndex) in prepayBalanceDetail" :key="preIndex" style="padding: 5px 0 5px 0; text-align: center;">
            <van-col span="10" style="color: #1989fa;" @click="goPrepaySheet(pre.sheet_id,pre.sheet_type)">
               <div>{{pre.sheet_no}}</div>
               <div style="color:#000;">{{pre.sheet_type=="T"?"退货":pre.sheet_type=="YS"?"预收":""}} {{pre.remark}}</div>
            </van-col>
            <!-- <van-col span="7">{{ pre.init_balance }}</van-col> -->
            <van-col :class="{ red: pre.balance < 0 }" span="7">{{ pre.balance }}</van-col>
            <van-col span="7">
            <!-- 使用 van-field 替换显示 amount 的部分 -->
            <input type="text" v-model="pre.amount" :disabled="pre.balance === 0" style="border: none; border-bottom: 1px solid black; width: 60px;">
          </van-col>
            <div style="border-bottom: 1px solid #ccc; margin: 5px 0; width: 100%;"></div>
          </van-row>
        </div>
      </van-dialog>


    <!-- 弹窗模块 -->
    <van-popup v-model="billPayPanel.show" round closeable style="width:75%; height:60%; overflow-y:hidden;">
      <div style="display:block; height:100%;">
        <div style="margin-top:15%;" />
        <div>扫描二维码，支付{{ billPayPanel.payAmount }}元</div>
        <van-image width="200" height="200" lazy-load :src="billPayPanel.qrCode" />
        <div style="font-size:smaller; color:gray;">
          此付款码1小时后失效
        </div>
        <van-button @click="btnPaid_click" color="linear-gradient(to right, #ff6034, #ee0a24)"
          style="margin-top:5%; width:70%; height:12%;">
          我已支付
        </van-button>
      </div>
    </van-popup>
    <van-popup v-model="popupSubImage1" :style="{ height: 'auto', width: '100%', overflowY: 'auto' }" class="van_popup"
      position="bottom" closeable close-icon-position="top-left">
      <div class="content-img">
        <img width="100%" height="100%  " lazy-load :src=imageSrc1 />
      </div>
    </van-popup>
    <van-popup v-model="popupSubImage2" :style="{ height: 'auto', width: '100%', overflowY: 'auto' }" class="van_popup"
      position="bottom" closeable close-icon-position="top-left">
      <div class="content-img">
        <img width="100%" height="100%  " lazy-load :src=imageSrc2 />
      </div>
    </van-popup>
    <van-popup v-model="popupSubmitPannel" :style="{ height: '100%', width: '80%', overflowY: 'auto' }" class="van_popup"
      position="right">
      <!-- <h5 class="custom_h5">
        其他选项
        <van-icon class="icon_h5" name="cross" @click="popupSubmitPannel = false"/>
      </h5> -->
      <div style="height:30px;border-top:1px solid #ccc"></div>

      <van-number-keyboard v-model="keyboardText" extra-key="." :title="keyboardText ? `${keyboardText}折` : ''"
        close-button-text="完成" :show="needKeyboardField != ''" @close="onKeyboardInputFinished"
        @blur="needKeyboardField = ''" />
      <!-- 审核界面 -->
      <div class="sales_more">
        <div v-if="canSeePrice" class="amount-row">
          <div style="display: flex; flex-direction: column">
            <div>合计: ￥{{ sheet.total_amount }}</div>
          </div>
          <!-- <div @click="btnCalcDiscount_click" v-if="!sheet.approve_time" style="margin-bottom:12px;margin-top:10px;background-color:#fff;border:1px solid #ccc" class="discount-btn">折</div> -->
          <div @click="btnCalcDiscount_click" v-if="!sheet.approve_time"
            style="margin-bottom:12px;margin-top:10px;background-color:#fff;" class="discount-btn">折</div>

        </div>
        <div v-if="canSeePrice && !(sheet.prepay_sub_id == '-1' && sheet.sheetType == 'DH')"
          style="display: flex; flex-direction: row;margin-top:5px">
          <van-field v-if="canAllowSheetDiscAmount" v-model="sheet.now_disc_amount" label="优惠:" type="number"
            placeholder="优惠" :formatter="checkInputLegalSymbol" format-trigger="onBlur" @input="onDiscAmountChange"
            :disabled="sheet.approve_time ? true : false" />
          <!-- <div @click="btnCalcDiscAmt_click" v-if="!sheet.approve_time" style="margin-top:10px;margin-bottom:0px;background-color:#fff;border:1px solid #ccc" class="discount-btn">计</div> -->
          <div @click="btnCalcDiscAmt_click" v-if="!sheet.approve_time"
            style="margin-top:10px;margin-bottom:0px;background-color:#fff;" class="discount-btn">计</div>
        </div>
        <div
          v-if="canSeePrice && ',X,T,CG,CT,DH,XD,TD,'.indexOf(',' + sheet.sheetType + ',') >= 0 && !(sheet.prepay_sub_id == '-1' && sheet.sheetType == 'DH')">
          <div style="display: flex; flex-direction: row" v-if="canAllowSheetArrears">
            <van-field v-model="sheet.left_amount" label="欠款:" type="number" placeholder="欠款"
              :formatter="checkInputLegalSymbol" format-trigger="onBlur" :disabled="sheet.approve_time ? true : false"
              @input="onLeftAmountChange" />
            <div style="margin-top:12px;margin-bottom:0px;background-color: #fff" class="discount-btn">元</div>
          </div>

          <van-field v-model="sheet.make_brief" label="备注" :disabled="sheet.approve_time ? true : false">
            <template #button
              v-if="(sheet.approve_time ? true : false) && (sheet.sheetType == 'X' || sheet.sheetType == 'XD' || sheet.sheetType == 'CG')">
              <van-button style="border:0" icon="edit" size="mini" type="default" @click="handleAppendBrief" />
            </template>
          </van-field>
          <div
          v-if="canSeePrice && ',X,'.indexOf(',' + sheet.sheetType + ',') >= 0 && isSaleNeedMarkIOU && (sheet.left_amount!=='0')">
          <div style="display: flex; flex-direction: row" v-if="canAllowSheetArrears">
            <van-checkbox :disabled="(sheet.approve_time ? true : false)" @click="onCheckboxClick"
            shape="square" v-model="noArrearsBills" style="margin-left: 16px;padding: 16px 0;">未给欠条</van-checkbox>
          </div>
        </div>
          <div class="other_payway">
            <van-row class="payway payway-minHeight">
              <van-col span="7" :disabled="shouldPaywayDisabled(1)" @click="onSelectPayway('1')"
                style="color:#8899bb;width: 102px;max-width: 102px;">
                {{ sheet.payway1_name ? sheet.payway1_name : "支付方式" }}
                <van-icon class="arrow" v-if="!popupPaywayPannel" name="arrow" />
                <van-icon class="arrow" v-else name="arrow-down" />
              </van-col>
              <van-col span="12" style="justify-content:flex-start;">
                <input type="number" :disabled="shouldPaywayDisabled(1)" :readonly="sheet.approve_time ? true : false" ref="paywayInput"
                  v-model="sheet.payway1_amount" placeholder="金额" @input="onPaywayAmount('1','payway1_id', sheet.payway1_id, payway1_balance && showPayWay1)" />
                <div @click="btnShowSubCode_click('qrcode1')" v-if="!(this.qrcode1 == '' || this.qrcode1 == null)"
                  style="margin-bottom:12px;margin-top:10px;position:static;background-color:#fff;border:1px solid #ccc"
                  class="discount-btn">码</div>
                <div @click="showPrePaySheet_click(sheet.payway1_id, 'payway1_id')" v-if="(payway1_balance && showPayWay1)"
                  style="margin-bottom:12px;margin-top:10px;position:static;background-color:#fff;border:1px solid #ccc"
                  class="discount-btn">选</div>
                <van-tag v-if="sheet.payway1_status" plain type="success" size="medium">{{ sheet.payway1_status
                }}</van-tag>
              </van-col>
              <van-col span="5" style="padding-right:10px;">
                <van-icon v-if="!sheet.approve_time && !showPayWay2" class="payway_add" name="plus"
                  @click="showPayWay2 = true" />
              </van-col>
            </van-row>
            <div v-if="payway1_balance && showPayWay1" class="payway-balance">
              余:{{ payway1_balance }}
            </div>

            <van-row class="payway" v-if="showPayWay2">
              <van-col span="7" @click="onSelectPayway('2')" :disabled="shouldPaywayDisabled(2)" style="color:#8899bb;">
                {{ sheet.payway2_name ? sheet.payway2_name : "支付方式" }}
                <van-icon class="arrow" v-if="!popupPaywayPannel" name="arrow" />
                <van-icon class="arrow" v-else name="arrow-down" />
              </van-col>
              <van-col span="12" style="justify-content:flex-start;">
                <input type="number" :disabled="shouldPaywayDisabled(2)" :readonly="sheet.approve_time ? true : false" ref="paywayInput"
                  v-model="sheet.payway2_amount" placeholder="金额" @input="onPaywayAmount('2', 'payway2_id', sheet.payway2_id, payway2_balance && showPayWay2)" />
                <div @click="btnShowSubCode_click('qrcode2')" v-if="!(this.qrcode2 == '' || this.qrcode2 == null)"
                  style="margin-bottom:12px;margin-top:10px;position:static;background-color:#fff;border:1px solid #ccc;margin: 10px;"
                  class="discount-btn">码</div>
                <div @click="showPrePaySheet_click(sheet.payway2_id, 'payway2_id')" v-if="(payway2_balance && showPayWay2)||sheet.prepay_sheet_info !== ''"
                  style="margin-bottom:12px;margin-top:10px;position:static;background-color:#fff;border:1px solid #ccc"
                  class="discount-btn">选</div>
                <van-tag v-if="sheet.payway2_status" plain type="success" size="medium">{{ sheet.payway2_status
                }}</van-tag>
              </van-col>
              <van-col span="5" style="padding-right:10px;">
                <van-icon v-if="!sheet.approve_time && !showPayWay3" class="payway_add" name="plus"
                  @click="showPayWay3 = true" />
                <van-icon v-if="!sheet.approve_time && !showPayWay3" class="payway_add" name="minus"
                  @click="showPayWay2 = false; sheet.payway2_amount = 0; onPaywayAmount('2')" />
              </van-col>
            </van-row>
            <div v-if="payway2_balance && showPayWay2" class="payway-balance">
              余:{{ payway2_balance }}
            </div>
            <van-row class="payway" v-if="showPayWay3 || sheet.payway3_amount">
              <van-col span="7" :disabled="shouldPaywayDisabled(3)" @click="onSelectPayway('3')" style="color:#8899bb;">
                {{ sheet.payway3_name ? sheet.payway3_name : "支付方式" }}</van-col>
              <van-icon class="arrow" v-if="!popupPaywayPannel" name="arrow" />
              <van-icon class="arrow" v-else name="arrow-down" />
              <van-col span="12" style="justify-content:flex-start;">
                <input type="number" :disabled="shouldPaywayDisabled(3)" :readonly="sheet.approve_time ? true : false" ref="paywayInput"
                  v-model="sheet.payway3_amount" placeholder="金额" @input="onPaywayAmount('3', 'payway3_id', sheet.payway3_id, payway3_balance && showPayWay3)" />
                <div @click="btnShowSubCode_click('qrcode3')" v-if="!(this.qrcode3 == '' || this.qrcode3 == null)"
                  style="margin-bottom:12px;margin-top:10px;position:static;background-color:#fff;border:1px solid #ccc"
                  class="discount-btn">码</div>
                <div @click="showPrePaySheet_click(sheet.payway3_id, 'payway3_id')" v-if="(payway3_balance && showPayWay3)||sheet.prepay_sheet_info !== ''"
                  style="margin-bottom:12px;margin-top:10px;position:static;background-color:#fff;border:1px solid #ccc"
                  class="discount-btn">选</div>
                <van-tag v-if="sheet.payway3_status" plain type="success" size="medium">{{ sheet.payway3_status
                }}</van-tag>
              </van-col>
              <van-col span="5" style="padding-right:10px;">
                <van-icon v-if="!sheet.approve_time" class="payway_add" name="minus"
                  @click="showPayWay3 = false; sheet.payway3_amount = 0; onPaywayAmount('3')" />
              </van-col>
            </van-row>
            <div v-if="payway3_balance && showPayWay3" class="payway-balance">
              余:{{ payway3_balance }}
            </div>
          </div>
        </div>
        <div>
          <div class="other_operate">
            <van-divider style="margin-top: 10px;">附件</van-divider>
            <div class="other_operate_content">

              <div class="appendixphoto-container" v-for="item, index in sheet.appendixPhotos" :key="index"
                style="justify-content:center;">
                <img @click="preview_photo(index)" class="photo" :src="item">
                <div v-if="sheet.approve_time == '' && !IsSubmiting" class="remove-icon" @click="remove_photo(index)">
                  <van-icon name="close" />
                </div>
              </div>
              <div class="iconfont" v-if="sheet.approve_time == '' && !IsSubmiting" @click="showImageSelect = true">
                &#xe62e;
              </div>

            </div>
          </div>
          <div class="other_operate">
            <van-divider style="margin: 20px 0px 40px;"></van-divider>
            <div class="other_operate_content" style="margin-bottom: 80px;flex-wrap: nowrap;">
              <button plain type="info" style="height: 45px; margin-top:10px;max-width: 105px;" class="color_fff"
                v-if="canMake" :disabled="sheet.approve_time !== '' || IsSubmiting || sheet.isRedAndChange"
                @click="btnSave_click">保存</button>
              <button plain type="info" class="color_fff"
                style="height: 45px; margin-top:10px;max-width: 105px; background-color: #ffcccc;" v-if="canApprove"
                :disabled="sheet.approve_time !== '' || IsSubmiting" @click="btnApprove_click">审核</button>
              <button plain type="info" style="height: 45px;max-width: 105px; margin-top:10px;" class="color_ffcccc"
                v-if="canReview" :disabled="sheet.sheet_id && (sheet.review_time || IsSubmiting)"
                @click="btnReview_click">复核</button>
              <button v-if="canPrint"  plain type="info" class="color_fff" style="height: 45px;  margin-top:10px;max-width: 105px;"
                @click="btnGotoPrintView_click()"
                :disabled="(sheetStatusForPrint == 'saved' && !sheet.sheet_id) || (sheetStatusForPrint == 'approved' && !sheet.approve_time) || isPrinting">打印</button>
              <button plain type="info" class="color_fff"
                style="height: 45px; margin-top:10px; background-color: #ffcccc;max-width: 105px;" v-if="sheet.approve_time !== '' &&
                  (sheet.payway1_channel || sheet.payway2_channel || sheet.payway3_channel)
                  && (sheet.payway1_channel != 2 && sheet.payway2_channel != 2 && sheet.payway3_channel != 2)
                  && sheet.sheetType === 'X'
                  " @click="btn_pay_OR_unpay_click">
                {{ isBillPaid ? '退款' : '付款' }}
              </button>
            </div>
            <div v-if="!isInPrintView" class="other_operate_content"
              style="justify-content: space-around; margin-top:20px;">
              <button class="small-btn" plain type="danger" :style="{ 'border-radius': '9px' }"
                v-if="canRed && sheet.approve_time" :disabled="!sheet.sheet_id || sheet.red_flag != ''"
                @click="btnRed_click">红冲</button>
              <button class="small-btn" plain type="danger" :style="{ 'border-radius': '9px' }"
                v-if="canRed && sheet.approve_time && sheet.sheetType != 'DH'"
                :disabled="!sheet.sheet_id || sheet.red_flag != ''" @click="btnRedAndModify_click">冲改</button>
              <button class="small-btn" plain type="danger" v-if="canDelete && sheet.sheet_id && !sheet.approve_time"
                @click="btnDeleteSheet_click">删除</button>
              <button class="small-btn" style="padding: 0; width: 50px; height: 30px;" type="default"
                @click="btnCopySheet_click">复制</button>
              <button class="small-btn" type="default" :disabled="IsSubmiting || sheet.approve_time !== ''"
                @click="onEmpty()">清空</button>
              <button class="small-btn" v-if="canSyncTicketSys && (sheet.sheetType == 'X' || sheet.sheetType == 'XD')"
                style="border-radius:9px;" type="default"
                :disabled="(sheetStatusForPrint == 'saved' && !sheet.sheet_id) || (sheetStatusForPrint == 'approved' && !sheet.approve_time)"
                @click="syncTicketAccess_click">传票证通</button>
              <button class="small-btn" type="default" :disabled="!(sheet.sheet_id)" @click="shareWeChat">分享</button>
              <button class="small-btn" v-if="sheet.sheetType === 'X' ||
                sheet.sheetType === 'T' ||
                sheet.sheetType === 'XD' ||
                sheet.sheetType === 'TD' ||
                sheet.sheetType === 'DH'" type="default" @click="handleInvite">邀请</button>
              <button class="small-btn" v-if="canPrint" type="default" @click="confirmPreviewOutside" :disabled="(sheetStatusForPrint == 'saved' && !sheet.sheet_id) || (sheetStatusForPrint == 'approved' && !sheet.approve_time) || isPrinting">预览</button>
              <button class="small-btn" type="default" @click="btnOut">退出</button>
            </div>
          </div>
        </div>
      </div>
      <!-- 打印详情界面 -->
      <transition name="hide-show">
        <div v-if="isInPrintView" class="sales_more">
          <div v-if="this.useTmp" class="print-template-wrapper" style="margin: 0px 0.55rem;">
            <div style="margin-top: 20px;margin-bottom: 10px;margin-left: 10px; text-align: left;">打印模板</div>
            <div class="radio-tmp-position">
              <!-- <div v-for="(item,i) in tmplist" :key="i" style="display: inline-block; margin: 5px 0; text-align: left; width: 50%;"> -->
              <div v-for="(item, i) in tmplist" :key="i" class="radio-tmp-style">
                <!-- //appearance: none; -->
                <input type="radio" name="template" :id="i" :value="i" v-model="selectedTmpId"
                  style="position: relative; margin-left: 10px;" class="radio-tmp-type" />
                <label :for="i" class="radio-tmp-name">
                  {{ item.name }}
                </label>
              </div>
            </div>
          </div>
          <div v-else style="height:170px;" />
          <!-- 搞个选择打印机 -->
          <div class="select-printer">
            <van-cell-group inset style="width:100%">
              <van-cell is-link title="打印机" title-style="flex: inherit;" :value="defaultPrinter.name"
                @click="showPrinterSelectionPage" />
            </van-cell-group>
          </div>
          <div v-if="!this.useTmp && arrCompanyNamesToPrint && arrCompanyNamesToPrint.length"
            style="margin: 0px 0.55rem;">
            <div class="print-company-title"
              style="margin-top:10px;margin-bottom: 10px;margin-left: 10px; text-align: left;">公司名称</div>
            <div class="radio-tmp-position setHeight">
              <!-- <div v-for="(item,i) in tmplist" :key="i" style="display: inline-block; margin: 5px 0; text-align: left; width: 50%;"> -->
              <div v-for="(name, i) in arrCompanyNamesToPrint" :key="i" class="radio-tmp-style">
                <!-- //appearance: none; -->
                <input type="radio" name="companyName" :id="i" :value="name" v-model="companyNameToPrint"
                  style="position: relative; margin-left: 10px;" class="radio-tmp-type" />
                <label :for="i" class="radio-tmp-name">
                  {{ name }}
                </label>
              </div>
            </div>
          </div>
          <div style="margin-top: 10px;">
            <van-checkbox-group v-model="printBarcodeStyleForSale"
              style="display: flex;  margin: 20px;  margin-left: 1px; padding-top: 0px; ">
              <div style="margin-left: 8px;margin-right:20px; line-height: 30px;">条码</div>
              <van-checkbox name="actualUnit" style="margin-right: 10px;">实际单位</van-checkbox>
              <van-checkbox name="smallUnit" style="margin-right: 10px">小单位</van-checkbox>
            </van-checkbox-group>
            <van-checkbox shape="square" v-model="printBarcodePic" style="margin-left: 56px;">打印条码图</van-checkbox>
            <div class="print-count" v-if="defaultPrinter.type !== 'cloud'" style="margin-bottom: 15px;">
              <button style="font-size: 20px; background: white;color:#777; min-width: 40px;"
                @click="printCount = printCount < 5 ? printCount + 1 : 5">+</button>
              <div style="display: inline-block; margin-right: 5px; color: #8d8d8d;">{{ printCount }}次</div>
              <button style="font-size: 20px; background: white;color:#777; min-width: 40px;"
                @click="printCount = printCount > 1 ? printCount - 1 : 1">-</button>
            </div>
          </div>
          <div style="margin-top: 25px; display: flex; justify-content: space-between; align-items: center; gap: 10px; flex-wrap: nowrap;">
            <van-button class="print-btns" style="flex: 1; min-width: 70px; max-width: 90px; padding: 1px 2px;margin-left:10px"
                        type="default"
                        @click="isInPrintView = false">返回</van-button>
            <van-button class="print-btns"
                        v-if="sheet.sheetType === 'X' || sheet.sheetType === 'T' || sheet.sheetType === 'XD' || sheet.sheetType === 'CG' || sheet.sheetType === 'CT' || sheet.sheetType === 'TD' || sheet.sheetType === 'DH'"
                        style="flex: 1; min-width: 70px; max-width: 90px; background-color: #ffcccc; padding: 1px 2px;"
                        type="default"
                        @click="btnPrint_click"
                        :disabled="isPrinting">确认打印</van-button>
            <van-button class="print-btns"
                        style="flex: 1; min-width: 70px; max-width: 90px; background-color: #f0f0f0; padding: 1px 2px;margin-right:10px"
                        type="default"
                        @click="confirmPreview">打印预览</van-button>
          </div>
        </div>
      </transition>
    </van-popup>
    <van-image-preview v-model="previewShow" :images="previewImageUrls" :start-position="previewIdx" @change="previewOnChange">
      <template v-slot:previewIdx>第{{ previewIdx + 1 }}页</template>
    </van-image-preview>
    <div>
      <!-- 传递 previewImageUrls 给 WeChatShare 组件 -->
      <!-- <WeChatShare :previewImageUrls="previewImageUrls" /> -->
    </div>
    <!-- 预览界面下方的返回、确认打印、分享按钮 -->
    <div v-if="previewShow" class="preview-footer" style="position: fixed; bottom: 20px; left: 50%; transform: translateX(-50%); z-index: 3000; display: flex; gap: 10px;">
      <van-button type="default" @click="previewShow = false" style="width: 100px;">
        返回
      </van-button>
      <van-button type="default" @click="btnPrint_click" :disabled="isPrinting" style="background-color: #ffcccc; width: 100px;">
        确认打印
      </van-button>
      <div>
        <!-- 在分享之前先把previewImageUrls图片传给wechatShare组件 -->
        <!-- <WeChatShare ref="wechatShare" :previewImageUrls="previewImageUrls" /> -->
        <van-button type="default" @click="callShareWeChatPrintPreview" style="width: 100px;">
          分享
        </van-button>
      </div>
    </div>

    <van-popup v-model="PopupPrintersSelect" position="bottom">
      <van-picker title="选择打印机" show-toolbar value-key="name" :columns="printers" :default-index="defaultPrinterIndex"
        @confirm="confirmPrinterSelectionChange" @cancel="hidePrinterSelectionPage" />
    </van-popup>
    <van-popup v-model="popupPaywayPannel" position="bottom">
      <van-picker :columns="paywaysForSelect" show-toolbar title="支付方式" value-key="subInfoToShow"
        @confirm="onPaywaySelected" @cancel="onPaywayCancel" />
    </van-popup>
    <van-popup v-model="showUnsubmitedSheets" round>
      <div class="lowItem" style="width: 300px;">
        <!-- 关闭按钮，外面包裹一个小圈圈 -->
        <div class="close-circle" @click="showUnsubmitedSheets = false" style="position: absolute; top: 10px; right: 10px; cursor: pointer;">
          <van-icon name="cross" class="close-icon" />
        </div>
        <h4>有未保存的单据</h4>
        <ul class="lowItem_ul">
          <li v-for="(item, index) in unsubmitedSheets" :key="index">
            <div class="lowItem_ull" @click="onUnsubmitedSheetSelected(item)">
              {{ item.sup_name }}
            </div>
            <div class="lowItem_ulr" @click="onUnsubmitedSheetSelected(item)">
              {{ item.saveTime }}
            </div>
            <div class="btn-delete" @click="onUnsubmitedSheetDelete(index)">
              删除
            </div>
          </li>
        </ul>
        <!-- 按钮区域 -->
        <div class="button-row">
          <van-button type="default" @click="showUnsubmitedSheets = false" style="height: 40px;border-radius:12px;">新建单据</van-button>
        </div>
      </div>
    </van-popup>
    <van-popup v-if="popupEditSheetRowPannel" v-model="popupEditSheetRowPannel" position="bottom" :style="{ height: '100%' }">
      <EditSheetRow @closeEditItem='()=>{
            popupEditSheetRowPannel = false
        }' :sheet="sheet" :editingRow="editingItem" @onRowEditDone="onRowEditDone" @delIndexItem="delIndexItem"
        @handlePopupEditSheetRowPannelFalse="handlePopupEditSheetRowPannelFalse" :branchList="branchList" />
    </van-popup>
    <div v-if="loadingMsg" class="loading-msg"
      style="border-radius:20px; width:200px;height:80px;background:#555;color:#fff; position:fixed;top:calc(50vh - 40px);left:calc(50vw - 100px);z-index:99999999;display:flex;justify-content:center;align-items:center;">
      <van-loading color="#fff">{{ loadingMsg }}...</van-loading>
    </div>
    <van-dialog width="320px" @confirm="confirmAppendBrief" @cancel="cancelAppendBrief" v-model="showAppendBriefDialog"
      show-cancel-button>
      <div style="padding: 25px 5px;">
        <van-field style="border-bottom:1px solid #eee" v-model="appendBrief" rows="1" autosize label="追加备注"
          type="textarea" placeholder="请输入追加备注" />
      </div>
    </van-dialog>
    <van-popup v-model="bPopupSelectSendTime" round position="bottom" :style="{ height: '40%' }">
      <date-picker v-if="bPopupSelectSendTime" v-model="sheet.send_time" :inline="false" :append-to-body="false"
        format="YYYY-MM-DD HH:mm:ss" type="datetime" value-type="format"
        :time-picker-options="{ start: '00:00', step: '00:30', end: '23:30', format: 'HH:mm' }" :open="true"
        :popup-style="{ 'top': '42px' }" />
    </van-popup>
    <van-popup v-model="bPopupBranchPicker" round position="bottom">
      <van-picker show-toolbar title="选择仓库" :columns="branchList" value-key="branch_name"
        @cancel="bPopupBranchPicker = false" @confirm="onConfirmBranch" />
    </van-popup>
    <van-popup v-model="bPopupSenderPicker" position="bottom" :style="{ height: '40%' }">
      <!-- 选择送货员 -->
      <van-checkbox-group v-model="selectedSenders">
        <van-cell-group>
          <van-cell v-for="(sender, index) in senderList" clickable :key="sender.senders_id" :title="sender.senders_name"
            @click="selectSenders(index)">
            <template #right-icon>
              <van-checkbox :name="sender" ref="sendersCheckboxes" />
            </template>
          </van-cell>
        </van-cell-group>
      </van-checkbox-group>
    </van-popup>
    <van-popup v-model="bPopupOrderAccountPicker" round position="bottom">
      <van-picker show-toolbar title="定货款账户" :columns="OrderAccountList" value-key="sub_name"
        @cancel="bPopupOrderAccountPicker = false" @confirm="onConfirmOrderAccount" />
    </van-popup>
    <van-popup v-model="bPopupClientSelectDialog" duration="0.4" position="bottom" :lock-scroll="false"
      :style="{ height: '100%', position: 'absolute', width: '100%', overflow: 'hidden' }">
      <!-- <div
      v-if="',CG,CT,CD,'.indexOf(',' + sheet.sheetType + ',') < 0"
      @click="addSupcust" style="float: right; padding: 12px">
        <svg width="26px" height="26px">
          <use xlink:href="#icon-add-supcust"></use>
        </svg>
      </div> -->
      <SelectCustomer canHide='true' @handleHide='bPopupClientSelectDialog = false' ref='selectCustomer'
        :showAcctCusts="sheet.sheetType == 'DH'" :sheetType="sheet.sheetType"
        v-if="',X,T,XD,TD,DH,'.indexOf(',' + sheet.sheetType + ',') >= 0" @onClientSelected="onClientSelected">
      </SelectCustomer>
      <SelectSupplier canHide='true' @handleHide='bPopupClientSelectDialog = false'
        v-if="',CG,CT,CD,'.indexOf(',' + sheet.sheetType + ',') >= 0" @onClientSelected="onClientSelected"></SelectSupplier>

    </van-popup>
    <WeChatShare ref="wechatshare" :sheet="sheet" @shareSheetSelected="shareSelected" />
    <WeChatShare ref="previewPrintShare"  @shareSheetSelected="previewPrintShareSelected" />

    <van-popup v-if="!multiSelectItemShow" class="addSheetRowOne" v-model="popupAddSheetRow" duration="0.12"
      position="bottom" @close="addSheetRowClose">
      <div class="add_goods_wrapper">
        <div class="goods_attr_wrapper">
          <transition name="myslide-left">
            <div v-show="attrShowFlag" class="goods_attr" key="attrListAdd">
              <AttrSelect ref="arrtMainToAddSheetRow" v-show="attrShowFlag" @handleAttrItemClick="handleItemClik" @handleAutoBtnOkClick="handleAutoBtnOkClick"
                key="attrlist_list" />
            </div>
          </transition>
          <transition name="myslide-right">
            <div v-show="!attrShowFlag" class="layout_content" @click="popupAddSheetRow = false" key="noAttrList"></div>
          </transition>
        </div>
        <div class="goods_add_wrapper" id="goodsAddWrapper">
          <div class="class_add_goods">
            <AddSheetRow ref="addSheetRow" :sheet="sheet" :multiSelectFlag="multiSelectFlag" @handleHC="handleHC"
              @onItemClick="handleItemClik" @onSheetRowAdd_Attr="onSheetRowAdd_Attr"
              @popupAddSheetRowFalse="popupAddSheetRowFalse" @handleAttrShowFlagChange="handleAttrShowFlagChange"
              @onSheetRowAdd_OK="onSheetRowAdd_OK" />
          </div>
        </div>
      </div>
    </van-popup>
    <van-action-sheet v-model="showImageSelect" :actions="imageSelectActions" cancel-text="取消" description="请输入附件上传方式"
      close-on-click-action @select="onImageWaySelected" style="height: auto;" />
    <van-action-sheet v-model="bPopupUploadMethodSelect" :actions="imageSelectActions" cancel-text="取消"
      description="选择导入表单文件的方式" close-on-click-action @select="onConfirmUploadMethod" style="height: auto;" />
    <van-popup v-model="bPopupUpNotMetHint" round @cancel="bPopupUpNotMetHint = false" @confirm="bPopupUpNotMetHint = false"
      style="width: 75% !important; height: 60% !important;">

      <div style="font-size: 16px;
            margin-top: 16px;
            margin-bottom: 16px;
            font-weight: bold;
            position: sticky;
            top: 0;
            background-color: #fff;">以下商品未创建商品档案</div>
      <div id="notMetItem" :data-clipboard-text="notMetItemText" @click="copyNotMetItems"
        style="max-height:80%; overflow-y: auto; border-top: 1px solid #ddd" v-html=notMetItemHtml></div>

    </van-popup>

    <div class="progress-container" v-if="showProgress">
      <van-progress :percentage="progressPercentage" />
    </div>

    <van-popup v-model="bPopupCustomHeaderInput" round @cancel="bPopupCustomHeaderInput = false" style="width: 75% !important; height: 500px !important;">

      <div style="font-size: 16px;
            margin-top: 16px;
            margin-bottom: 16px;
            font-weight: bold;
            position: sticky;
            top: 0;
            height: 16px;
            background-color: #fff;">请输入自定义表头</div>

      <div id="customHeaderBody"
        style="padding:7px 0 ;max-height:385px; overflow-y: auto; border-top: 1px solid #ddd; display: grid; flex-direction: column; justify-content: center; align-items: center;">
        <div style="margin:  7px 0; display: flex; align-items: center;">
          <label style="margin-right: 10px;">商品名称</label><input v-model="customHeader.item_name" type="text"
            style="width: 100px; border: 1px solid #ccc; border-radius: 4px; padding: 6px;" />
        </div>
        <div style="margin: 7px 0; display: flex; align-items: center;">
          <label style="margin-right: 10px;">商品条码</label><input v-model="customHeader.barcode" type="text"
            style="width: 100px; border: 1px solid #ccc; border-radius: 4px; padding: 6px;" />
        </div>
        <div style="margin: 7px 0; display: flex; align-items: center;">
          <label style="margin-right: 10px;">商品规格</label><input v-model="customHeader.item_spec" type="text"
            style="width: 100px; border: 1px solid #ccc; border-radius: 4px; padding: 6px;" />
        </div>
        <div style="margin: 7px 0; display: flex; align-items: center;">
          <label style="margin-right: 10px;">数量单位</label><input v-model="customHeader.unit_no" type="text"
            style="width: 100px; border: 1px solid #ccc; border-radius: 4px; padding: 6px;" />
        </div>
        <div style="margin: 7px 0; display: flex; align-items: center;">
          <label style="margin-right: 10px;">商品数量</label><input v-model="customHeader.quantity" type="text"
            style="width: 100px; border: 1px solid #ccc; border-radius: 4px; padding: 6px;" />
        </div>
        <div style="margin: 7px 0; display: flex; align-items: center;">
          <label style="margin-right: 10px;">商品单价</label><input v-model="customHeader.real_price" type="text"
            style="width: 100px; border: 1px solid #ccc; border-radius: 4px; padding: 6px;" />
        </div>
        <div style="margin: 7px 0; display: flex; align-items: center;">
          <label style="margin-right: 10px;">商品总价</label><input v-model="customHeader.sub_amount" type="text"
            style="width: 100px; border: 1px solid #ccc; border-radius: 4px; padding: 6px;" />
        </div>
        <div style="margin: 7px 0 0 0; display: flex; align-items: center;">
          <label style="margin-right: 10px; vertical-align: middle;">使用小单位</label>
          <input type="checkbox" v-model="customHeader.use_s_unit"
            style="width: 20px; height: 20px; border-radius: 4px; padding: 0; margin: 0; vertical-align: middle;" />
        </div>
      </div>

      <div
        style="text-align:center; position: absolute; bottom: 0px; width: 100%;border-top: 1px solid #ddd;height:px;padding: 10px 0;">
        <van-button @click="bPopupCustomHeaderInput = false" type="default"
          style="width: 75px;height: 35px;margin-right: 10px;">取消</van-button>
        <van-button @click="bPopupCustomHeaderInput = false, bPopupUploadMethodSelect = true"
          style="width:75px;height: 35px;margin-left: 10px;">确认</van-button>
      </div>
    </van-popup>

    <van-popup v-model="bPopupAfterChangeClient" round>
      <div style="margin-left:-100px;width:400px;height:100px;">
        <van-radio-group v-model="isChangePrice" style="margin-left:150px">
          <van-radio name="true" style="font-size:20px;margin-bottom:20px;margin-top:40px">切换客户改变价格</van-radio>
          <van-radio name="false" style="font-size:20px;" v-if="canallowChangeSaleSheetPrice">切换客户不改变价格</van-radio>
        </van-radio-group>
      </div>
      <div style="text-align:center">
        <van-button @click="changeClientCancle" type="default" style="width:150px;">取消</van-button>
        <van-button @click="changeClientConfirm" type="default"style="width:150px;">确认</van-button>
      </div>

    </van-popup>
    <van-popup v-model="popupQRCodeShare" round>
      <div style="padding:20px;">
        <div style="display:flex;flex-direction:row;justify-content:center;margin-bottom:10px;">
          <img class="qrcode-logo" width="54" src="https://yingjiang.co/images/logo.svg" >
          <div style="line-height:54px;font-size:24px;"></div>
        </div>
        <div ref="qrcode" id='qrcode'></div>
      </div>
    </van-popup>
    <!-- <input type="file" id="imgFile" accept="image/*" @input="getPicture($event)" style="display:none;" /> -->
    <we-chat-sale-sheet-qr-code ref="weChatSaleSheetQrCodeRef" :sheet="sheet" />

  </div>
</template>

<script>
import {
  NavBar,
  Button,
  Toast,
  Icon,
  Popup,
  Divider,
  Field,
  Picker,
  Dialog,
  SwipeCell,
  Col,
  Row,
  Image as VanImage,
  ImagePreview,
  Checkbox,
  Cell,
  NumberKeyboard,
  CellGroup,
  CheckboxGroup, Loading,
  Tag,
  ActionSheet,
  RadioGroup,
  Radio,
  Progress
} from "vant";
import MyPreviewImage from '../VisitRecord/MyPreviewImage.vue';
import TicketAccess from '../SaleSheet/TicketAccess.js'
import AddSheetRow from './AddSheetRow'
import sheetImages from '../../util/sheetImages'
import {
  GetBillPayCode,
  GetBillStatus,
  RefundBill,
  AppSheetSaleLoad,
  AppSheetSaleSubmit,
  AppSheetSaleSave,
  AppSheetGetPrintInfo,
  AppSheetSaleOrderLoad,
  ApiSheetSaleOrderApprove,
  ApiSheetSaleOrderReview,
  ApiSheetSaleReview,
  AppSheetSaleOrderSave,
  AppSheetBuyLoad,
  AppSheetBuyOrderLoad,
  AppSheetBuySubmit,
  AppSheetBuyOrderSubmit,
  AppSheetBuySave,
  AppSheetBuyOrderSave,
  GetBranchList,
  AppSheetOrderItemLoad,
  AppSheetOrderItemSubmit,
  AppSheetOrderItemSaleSave,
  SheetSaleGetClientAccountInfo,
  ApiSheetBuyGetSupplierAccountInfo,
  AppCloudPrint_sheetTmp,
  AppCloudPrint_escCmd,
  AppSheetToEsc,
  AppSheetToImages,
  AppGetSheetToPrint,
  AppGetSheetToPrint_Post,
  AppGetTemplate,
  SheetSaleRed,
  SheetBuyRed,
  SheetBuyOrderRed,
  SheetSaleOrderRed,
  SheetOrderItemRed,
  SheetSaleDelete,
  SheetBuyDelete,
  SheetSaleOrderDelete,
  SaleSheetGetItemsInfo,
  SheetOrderItemDelete,
  BuySheetGetItemsInfo,
  AppendBriefSheetX,
  AppendBriefSheetXD,
  AppendBriefSheetCG,
  AppSheetSaleGetItemList,
  AppSheetBuyGetItemList,
  BuySheetImportItems,
  GptTableRecognition,
  KimiTableRecognition,
  GetCustomImportHeader,
  AppSheetOrderItemGetItemList,
  SetUserActionHistory, QueryDisplayTemplateActionsForFdSender,
  AppSaleGetPromotionList,
  SaveSingleImage,
  ApiPrintMark,
  AppSheetSaleOrderGetItemStockQtys,
  AppSheetNoNewSheetsAfterChecked,
  GetPrepaySheetForSub,
  ApiCanApproveSheetToday,
  ApiNoRedSheetAfterPrint,
  ApiNoRedOrderSheetAfterPrint
} from "../../api/api";
import Printing from "../Printing/Printing";
import EditSheetRow from "./EditSheetRow";
import SelectCustomer from "../components/SelectCustomer";
import SelectSupplier from "../components/SelectSupplier";
import mixins from './sheetMixin/mixin'
import TakePhoto from '../service/TakePhoto'
import ImageUtil from '../service/Image'
import Position from '../../components/Position'
import YJDotMenu from '../components/YJDotMenu';
import QrCodeTicket from "../components/wechatComponents/QrCodeTicket"
import MiniQrCode from "../components/wechatComponents/MiniQrCode"
import WeChatShare from "../components/wechatComponents/WeChatShare";
import AttrSelect from "./SaleSheetAttrSelect"
import globalVars from "../../static/global-vars";
import DisplayActionsList from "./DisplayListComponent/DisplayActionsList";
import ConcaveDottedCenter from "../components/ConcaveDottedCenter";
import SelectOne from "../components/SelectOne";
import html2canvas from 'html2canvas';
import WeChatSaleSheetQrCode from "../components/wechatComponents/WeChatSaleSheetQrCode";
import { SlickList, SlickItem } from 'vue-slicksort'
import QRcode from 'qrcodejs2'
import Clipboard from 'clipboard'
import DatePicker from 'vue2-datepicker';
import 'vue2-datepicker/index.css';
import 'vue2-datepicker/locale/zh-cn';

export default {
  name: "SaleSheet",
  mixins: [mixins],
  created() {


    this.$on('handleSelectedSheetRowsEmitEvent', (arg) => {
      this.handleSelectedSheetRows()
    })
    /*
          history.pushState(null, null, document.URL);
          window.addEventListener('popstate', function () {
          history.pushState(null, null, document.URL);
        })*/
  },
  data() {
    return {
      previewImageUrls:[],
      previewItemImageUrl:"",
      previewShow: false,    // 控制预览显示
      previewIdx: 0,         // 当前预览的图片索引
      showPrices:[],
      clientInfoObj:{},
      isChangePrice:"true",
      isTmpPriting: true, // 判断是否有可用模板
      selectedTmpId: 0, // 选择模板自动赋值
      redBrief: '',
      showImagePreviewFlag:false,
      arrCompanyNamesToPrint: [],
      companyNameToPrint: '',
      tmplist: [],
      printTemplates: [],
      printTemplatesTiny: [],
      templatesLoaded: false,
      useTmp: false,
      printCount: 1,
      isInPrintView: false,  // false审核界面 true打印详情
      switchStyleCount: 0,
      timerHandle: '',
      isTerse: false,// 控制改前价
      imageFlag: false,
      order_source: '',
      fromDelivery: false,
      qtyLoaded: false,
      showTemplate: false,
      loadingMsg: '',
      isPrinting: false,
      sheetRowCount: 0,
      kaiguan: 0,
      supData: {},
      searchStr: "",
      selectedPayway: "",
      showUnsubmitedSheets: false,
      unsubmitedSheets: [],
      currentTime: "",
      printBarcodeStyleForSale: [],
      printBarcodePic: false,
      //dialog popup control
      needKeyboardField: '',
      keyboardText: "",
      canNotChangeClientReason: '',
      bPopupAfterChangeClient: false,
      bPopupClientSelectDialog: false,
      bPopupBranchPicker: false,
      bPopupOrderAccountPicker: false,
      bPopupSenderPicker: false,
      bPopupSelectSendTime:false,
      branchListAll: [],
      branchList: [],
      branchListForSale:[],
      branchListForReturn: [],
      OrderAccountList: [],
      senderList: [],
      imageSrc1: '',
      imageSrc2: '',
      qrcode1: '',
      qrcode2: '',
      qrcode3: '',
      sup_group: '',
      sup_rank: '',
      sup_regions: '', // 即other_region字段, 格式如‘/125/300/’
      isSharingSheetImage:false,

      sheet: {
        sheet_id: "",
        sheet_no: "",
        left_amount: 0,
        now_disc_amount: 0,
        disc_amount: 0,
        total_amount: 0,
        total_profit: 0,

        now_pay_amount: 0,
        approve_time: "",
        payway1_id: "",
        payway1_name: "",
        payway1_amount: 0,
        payway2_id: "",

        payway2_name: "",
        payway2_amount: 0,
        payway3_id: "",
        payway3_name: "",
        payway3_amount: 0,

        payway1_channel: null,
        payway2_channel: null,
        payway3_channel: null,
        payway1_status: '',
        payway2_status: '',
        payway3_status: '',

        payway_code: '',
        appendixPhotos: [],
        displayGiveProofs: [], // 兑付凭证
        supcust_id: "",
        branch_id: "",
        make_brief: "",
        sheetRows: [],
        seller_id: '',
        seller_name: '',
        senders_id: '',
        senders_name: '',
        getter_id: '',
        getter_name: '',
        acct_type: '',
        promotion_fulldisc_used: false,   // 促销活动:是否已经使用过了满减
        promotion_fullgift_content: '',   // 促销活动:满赠内容
        promotionLoaded: false,
        acct_type_name:'',
        clientAllowChangePrice:true,
        prepay_sheet_info: '',    //预收款单据信息
      },
      is_sender: false, // 是否是送货员
      appOperIsDefSender: false,  // 登陆人有送货员身份，送货员就默认登陆人
      billPayPanel: {
        show: false,
        payAmount: 0,
        qrCode: '',
      },
      isBillPaid: false,
      checkBillPaidInterval: undefined,

      //printTmpVariables:{},
      onloadBrief: [],
      onloadPayways: [],
      paywaysForSelect: [],
      editingItem: {},
      EditSheetRowIndex: "",
      popupQRCodeShare:false,
      bGenerateQRCode:false,
      popupEditSheetRowPannel: false,
      popupSubmitPannel: false,
      popupPaywayPannel: false,
      selectingPaywayIndex: '1',
      popupSubImage1: false,
      popupSubImage2: false,
      showPrePaySheet: false,//预收款dialog
      prepayBalanceDetail: [],//预收款
      prepay_sheet_infos: {}, // 存储输入的信息
      payamounts: [], //存储paywayid
      current_subId: 0,
      showPayWay1: false,
      showPayWay2: false,
      showPayWay3: false,
      payway1_balance: 0,
      payway2_balance: 0,
      payway3_balance: 0,

      IsSubmiting: false,
      submitClicked: false,

      arrearsInfo: "",
      prepayInfo: "",
      dispInfo: "",

      xAmount: 0,
      tAmount: 0,
      selectedSenders: [],
      sumQuantityUnitConv: "",
      selectSheetRowConf: {
        'X': { showMsg: '销', style: 'color:rgba(245, 108, 108, 0.8)' },
        'T': { showMsg: '退', style: 'color:rgba(245, 108, 108, 0.8)' },
        'CT': { showMsg: '采退', style: 'color:rgba(245, 108, 108, 0.8)' },
        'DH': { showMsg: '定', style: 'color:rgba(245, 108, 108, 0.8)' },
        'J': { showMsg: '借', style: 'color:rgba(245, 108, 108, 0.8)' },
        'H': { showMsg: '还', style: 'color:#9581ee' },
        'HR': { showMsg: '换入', style: 'color:rgba(245, 108, 108, 0.8);' },
        'HC': { showMsg: '换出', style: 'color:rgba(245, 108, 108, 0.8)' },
        'KS': { showMsg: '客损', style: 'color:rgba(245, 108, 108, 0.8)' },
        'CL': { showMsg: '陈列', style: 'color:rgba(245, 108, 108, 0.8)' }, // 备用
      },
      // publicBox3Height: '',
      pageHeight: '',
      ksPayWayObj: null,
      copyFlag: false,
      appendBrief: '',
      showAppendBriefDialog: false,
      // moreOptions: false,
      weChatInfo: [],
      popupAddSheetRow: false,
      attrShowFlag: false,
      multiSelectFlag: false,
      multiSelectItemShow: false,
      sheetsFromeShowAccount: [],
      redSheetIndex: -1,
      fromShowAccount: false,
      userActionHistory: {
        "tm": "",
        "act": "",
        "sn": "",
        "rs": ""
      },
      sheetAttributeActionKey: ['fd_seller', 'fd_sender', 'cx_give'],
      msgId: '',
      bluetoothDeviceInputFlag: false,

      salePromotionCombines: [],
      salePromotionSecKills: [],
      salePromotionFullDiscs: [],
      salePromotionFullGifts: [],
      salePromotionCashPrizes: [],

      printers: [],
      defaultPrinter: {},
      defaultPrinterIndex: 0,
      PopupPrintersSelect: false,
      customHeader: { "item_name": "", "barcode": "", "item_spec": "", "unit_no": "", "quantity": "", "real_price": "", "sub_amount": "", "use_s_unit": true },
      bPopupUploadMethodSelect: false,
      bPopupUpNotMetHint: false,
      bPopupCustomHeaderInput: false,
      notMetItemHtml: "",
      notMetItemText: "",
      sheetHasChange: false,
      isChangingSheetByCode: false,
      showImageSelect: false,
      imageSelectActions: [{ name: '拍照' }, { name: '相册' }],
      progressPercentage: 0,
      showProgress: false,
      intervalId: null,

      dispAccounts: [],
      noArrearsBills: false,  // 未给欠条
    };
  },
  computed: {
    isSaleNeedMarkIOU(){
      return this.$store.state.operInfo.setting && this.$store.state.operInfo.setting.saleNeedMarkIOU && this.$store.state.operInfo.setting.saleNeedMarkIOU.toLowerCase()==="true"?true:false
    },
    isLoadVirtualProduceDate(){
      return this.$store.state.operInfo.setting && this.$store.state.operInfo.setting.loadVirtualProduceDate && this.$store.state.operInfo.setting.loadVirtualProduceDate.toLowerCase()=="false"?false:true
    },
    isShowVirtualProduceDate(){
      return this.$store.state.operInfo.setting && this.$store.state.operInfo.setting.batchType && this.$store.state.operInfo.setting.batchType=="-1"?false:true
    },
    payBillStatusText() {
      return this.sheet.payb_status === 'paid' ? '已在线支付' : ''
    },
    isPopupVisible() {
      return this.$popup.popupStack.length > 0
    },
    calcThreeMonthAgo() {
      var dt = new Date();
      dt.setMonth(dt.getMonth() - Number(3));
      console.log("dt.toLocaleString().replace(/\//g, '-')", dt.format("yyyy-MM-dd"))
      return isiOS ? dt.format("yyyy-MM-dd") : dt.format("yyyy-MM-dd").replace(/\//g, '/')
    },
    paidAmountInfo(){
      if(this.sheet.approve_time && ',X,T,CG,CT,'.indexOf(this.sheet.sheetType)>=0){
        var total = Math.round(this.sheet.total_amount * 100);
        var paid = Math.round(this.sheet.paid_amount * 100);
        var disc = Math.round(this.sheet.disc_amount * 100);
        var leftAmt = (total - paid - disc) / 100;
        if(Math.abs(leftAmt)<0.01) leftAmt=0
        var s=''
        if(this.sheet.paid_amount>0) s+="已结:"+this.sheet.paid_amount
        if(this.sheet.disc_amount>0) s+="已惠:"+this.sheet.disc_amount
        if(leftAmt>0) s+="尚欠:" + leftAmt
        return s
      }
      else return ''
    },
    logoUrl() {
      return globalVars.wechatConf.imgUrl
    },
    calcNowDate() {
      var nt = new Date();
      return isiOS ? nt.format("yyyy-MM-dd") : nt.format("yyyy-MM-dd").replace(/\//g, '/')
    },
    canAppSelectSeller() {
      var appSelectSeller = window.getRightValue("delicacy.appSelectSeller.value");
      return appSelectSeller == "true"
    },
    canAppInputSendTime() {
      var appInputSendTime = window.getRightValue("delicacy.appInputSendTime.value");
      return appInputSendTime == "true"
    },
    canallowChangeSaleSheetPrice() {
      var allowChangeSaleSheetPrice = window.getRightValue("delicacy.allowChangeSaleSheetPrice.value");
      return allowChangeSaleSheetPrice == "true"
    },
    canAppSelectSender() {
      var appSelectSeller = window.getRightValue("delicacy.appSelectSender.value");
      if (appSelectSeller == "true") return true
      else if (appSelectSeller == 'false') return false

      var setting = this.$store.state.operInfo.setting
      var b = false
      if (setting) b = setting.appSaleUseSender
      return b && b.toString().toLowerCase() == 'true'

    },
    canAppSelectGetter() {
      var appSelectGetter = window.getRightValue("delicacy.appSelectGetter.value")
      return appSelectGetter == "true" && ',X,T,CG,CT,DH,'.indexOf(',' + this.sheet.sheetType + ',') >= 0
    },
    canRed() {
      if (this.sheet.sheetType == "X") return hasRight("sale.sheetSale.red") || hasRight("orderFlow.orderToSale.red");
      else if (this.sheet.sheetType == "T")
        return hasRight("sale.sheetReturn.red") || hasRight("orderFlow.orderToSale.red");
      else if (this.sheet.sheetType == "XD")
        return hasRight("sale.sheetSaleOrder.red");
      else if (this.sheet.sheetType == "TD")
        return hasRight("sale.sheetReturnOrder.red");
      else if (this.sheet.sheetType == "CG")
        return hasRight("buy.sheetBuy.red");
      else if (this.sheet.sheetType == "CT")
        return hasRight("buy.sheetBuyReturn.red");
      else if (this.sheet.sheetType == "CD")
        return hasRight("buy.sheetBuyOrder.red");
      else if (this.sheet.sheetType == "DH")
        return hasRight("sale.orderItemSheet.red");
      else if (this.sheet.sheetType == "BQ")
        return hasRight("sale.sheetLabelPrint.red");
    },
    canDelete() {
      if (this.sheet.sheetType == "X") return hasRight("sale.sheetSale.delete");
      else if (this.sheet.sheetType == "T")
        return hasRight("sale.sheetReturn.delete");
      else if (this.sheet.sheetType == "XD")
        return hasRight("sale.sheetSaleOrder.delete");
      else if (this.sheet.sheetType == "TD")
        return hasRight("sale.sheetReturnOrder.delete");
      else if (this.sheet.sheetType == "CG")
        return hasRight("buy.sheetBuy.delete");
      else if (this.sheet.sheetType == "CT")
        return hasRight("buy.sheetBuyReturn.delete");
      else if (this.sheet.sheetType == "CD")
        return hasRight("buy.sheetBuyOrder.delete");
      else if (this.sheet.sheetType == "DH")
        return hasRight("sale.orderItemSheet.delete");
      else if (this.sheet.sheetType == "BQ")
        return hasRight("sale.sheetLabelPrint.delete");
    },
    canNagativePrepay() {
      return hasRight("delicacy.allowNegativePrepay.value");
    },
    canMake() {
      if (this.sheet.sheetType == "X")
        return hasRight("sale.sheetSale.make") || hasRight("orderFlow.orderToSale.make") || hasRight("sale.saleFromOrder.make")
      else if (this.sheet.sheetType == "T")
        return hasRight("sale.sheetReturn.make") || hasRight("orderFlow.orderToSale.make") || hasRight("sale.saleFromOrder.make")
      else if (this.sheet.sheetType == "XD")
        return hasRight("sale.sheetSaleOrder.make")
      else if (this.sheet.sheetType == "TD")
        return hasRight("sale.sheetReturnOrder.make")
      else if (this.sheet.sheetType == "CG")
        return hasRight("buy.sheetBuy.make")
      else if (this.sheet.sheetType == "CT")
        return hasRight("buy.sheetBuyReturn.make")
      else if (this.sheet.sheetType == "CD")
        return hasRight("buy.sheetBuyOrder.make");
      else if (this.sheet.sheetType == "DH")
        return hasRight("sale.orderItemSheet.make")
      else if (this.sheet.sheetType == "BQ")
        return hasRight("sale.sheetLabelPrint.make")
    },
    canApprove() {
      //|| hasRight("sale.saleFromOrder.approve")  以后要删掉
      if (this.sheet.sheetType == "X")
        return hasRight("sale.sheetSale.approve") || hasRight("orderFlow.orderToSale.approve") || hasRight("sale.saleFromOrder.approve")
      else if (this.sheet.sheetType == "T")
        return hasRight("sale.sheetReturn.approve") || hasRight("orderFlow.orderToSale.approve") || hasRight("sale.saleFromOrder.approve")
      else if (this.sheet.sheetType == "XD")
        return hasRight("sale.sheetSaleOrder.approve")
      else if (this.sheet.sheetType == "TD")
        return hasRight("sale.sheetReturnOrder.approve")
      else if (this.sheet.sheetType == "CG")
        return hasRight("buy.sheetBuy.approve")
      else if (this.sheet.sheetType == "CT")
        return hasRight("buy.sheetBuyReturn.approve")
      else if (this.sheet.sheetType == "CD")
        return hasRight("buy.sheetBuyOrder.approve");
      else if (this.sheet.sheetType == "DH")
        return hasRight("sale.orderItemSheet.approve")
    },
    canReview() {
      if (this.sheet.sheetType == "XD")
        return hasRight("sale.sheetSaleOrder.review")
      else if (this.sheet.sheetType == "TD")
        return hasRight("sale.sheetReturnOrder.review")
      else if (this.sheet.sheetType == "X")
        return hasRight("sale.sheetSale.review")
      else if (this.sheet.sheetType == "T")
        return hasRight("sale.sheetReturn.review")
      else if (this.sheet.sheetType == "CG")
        return hasRight("buy.sheetBuy.review")
      else if (this.sheet.sheetType == "CT")
        return hasRight("buy.sheetBuyReturn.review")
      else if (this.sheet.sheetType == "CD")
        return hasRight("buy.sheetBuyOrder.review");
      else if (this.sheet.sheetType == "DH")
        return hasRight("sale.orderItemSheet.review")
    },
    canPrint() {
      var path ="",path2=""
      if (this.sheet.sheetType == "XD")
        path="sale.sheetSaleOrder.print"
      else if (this.sheet.sheetType == "TD")
        path="sale.sheetReturnOrder.print"
      else if (this.sheet.sheetType == "X"){
        path="sale.sheetSale.print"
        if(this.sheet.order_sheet_id) path2="sale.orderToSale.print"
      }
      else if (this.sheet.sheetType == "T"){
        path="sale.sheetReturn.print"
        if(this.sheet.order_sheet_id) path2="sale.orderToSale.print"
      }
      else if (this.sheet.sheetType == "CG")
        path="buy.sheetBuy.print"
      else if (this.sheet.sheetType == "CT")
        path="buy.sheetBuyReturn.print"
      else if (this.sheet.sheetType == "CD")
        path="buy.sheetBuyOrder.print"
      else if (this.sheet.sheetType == "DH")
        path="sale.orderItemSheet.print"

      var res,res2
      if(path){
        res = window.getRightValue(path)
      }
      if(path2){
        res2 = window.getRightValue(path2)
        if(res ?.toLowerCase()=="false" && res2 ?.toLowerCase()=="false")
          return false
      }
      else if(res?.toLowerCase()=="false") return false

      return true

    },
    inPrivateMode() {
      return this.$store.state.inPrivateMode
    },
    canSyncTicketSys() {

       var operInfo= this.getOperInfo()
       if(!operInfo){
         alert('the operInfo is ' + operInfo)
       }

       if(operInfo  && operInfo.setting?.openTicketAccessSys === 'True') return true;
       else return false;
    },
    sheetStatusForPrint() {
      return window.getRightValue('delicacy.sheetStatusForPrint.value');
    },
    allowMobilePicImport() {
      console.log('allowMobilePicImport' + window.getRightValue('delicacy.allowMobilePicImport.value'))
      return window.getRightValue('delicacy.allowMobilePicImport.value') === 'true';
    },
    allowPrintBeforeApprove() {
      return hasRight("delicacy.allowPrintBeforeApprove.value");
    },
    canSeePrice() {
      if (',CG,CT,CD,'.indexOf(',' + this.sheet.sheetType + ',') >= 0) {
        if (!hasRight('delicacy.seeInPrice.value')) {
          return false
        }
        if (this.inPrivateMode) return false
      }
      else if (',X,T,XD,TD,'.indexOf(',' + this.sheet.sheetType + ',') >= 0) {
        if (!hasRight('delicacy.seeSalePrice.value', true)) {
          return false
        }
      }
      return true
    },
    seeProfit() {
      return false
      return hasRight("delicacy.seeProfit.value");
    },
    //允许欠款
    canAllowSheetArrears() {
      if (this.sheet.sheetType === 'X' || this.sheet.sheetType === 'DH') {
        var allowSheetArrears = window.getRightValue('delicacy.allowSheetArrears.value')
        if (Number(this.sheet.left_amount) !== 0) {
          return true
        }
        //if(allowSheetArrears === 'true') {
        if (allowSheetArrears === 'false') {//默认允许欠款
          return false
        }
        return true
      }
      return true
    },
    canAllowSheetDiscAmount() {
      if (this.sheet.sheetType == "X" || this.sheet.sheetType == "XD") {
        var allowSheetDiscAmount = window.getRightValue('delicacy.allowSheetDiscAmount.value')
        if (Number(this.sheet.now_disc_amount) !== 0) return true
        if (allowSheetDiscAmount === 'false') return false
        return true
      }
      return true
    },

    appShowBarcode() {
      const setting = this.$store.state.operInfo.setting
      var b = 'notshow'
      if (setting && setting.showBarcode) b = setting.showBarcode
      return b
    },
    appSheetShowItemSpec() {
      var s = getSettingValue('appSheetShowItemSpec')
      return s.toLowerCase() == "true"
    },
    appSheetItemShowStyle() {
      const setting = this.$store.state.operInfo.setting
      var s = getSettingValue('appSheetItemShowStyle')
      var longNames = 0;
      if (s == 'auto') {
        if (this.sheet.sheetRows.length == 0) return 'loose'
        this.sheet.sheetRows.forEach(row => {
          if (row.name.length > 8) longNames++
        })
        if (parseFloat(longNames) / this.sheet.sheetRows.length > 0.3) {
          return 'loose'
        }
        else return 'compact'
      }
      else if (s == "") {
        return "compact"
      }
      return s
    },
    appSheetUseAssistQty() {
      return getSettingValue('appSheetUseAssistQty').toLowerCase() === "true"
    },
    appCalcDiscount() {
      var b = window.getRightValue('delicacy.appCalcDiscount.value')
      if (b && b.toString() == "false") return false; else return true
    },
    appShowDiscount() {
      var b = window.getRightValue('delicacy.appShowDiscount.value')
      return (b && b.toString() == "true")
    },
    appUseVirtualProduceDate() {
      const setting = this.$store.state.operInfo.setting
      var b = false
      if (setting) b = setting.appUseVirtualProduceDate
      return b && b.toString().toLowerCase() == 'true'
    },
    appPriceBeforeChange() {
      const setting = this.$store.state.operInfo.setting
      var b = false
      if (setting) b = setting.appPriceBeforeChange
      return b && b.toString().toLowerCase() == 'true'
    },
    appUseSn() {
      const setting = this.$store.state.operInfo.setting
      var b = false
      if (setting) b = setting.appSaleUseSn
      //console.log("appUseSn:",b)
      return b && b.toString().toLowerCase() == 'true'
    },
    qrCodeParams() {
      return {
        company_id: this.sheet.company_id,
        supcust_id: this.sheet.supcust_id,
        mobile: this.sheet.mobile,
        contact_nick_name: this.sheet.sup_name
      }
    },
    // allowWeChatShare() {
    //   return true
    // },
    showClientInfoDom() {
      return this.sheet.supcust_id !== 0 &&
        this.sheet.sup_name &&
        (this.prepayInfo !== '' || this.arrearsInfo !== '' || this.dispInfo !== '' || this.sheet.acct_cust_name !== '' || this.sheet.now_pay_amount !== '')
    },
    displayGiveProofsType() {
      let typeActionObj = {
        key: 'cx_give',
        name: '车销兑付行为'
      }
      const typeActionSelect = [
        [
          () => this.sheet.sheetType === 'XD',
          () => {
            typeActionObj.key = 'fd_seller'
            typeActionObj.name = '访单业务员兑付行为'
          }
        ],
        [
          () => this.sheet.sheetType === 'X' && this.sheet.order_sheet_id !== '',
          () => {
            typeActionObj.key = 'fd_sender'
            typeActionObj.name = '访单送货员兑付行为'
          }
        ],
        [
          () => this.sheet.sheetType === 'X' && this.sheet.order_sheet_id === '',
          () => {
            typeActionObj.key = 'cx_give'
            typeActionObj.name = '车销兑付行为'
          }
        ]
      ]
      const target = typeActionSelect.find(item => item[0]())
      if (target) {
        target[1]()
      }
      return typeActionObj
    },

    negativeDiscAmt() {
      let right = window.getRightValue('delicacy.negativeDiscAmt.value')
      //if (right === '') return false
      return !right || right.toString() !== "false"
    },
    negativeLeftAmt() {
      let right = window.getRightValue('delicacy.negativeLeftAmt.value')
      //if (right === '') return false
      return !right || right.toString() !== "false"
    },
    negativePayAmt() {
      let right = window.getRightValue('delicacy.negativePayAmt.value')
      //if (right === '') return false
      return !right || right.toString() !== "false"
    },
    noStockAttrSplitShow() {
      return window.getSettingValue('noStockAttrSplitShow').toLowerCase() == 'true'
    },
    appOcrEngine() {
      const ocrEngineSetting = this.$store.state.operInfo?.setting?.ocrEngine
      return ocrEngineSetting || ''
    },
    isNoImagesInAllItems() {
      if (Array.isArray(this.sheet.sheetRows)) {
        this.sheet.sheetRows.forEach((item, index) => {
        });
        return this.sheet.sheetRows.every(item => {
          return item.showImages && item.showImages.tiny === '';
        });
      }
      return false;
    }
  },
  // 动态初始化
  activated() {
    //this.iSreades = this.$route.query.iSreades === false? this.$route.query.iSreades:true;
    // history.pushState(null, null, document.URL);
    this.searchStr = "";
    //this.changOrderPayWay();
    //this.changeHeight();
    let that = this
    window.sayCode = function (result) {
      that.pageSayCode(result)
    }
    setTimeout(() => {
      this.$forceUpdate()
    }, 300)
  },
  // beforeUpdate() {
  //   this.changeHeight();
  // },
  watch: {
    "$store.state.currentSheet": function () {
      if (this.$store.state.currentSheet) {
        this.updateTotalAmount();
      }
    },
    bPopupClientSelectDialog(val) {
      if (val) {
        window.addBackHandler(() => {
          this.bPopupClientSelectDialog = false
        })
      }
      else {
        window.popBackHandler()
      }
    },
    printBarcodeStyleForSale: {
      handler: function (val, oldVal) {
        this.$store.state.printBarcodeStyleForSale = val
      },
      deep: true

    },
    printBarcodePic: {
      handler: function (val, oldVal) {
        this.$store.state.printBarcodePic = val
      },
      deep: true
    },
    "billPayPanel.show": {
      handler: function () {
        if (this.billPayPanel.show) {
          this.checkBillPaidInterval = setInterval(() => {
            this.checkBillPaid()
          }, 2000);
        } else {
          clearInterval(this.checkBillPaidInterval)
        }
      },
      deep: true
    },
    "sheet.sheetRows": {
      handler: function () {
        if (this.sheet.sheetRows === [] || this.sheet.sheetRows === undefined)
          return;

        this.sheetRowCount = this.sheet.sheetRows.length;
        let ksFlag = false
        for (let i = 0; i < this.sheetRowCount; i++) {
          if (this.sheet.sheetRows[i].trade_type == 'KS') {
            ksFlag = true
            break
          }
        }

        this.onSheetRowsChanged()

        //this.changeKsPayWay();
        // 添加商品，修改支付方式
        //this.changOrderPayWay();
        //  this.changPayWayBySheetRows()
        // 商品改变，进行结算方式联动
        // this.updateTotalAmount();
        // this.updatePayAmount();
        // this.handleAcctType()
        // this.calcSaleAndReturnAmount()
        // this.calculateSumQuantityUnitConv()
        //this.handleSelectedSheetRows()
      },
      deep: true
    },
    // 'sheet.make_brief': {
    //   handler: function (val, oldVal) {
    //     this.changeHeight()
    //   },
    //   deep: true
    // },
    ksPayWayObj: {
      handler: function (val, oldVal) {
        this.$store.commit('ksPayWayObj', val)
      },
      deep: true
    },
    sheet: {
      deep: true,
      handler() {
        if (this.sheet.sheetType == 'X') {
          if (this.sheet.sheet_id && !this.sheet.approve_time && !this.isChangingSheetByCode) {

            this.sheetHasChange = true
          }
          if (!this.sheet.approve_time && this.sheet.left_amount === 0){
            this.noArrearsBills = false
          }

        }

      }
    }
  },
  props: {
    isPopup: {
      type: Boolean,
      default: false
    },
    saleSheetShowFlag: { // 历史单据打开，阻止已选信息
      type: Boolean,
      default: false
    }
    /*
    saleSheetQuery: {
      type: Object,
      default: function () {
        return {}
      }
    }*/

  },
  mounted() {
    console.log('entering....................||||||||||||||||||||||.........................')

    this.templatesLoaded = false
    var sheetType = ''
    var fromOrderSheet = ''
    window.redChangeSheet=null
    var sheetID = ''
    sheetType = this.$route.query.sheetType;
    this.msgId = this.$route.query.msgId ? this.$route.query.msgId : ''
    this.$store.commit('saleSheetAddSheetRowState', 'X')
    this.companyNameToPrint = this.$store.state.companyNameToPrint || ''
    var rights = this.$store.state.operInfo.operRights;
    // 转单标记
    let fromOtherSheet = false
    let sheetToOtherParams = this.$route.query.sheetToOtherParams
    if (sheetToOtherParams) {
      fromOtherSheet = true
      Toast.loading({
        message: '转单中...',
        forbidClick: true,
        loadingType: 'spinner',
        duration: 0
      });
    }

    if (sheetType) { // 历史单据（由路由跳转改为弹窗）
      this.fromDelivery = this.$route.query.fromDelivery;
      if (this.$route.query.order_source) {
        this.order_source = this.$route.query.order_source
      } else {
        this.order_source = ''
      }
      fromOrderSheet = this.$route.query.fromOrderSheet;//判断是否为转单单据
      sheetID = this.$route.query.sheetID || "";
      if (sheetID) {
        this.showUnsubmitedSheets = false;
      }

      this.loadSheet(sheetType, sheetID, fromOrderSheet, fromOtherSheet);

      var companyNames = window.getSettingValue('companyNamesToPrint')
      if (companyNames) {
        this.arrCompanyNamesToPrint = companyNames.split('\n')
      }

    }
    /*
    if(!this.isPopup) { // 历史单据（由路由跳转改为弹窗）
      sheetType = this.$route.query.sheetType;
      fromOrderSheet = this.$route.query.fromOrderSheet;//判断是否为转单单据
      sheetID = this.$route.query.sheetID || "";
      if (sheetID) {
        this.showUnsubmitedSheets = false;
      }
      var forceVisit = this.$route.query.forceVisit;
      this.loadSheet(sheetType, sheetID, fromOrderSheet,forceVisit==='true');
    } else {
      sheetType = this.saleSheetQuery.sheetType
      sheetID = this.saleSheetQuery.sheetID
      this.loadSheet(sheetType, sheetID);
    }
    */

    //this.changOrderPayWay();
    this.printBarcodeStyleForSale = this.$store.state.printBarcodeStyleForSale
    if (!this.printBarcodeStyleForSale) {
      if (this.$store.state.printBarcodeStyle == 'noBarcode' || !this.$store.state.printBarcodeStyle) this.printBarcodeStyleForSale = [];
      else this.printBarcodeStyleForSale = [this.$store.state.printBarcodeStyle];
    }


    this.printBarcodePic = this.$store.state.printBarcodePic
    // this.pageHeight = this.$refs.pages.offsetHeight
    // this.changeHeight();
    this.appendBrief = ''
    let that = this
    window.sayCode = function (result) {
      that.pageSayCode(result)
    }
    if (this.fromDelivery) {
      that = this; // surf. setTimeout内部的this指向与外部不同。
      //let operkey_temp = this.$store.state.operKey
      setTimeout(function () {
        var itemIds = []
        that.sheet.sheetRows.forEach(sheetRow => {
          itemIds.push(sheetRow.item_id)
        })
        if (itemIds.length == 0) {
          return;
        }
        let params = {
          //operKey: operkey_temp,
          itemIds: itemIds.join(','),
          branch_id: that.sheet.branch_id
        }
        AppSheetSaleOrderGetItemStockQtys(params).then(res => {
          if (res.msg == '') {
            that.sheet.sheetRows.forEach(sheetRow => {
              res.stock_qtys.forEach(stockQty => {
                if (sheetRow.item_id == stockQty.item_id) {
                  //this.$set(this.params, 'isShow', true)
                  sheetRow.stock_qty = stockQty.stock_qty
                }
              })
            })
            that.qtyLoaded = true; // !important. 调整v-if参数以使v-for刷新
          }
        })
      }, 300) // 延时执行。
    }
    this.loadPromotions();

    // 获取当前公司的打印机列表
    let printers = window.getCompanyStoreValue('c_printers')
    // 兼容旧版本
    if (!printers) {
      printers = []
      const printer = {
        id: this.$store.state.printerID ? this.$store.state.printerID : this.$store.state.printerID,
        name: this.$store.state.printer_name || '蓝牙打印机',
        type: this.$store.state.printerType || 'bluetooth',
        kind: this.$store.state.printer_kind || 'tiny',
        paperSize: this.$store.state.paperSize || '58',
        bluetoothID: this.$store.state.printerID,
        bluetoothType: 'classic',
        bluetoothDataStyle: this.$store.state.bluetoothSendDataStyle,
        useTemplate: this.$store.state.useTemplateToPrint === true || this.$store.state.printStyle == 'template',

        brand: this.$store.state.printer_brand || '',
        cloudID: this.$store.state.device_id || '',
        cloudCode: this.$store.state.check_code || '',

        isDefault: true
      }
      printers.push(printer)
    }
    this.printers = printers

    for (let _i = 0; _i < printers.length; _i++) {
      const _printer = printers[_i]
      if (_printer.isDefault) {
        this.defaultPrinter = _printer;
        this.defaultPrinterIndex = _i;
        break;
      }
    }
    if(rights && rights.delicacy && rights.delicacy.appOperIsDefSender && rights.delicacy.appOperIsDefSender.value){
      this.appOperIsDefSender = true
    }
    this.is_sender = this.$store.state.operInfo.is_sender == 'True' ? true : false
    // 初始化进入单据，清空该单据购物车，防止缓存上个客户购物车，切换到其他客户情况
    this.$store.commit("shoppingCarObj", {
      clearFlag: true,
      sheetType: this.sheet.sheetType
    })
  },
  unmounted(){
    window.redChangeSheet=null

  },

  // 监听返回-如果向上层返回就清空数据
  beforeRouteLeave(to, from, next) {
    const toIndex = to.meta.index;
    const fromIndex = from.meta.index;
    // 离开路由进行还原，防止不必要的触发
    this.bluetoothDeviceInputFlag = false
    to.query.indexActive = 1;

    if (to.name == "ShowAccount") {
      to.query.sheets = this.sheetsFromeShowAccount
    }

    //if (this.sheet.red_flag == "1") to.query.reded_sheet_id=this.sheet.sheet_id


    if (fromIndex > toIndex) {
      // this.sheet = {};
      window.redChangeSheet=null

    }
    // from 代表自己
    // Dialog.confirm({
    //   title: "离开校验",
    //   message: "请确认是否离开?",
    // })
    // .then(() => {
    //   Toast("我离开了")
    next()
    //   // this.reload(0)
    // })
    // .catch(() => {
    //   Toast("我留下了");
    //   next(false);
    // });
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      if (from.name == "ShowAccount") {
        vm.sheetsFromeShowAccount = to.query.sheets
        vm.fromShowAccount = true
      }
      // 通过 `vm` 访问组件实例
    })
    // ...
  },
  components: {
    DatePicker,
    SlickItem,
    SlickList,
    WeChatSaleSheetQrCode,
    ConcaveDottedCenter,
    DisplayActionsList,
    "my-preview-image": MyPreviewImage,
    "van-action-sheet": ActionSheet,
    "van-tag": Tag,
    "van-nav-bar": NavBar,
    "van-button": Button,
    "van-icon": Icon,
    "van-popup": Popup,
    "van-divider": Divider,
    "van-field": Field,
    "van-picker": Picker,
    "van-swipe-cell": SwipeCell,
    "van-image": VanImage,
    "van-radio-group":RadioGroup,
    "van-radio" :Radio,
    "van-progress": Progress,
    [ImagePreview.Component.name]: ImagePreview.Component,
    EditSheetRow: EditSheetRow,
    "van-row": Row,
    "van-col": Col,
    "van-checkbox": Checkbox,
    "van-number-keyboard": NumberKeyboard,
    "van-cell": Cell,
    "van-cell-group": CellGroup,
    "van-checkbox-group": CheckboxGroup,
    SelectCustomer,
    SelectSupplier,
    [Dialog.Component.name]: Dialog.Component,
    "van-loading": Loading,
    QrCodeTicket,
    WeChatShare,
    AddSheetRow,
    MiniQrCode,
    AttrSelect,
    "yj-dot-menu": YJDotMenu,
    "select-one": SelectOne
  },
  methods: {
    onCheckboxClick(event) {
    console.log('Checkbox clicked!', event);
  },
    onThisPageClose(params){
    params.sheet_id=this.sheet.sheet_id
    if (this.sheet.red_flag == "1") params.action='red'
  },
  showPreviewItemPhoto(photos){

  },
    setBranchForReturn() {
      let branchForReturn = window.getSettingValue('branchForReturn')
      let branchForOrderReturn = window.getSettingValue('branchForOrderReturn')
      if(this.sheet.sheetType === 'XD' ||this.sheet.sheetType === 'TD') {
        branchForReturn = branchForOrderReturn
      }

      if (!branchForReturn) return
      if (this.sheet.sheet_id) return
      this.branchListForReturn.some(e => {
        if (e.branch_id == branchForReturn) {
          this.sheet.branch_id = branchForReturn
          this.sheet.branch_name = e.branch_name
          return true
        } else {
          return false
        }
      })
    },
    getSearchInputWidth() {
      let width = 240
      // 需要时可以加其他条件
      // if(this.sheet.sheetType === 'CG'){
      //   width = width - 50
      // }
      if ((this.sheet.sheetType === 'CG' || this.sheet.sheetType === 'X') && this.allowMobilePicImport) {
        width = width - 40
      }
      console.log('inputWidth:' + width)
      return width + '' + 'px'
    },
    changeClientCancle() {
      this.bPopupAfterChangeClient = false
    },
    changeClientConfirm() {

      this.handleChangeSupcustInfo(this.clientInfoObj)
      this.$store.commit("clearPartOfSheetState", '')
      this.$store.commit("shoppingCarObj", {
        clearFlag: true,
        sheetType: this.sheet.sheetType
      })
      for (let i = 0; i < this.sheet.sheetRows.length; i++) {
        var row = this.sheet.sheetRows[i]
        if (row.order_sub_id || row.disp_flow_id) {
          this.sheet.sheetRows.splice(i, 1);
          i--;
        }
      }
      // 此处进行普通商品改价操作
      if (this.isChangePrice == "true") {
        var items_id = ''
        this.sheet.sheetRows.forEach(sheetRow => {
          if (items_id != '') items_id += ','
          items_id += sheetRow.item_id
        })
        if (items_id !== "") {
          this.getItemsInfo(items_id, () => {
            this.doChangeClient(true)
          })
        }
      }

      this.bPopupAfterChangeClient = false

    },
    // 点击图片时，触发预览函数
    previewImage(index) {
      this.previewIdx = index; // 设置当前预览的索引
      this.previewShow = true; // 显示图片预览
    },

    // 监听预览变化
    previewOnChange(index) {
      this.previewIdx = index; // 更新当前显示的图片索引
    },
    // 打印预览方法
    async confirmPreview() {
      this.isInPrintView = true
      // 获取默认打印机
      const defaultPrinter = window.getDefaultPrinter()
      // 判断是否为云打印
      var printer_kind = defaultPrinter.kind
      if (!printer_kind) {
        if (defaultPrinter.type == "cloud")
          printer_kind = '24pin'
        else
          printer_kind = 'tiny'
      }
      this.useTmp = defaultPrinter.useTemplate
      //获取模板并展示
      if (this.useTmp) {
        if (this.templatesLoaded) {
          this.confirmTemplates(printer_kind)
        } else {
          this.loadPrintTemplates((data) => {
            this.confirmTemplates(printer_kind)
          })
        }
      }
      if (!this.useTmp) {
        this.$store.commit('companyNameToPrint', this.companyNameToPrint)
        this.print_noTemplate(this.printCount)
        console.log('Jade打印完成')
      } else {
        if (!this.tmplist || this.tmplist.length == 0) {
          Toast.fail("没有可用的打印模板")
        } else {
          var tmp
          try {
            tmp = this.tmplist[this.selectedTmpId].tmp
          }
          catch (e) {
            Toast.fail('打印错误' + e.message)
            return
          }
          // 先对是否保存单据进行判断 不然会先黑屏加载图片 保存单据的提示在下面的图层看上去很奇怪
          if (!this.sheet.sheet_id) {
            Toast.fail("请先保存单据");
            return;
          }
          console.log('Jade打印模板:', tmp)
          this.btnPreview_ByTemplate(tmp)
          this.previewShow = true;
        }
      }
      setTimeout(() => {
        this.sheetHasChange = false
      }, 100)
    },
    previewPrintShareSelected() {
        this.wechatSharePrintPreview(this.previewImageUrls[0], 0);

    },
    wechatSharePrintPreview(imgUrl, index) {
      console.log("准备分享图片", index + 1, "URL:", imgUrl);

      let title = `分享图片 ${index + 1}`;
      let companyName = this.$store.state.operInfo?.setting?.companyName || this.$store.state.account.companyName;

      console.log("分享标题:", title);
      console.log("公司名称:", companyName);
      window.Wechat.share({
        message: {
          title: title,
          description: `来自【${companyName}】`,
          thumb: globalVars.wechatConf.imgUrl,
          media: {
            type: window.Wechat.Type.IMAGE,
            image: imgUrl
          }
        },
        scene: window.Wechat.Scene.SESSION
      }, function () {
        console.log("分享成功");
        Toast.success("分享成功");
      }, function (reason) {
        console.log("分享失败", reason);
        Toast.error("分享失败", reason);
      });
    },
    // 打印预览方法 外层 无视打印机的选择 先小于110的 如果没有 再考虑大于110的
    async confirmPreviewOutside() {
      if (!this.sheet.sheet_id) {
          Toast.fail("请先保存单据");
          return;
      }

      // 获取默认打印机
      const defaultPrinter = window.getDefaultPrinter()
      // 判断是否为云打印
      var printer_kind = defaultPrinter.kind
      if (!printer_kind) {
        if (defaultPrinter.type == "cloud")
          printer_kind = '24pin'
        else
          printer_kind = 'tiny'
      }
      this.useTmp = defaultPrinter.useTemplate

      if (this.templatesLoaded) {
          this.confirmTemplatesIgnorePrintKind()
        } else {
          this.loadPrintTemplates((data) => {
            if (!this.tmplist || this.tmplist.length == 0) {
              Toast.fail("没有可用的打印模板")
              return
            }
            this.confirmTemplatesIgnorePrintKind()
            
          })
        }
     
      // confirmTemplates()改变了this.tmplist的值
       
        
        
 
    },
    btnPreview_ByTemplate(tmp){//模板预览
      // 为了避免重复点击打印预览之后 previewImageUrls数组被重复添加值，初始化一个空数组
      this.previewImageUrls = []
      // 检查单据状态
      if (!this.sheet.sheet_id) {
        Toast.fail("请先保存单据");
        return;
      }
      const that = this
      const printErrorHandler = (error) => {
        handlePrintComplete()
        Toast.fail(error)
      }
      const handlePrintComplete = () => {
        this.loadingMsg = ''
        this.isPrinting = false
      }
      console.log('Print Template:', tmp)


      const defaultPrinter = window.getDefaultPrinter()
      this.loadingMsg = '正在打印' // 显示打印信息

      // 为打印机类型赋默认值(如果未定义)
      var printer_kind = defaultPrinter.kind
      if (!printer_kind) {
        if (defaultPrinter.type == "cloud")
          printer_kind = '24pin'
        else
          printer_kind = 'tiny'
      }
      // 组装单据校验的请求参数
      this.isPrinting = true
      var sTmp = tmp.template_content
      tmp = JSON.parse(tmp.template_content);
      var printTemplate = []
      const isCloud = defaultPrinter.type == 'cloud'
      if (sTmp.indexOf('"prepay_balance"') !== -1) printTemplate.push({ name: "prepay_balance" })
      if (sTmp.indexOf('"arrears_balance"') !== -1) printTemplate.push({ name: "arrears_balance" })
      if (sTmp.indexOf('"print_count"') !== -1) printTemplate.push({ name: "print_count" })
      if (sTmp.indexOf('"name":"give_qty_unit"') >= 0) printTemplate.push({ name: "give_qty_unit" })

      var smallUnitBarcode = false
      if (this.printBarcodeStyleForSale?.length) {
        if (this.printBarcodeStyleForSale.indexOf('smallUnit') !== -1) {
          smallUnitBarcode = true
        }
      }
      console.warn('smallUnitBarcode:', smallUnitBarcode)
      var params = {
        sheetType: this.sheet.sheetType,
        sheet_type: this.sheet.sheet_type,
        sheet_id: this.sheet.sheet_id,
        smallUnitBarcode: smallUnitBarcode,
        printTemplate: JSON.stringify(printTemplate),
        copies: "1"
      }
      // 使用POST方法避免printTemplate参数过长导致的问题
      AppGetSheetToPrint_Post(params).then(data => {
        if (data.result != 'OK') {
          printErrorHandler(data.msg)
        }
        else {
          var sheet = data.sheet

          if (isCloud) { //云打印
            var params = {
                operKey: this.$store.state.operKey,
                tmp: tmp,
                sheet: sheet
              }
            AppSheetToImages(params).then(async res => {
                var escCommand = ''
                var jr = []
                jr = res//JSON.parse(res);
                console.log(jr);
                for (var i = 0; i < jr.length; i++) {
                  let imgb64 = `data:image/jpg;base64,${jr[i]}` // 重要! base64ToPrintBytesPromise须使用合法的src作为图片源并处理
                  this.previewImageUrls.push(imgb64)
                }
                handlePrintComplete()
              }).catch(err => { printErrorHandler(err); })
          } else {
            // 蓝牙打印
            let must24Pin = false // 必须使用针式打印机的图片打印指令
            let ignorePaperFeedCmd = false // 跳过换行、走纸、换页命令
            const pname = defaultPrinter.name
            // 2023.02.13 兼容ZICOX-CC4打印机
            if (pname.match('CC4_')) { // CC4_1656L
              ignorePaperFeedCmd = true
              must24Pin = true
            }
            var params = {
              operKey: this.$store.state.operKey,
              tmp: tmp,
              sheet: sheet,
              ignorePaperFeedCmd: ignorePaperFeedCmd
            }
            Printing.initPrinter()
            if (Printing.cmdMode === '_cpcl') {
              console.warn('正在使用CPCL打印机 | 蓝牙打印')
            }

            AppSheetToImages(params).then(async res => {
                var blocks = []
                var jr = []
                jr = res

                /** 处理图片转译的函数(须为async) */
                let imgFunc = Printing.base64ToPrintBytesPromise
                /** 图片指令是否应添加至content, 而非imageBytes */
                let addToContent = false
                if (Printing.cmdMode === '_cpcl') {
                  imgFunc = Printing.base64ToPrintBytesPromise_Cpcl
                  addToContent = true
                }

                for (var i = 0; i < jr.length; i++) {
                  let imgb64 = `data:image/jpg;base64,${jr[i]}` // 重要! base64ToPrintBytesPromise须使用合法的src作为图片源并处理
                  // this.previewImage = imgb64
                  this.previewImageUrls.push(imgb64)
                  var imageBytes = await imgFunc(imgb64)
                }

                handlePrintComplete()
            }).catch(err => { printErrorHandler(err); })
          }
        }
      }).catch(err => { printErrorHandler(err); })
    },
    async shareSelected(e) {
      switch (e.type) {
        case "shareQRCode":
          this.popupQRCodeShare = true
          if (!this.bGenerateQRCode) {
            setTimeout(() => {
              new QRcode(this.$refs.qrcode, {
                text: e.webpageUrl, // 扫二维码后获得的信息
                width: 180, // 图片宽90px，左右padding各4px，边框各1px， 100-8px-2px
                height: 180, // 图片高90px，上下padding各4px，边框各1px， 100-8px-2px
              })
              this.bGenerateQRCode = true
            }, 200)
          }

          break
        case "sharePicture":
          console.log(e.type)
          Toast.loading({
            message: "生成图片中..."
          })
          this.popupSubmitPannel = false

          setTimeout(async () => {
            if (e.type == 'sharePicture') {
              this.isSharingSheetImage=true

              const canvas = await html2canvas(this.$refs.pages, {
                backgroundColor: "#fff",
                useCORS: true,
                scale: 1.5,
                height: this.$refs.publicBox2.scrollHeight + this.$refs.publicBox3.scrollHeight + this.$refs.publicBox4.scrollHeight,
                windowHeight: this.$refs.publicBox2.scrollHeight + this.$refs.publicBox3.scrollHeight + this.$refs.publicBox4.scrollHeight
              })
              this.isSharingSheetImage=false

              const url = canvas.toDataURL('image/png') //转图片链接,为图片的base64形式
              window.Wechat.share({
                message: {
                  title: "X",
                  description: `来自【公司名称】`,
                  thumb: globalVars.wechatConf.imgUrl,
                  media: {
                    type: window.Wechat.Type.IMAGE,
                    image: url
                  }
                },
                scene: window.Wechat.Scene.SESSION
              }, function () {
                Toast.success("分享成功")
              }, function (reason) {
                Toast.error("分享失败" + reason)
              });
            }
          }, 400);
      }
    },
    async downloadPic() {
      // //el的全局loading，根据需求，可加可不加,在下载完成时或请求完成时用 loading.close()关闭
      //     const loading = this.$loading({
      //       lock: true,
      //       text: '文件下载中',
      //       spinner: 'el-icon-loading',
      //       background: 'rgba(0, 0, 0, 0.8)'
      //     });
      Toast.loading({
        message: "生成中..."
      })
      const canvas = await html2canvas(this.$refs.pages, {
        backgroundColor: "#fff",//画布背景色（如果未在DOM中指定）。设置null为透明
        useCORS: true,    //允许跨域
        scale: 1,   		//用于渲染的比例
        //publicBox3
        height: this.$refs.publicBox2.scrollHeight + this.$refs.publicBox3.scrollHeight + this.$refs.publicBox4.scrollHeight,
        windowHeight: this.$refs.publicBox2.scrollHeight + this.$refs.publicBox3.scrollHeight + this.$refs.publicBox4.scrollHeight
      })
      // 此时已经生成我们想要的截图,可以下载查看
      const url = canvas.toDataURL('image/png') //转图片链接,为图片的base64形式
      const image = new Image()
      image.src = url
      image.onload = () => {
        window.canvas2ImagePlugin.saveImageDataToLibrary(
          (msg) => {
            this.$toast("保存成功！")
            // eslint-disable-next-line handle-callback-err
          }, (err) => {
            console.log(err)
            this.$toast("保存失败！")
          },
          canvas
        )
      }
    },
    onBackToThisPage(fromPage, params) {
      console.log('onBackToThisPage is called. fromPage: ' + fromPage)
      if (fromPage == "SelectItems") {
        console.log('params:', params)
        if (params.salePromotionCombines) {
          this.salePromotionCombines = params.salePromotionCombines
        }
        if (params.salePromotionSecKills) {
          this.salePromotionSecKills = params.salePromotionSecKills
        }
        if (params.salePromotionFullDiscs) {
          this.salePromotionFullDiscs = params.salePromotionFullDiscs
        }
        if (params.salePromotionFullGifts) {
          this.salePromotionFullGifts = params.salePromotionFullGifts
        }
        if (params.salePromotionCashPrizes) {
          this.salePromotionCashPrizes = params.salePromotionCashPrizes
        }
      }
    },
    calBillPaid() {
      this.isBillPaid = (this.sheet.payway1_channel && this.sheet.payway1_status)
        || (this.sheet.payway2_channel && this.sheet.payway2_status)
        || (this.sheet.payway3_channel && this.sheet.payway3_status)
    },
    showPrinterSelectionPage() {
      this.PopupPrintersSelect = true
    },
    hidePrinterSelectionPage() {
      this.PopupPrintersSelect = false
    },
    confirmPrinterSelectionChange(selectedPrinter) {
      this.useTmp = selectedPrinter.useTemplate
      this.printers.forEach((prt) => {
        if (prt.id === selectedPrinter.id) {
          prt.isDefault = true
        } else {
          prt.isDefault = false
        }
      })
      window.setCompanyStoreValue('c_printers', this.printers)
      // Toast.success('修改成功!')
      this.defaultPrinter = selectedPrinter
      for (let _i = 0; _i < this.printers.length; _i++) {
        const _printer = this.printers[_i]
        if (_printer.isDefault) {
          this.defaultPrinterIndex = _i;
          break;
        }
      }

      this.PopupPrintersSelect = false

      const printer_kind = selectedPrinter.kind
      if (this.useTmp) {
        if (this.templatesLoaded) {
          this.confirmTemplates(printer_kind)
        } else {
          this.loadPrintTemplates((data) => {
            this.confirmTemplates(printer_kind)
          })
        }
      }
    },
    /** 将模板加载到this.tmplist中,并执行验证 */
    confirmTemplates(printer_kind) {
      this.tmplist = printer_kind == 'tiny' ? this.printTemplatesTiny : this.printTemplates
      if (this.tmplist.length == 0) {
        var err = printer_kind == 'tiny' ? '没有可用的小票模板' : '没有可用的打印模板'
        Toast.fail(err); return
      }
    },
    // 无视打印机类型，选择模板（外层打印按钮用
    confirmTemplatesIgnorePrintKind() {
      // 优先选择宽度小于 110 的模板
      this.tmplist = this.printTemplates.filter(template => {
        try {
          // 解析template_content 获取宽度
          let tmp_tocheck = JSON.parse(template.tmp.template_content);
          return tmp_tocheck.width <= 110;  // 判断宽度是否小于或等于 110
        } catch (e) {
          console.error('解析模板内容时出错:', template, e);
          return false;
        }
      });

      // 如果没有小于 110 的模板，选择宽度大于等于 110 的模板
      if (this.tmplist.length === 0) {
        this.tmplist = this.printTemplates.filter(template => {
          try {
            let tmp_tocheck = JSON.parse(template.tmp.template_content);
            return tmp_tocheck.width >= 110;  // 判断宽度是否大于等于 110
          } catch (e) {
            console.error('解析模板内容时出错:', template, e);
            return false;  // 如果解析失败，则不包含该模板
          }
        });
      }
      if (this.tmplist.length == 0) {
        Toast.fail("没有可用的打印模板");
      }
      var tmp = this.tmplist[0].tmp 
      this.btnPreview_ByTemplate(tmp)
      this.previewShow = true;

    },



    loadPromotions() {
      if (!this.sheet.supcust_id && this.sheet.supcust_id !== 0) {
        console.log("客户未选择,促销活动加载暂缓..."); return
      } else if (!this.sheet.branch_id) {
        console.log("仓库未选择,促销活动加载暂缓..."); return
      } else if (this.sheet.sheetType !== 'X' && this.sheet.sheetType !== 'XD') {
        console.log("不是销售单/销售订单, 不执行促销活动加载..."); return
      } else if (this.sup_group === undefined && this.sheet.supcust_id !== 0) {
        console.log("客户详情未完全加载,促销活动加载暂缓..."); return
      }
      // 暂时没啥优化性能的必要，加这玩意还会增加bug，比如复制后单据不加载促销
      // else if (this.sheet.promotionLoaded) {
      //   console.log("已经在此单据加载过促销活动,不再重复加载..."); return
      // }
      // } else if (this.sheet.approve_time) {
      //   console.log("此单据已经审核,不再可以编辑, 不执行促销活动加载..."); return
      // }

      const params = {
        // operKey: this.$store.state.operKey,
        dept: this.$store.state.operInfo?.depart_id,

        supcust_id: this.sheet.supcust_id,

        group: this.sup_group,
        rank: this.sup_rank,
        regions: this.sup_regions

      }
      console.log('Start promotionLoad, param:', params)
      AppSaleGetPromotionList(params).then((res) => {
        if (res.result === 'OK') {
          const promotions = JSON.parse(res.data)
          console.log('促销活动加载成功:', promotions)
          this.salePromotionCombines = promotions.combos
          this.salePromotionSecKills = promotions.seckills
          this.salePromotionFullDiscs = promotions.fulldiscs
          this.salePromotionFullGifts = promotions.fullgifts
          this.salePromotionCashPrizes = promotions.cashprizes
          // this.$store.commit('salePromotionCombines', promotions.combos)
          // this.$store.commit('salePromotionSecKills', promotions.seckills)
          // this.$store.commit('salePromotionFullDiscs', promotions.fulldiscs)
          // this.$store.commit('salePromotionFullGifts', promotions.fullgifts)
          // this.$store.commit('salePromotionCashPrizes', promotions.cashprizes)

          // 2024.07.25
          // 增加一个能够根据item_id反查关联promotion的jsMap
          const promotionMap = {}
          const buildMap = (itemID, promotionID) => {
            if (promotionMap[itemID]) {
              if(!promotionMap[itemID].includes(promotionID)) {
                promotionMap[itemID].push(promotionID)
              }
            } else {
              promotionMap[itemID] = [promotionID]
            }
          }
          promotions.combos?.forEach(combo => {
            combo.items?.forEach(item => {
              const promotionID = item.id
              item.sources?.forEach(source => {
                const itemIDs = source.items_id?.split(',')
                itemIDs?.forEach(itemID => {
                  buildMap(itemID, promotionID)
                })
              })
              item.gifts?.forEach(gift => {
                const itemIDs = gift.items_id?.split(',')
                itemIDs?.forEach(itemID => {
                  buildMap(itemID, promotionID)
                })
              })
            })
          })
          this.sheet.promotionMap = promotionMap
          console.log('promotionMap:', promotionMap)
        } else {
          this.salePromotionCombines = []
          this.salePromotionSecKills = []
          this.salePromotionFullDiscs = []
          this.salePromotionFullGifts = []
          this.salePromotionCashPrizes = []
          console.warn('促销活动加载失败:', res)
        }

        if (this.sheet.promotion_fullgift_content) {
          try {
            var fg_obj = JSON.parse(this.sheet.promotion_fullgift_content)
            this.$store.commit('salePromotionFullGifts', fg_obj)
            console.log('[促销活动] 使用了已存储的促销活动满赠替换服务器内容:', fg_obj)
          } catch (e) {
            console.error('已存储的促销活动满赠不是可以被PARSE的合法字符串:', this.sheet.promotion_fullgift_content)
            console.error('Parse error detail:', e)
          }
        }
        this.sheet.promotionLoaded = true;
      }).catch((e) => {
        this.salePromotionCombines = []
        this.salePromotionSecKills = []
        this.salePromotionFullDiscs = []
        this.salePromotionFullGifts = []
        this.salePromotionCashPrizes = []
        console.error('促销活动加载错误:', e)
      })
    },
    paywayIsKS(num) {
      var ksSubID = window.getSettingValue('feeOutSubForKS')
      if (ksSubID && this.sheet[`payway${num}_id`] == ksSubID)
        return true
      return false
    },
    shouldPaywayDisabled(paywayIndex) {
      var isOrder = this.sheet[`payway${paywayIndex}_is_order`]
      if (isOrder && isOrder.toString().toLowerCase() == 'true') return true
      if (this.paywayIsKS(paywayIndex)) return true
      return false
    },
    async btnPrint_click(){
     if(this.sheet.sheetType === 'X' || this.sheet.sheetType === 'XD'){
        var value = getRightValue('delicacy.appSalePrintIsApprove.value')
        if(value == 'true'){
          console.log("单据类型",this.sheet.sheetType)
          //Toast.fail('打印即审核')
          if(!this.sheet.approve_time){
            this.checkJobBeforeSave(()=>{
              this.approveSheet(false,()=>{
                this.confirmPrint()
              })
            })
            return
          }
        }
     }

      if(this.sheet.sheet_id && !this.sheet.approve_time && this.sheetHasChange){
        this.doSave(()=>{
          this.confirmPrint()
        })
      }
      else {
        this.confirmPrint()
      }
    },
    // 最终打印方法
    async confirmPrint() {

      if (!this.useTmp) {
        this.$store.commit('companyNameToPrint', this.companyNameToPrint)
        this.print_noTemplate(this.printCount)
        console.log('打印完成')
      } else {
        if (!this.tmplist || this.tmplist.length == 0) {
          Toast.fail("没有可用的打印模板")
        } else {
          var tmp
          try {
            tmp = this.tmplist[this.selectedTmpId].tmp
          }
          catch (e) {
            Toast.fail('打印错误' + e.message)
            return
          }
          console.log('打印模板:', tmp)
          this.btnPrint_ByTemplate(tmp, this.printCount)
        }

      }
      setTimeout(() => {
        this.sheetHasChange = false
      }, 100)
    },
    // base64 to uint8array (byC)
    base64ToUint8Array(base64) {
      var raw = atob(base64);
      var rawLength = raw.length;
      var array = new Uint8Array(new ArrayBuffer(rawLength));
      for (var i = 0; i < rawLength; i++) {
        array[i] = raw.charCodeAt(i);
      }
      return array;
    },
    loadPrintTemplates(successCb) {
      this.tmplist = []
      this.printTemplates = []
      this.printTemplatesTiny = []
      debugger
      var params = {
        sheetType: this.sheet.sheetType,
        clientID: this.sheet.supcust_id
      }
      AppGetTemplate(params).then(data => {
        var templateList = data.templateList
        for (let i = 0; i < templateList.length; i++) {
          const template = templateList[i]
          var inserttmp = {
            name: template.template_name,
            i: i,
            tmp: template
          }
          this.printTemplates.push(inserttmp)
          try {
            let tmp_tocheck = JSON.parse(template.template_content)
            if (tmp_tocheck.width <= 110) {
              this.printTemplatesTiny.push(inserttmp)
            }
          } catch {
            console.error('在解析模板的宽度时发生错误,inserttmp:', inserttmp)
          }
        }
        this.templatesLoaded = true
        if (successCb) {
          successCb(data)
        }
      })
    },
    // 跳转打印详情 判断是否使用模板与打印+渲染模板radio
    btnGotoPrintView_click() {
      this.isInPrintView = true
      // 获取默认打印机
      const defaultPrinter = window.getDefaultPrinter()
      // 判断是否为云打印
      var printer_kind = defaultPrinter.kind
      if (!printer_kind) {
        if (defaultPrinter.type == "cloud")
          printer_kind = '24pin'
        else
          printer_kind = 'tiny'
      }
      this.useTmp = defaultPrinter.useTemplate
      //获取模板并展示
      if (this.useTmp) {
        if (this.templatesLoaded) {
          this.confirmTemplates(printer_kind)
        } else {
          this.loadPrintTemplates((data) => {
            this.confirmTemplates(printer_kind)
          })
        }
      }
      this.isPrinting = false
    },

    title_click(e) {
      if (!(e.target.className && e.target.className.indexOf && e.target.className.indexOf('van-nav-bar__title') >= 0)) return
      if (this.lastClickTime) {
        if (new Date() - this.lastClickTime < 1000) {
          // this.inPrivateMode = !this.inPrivateMode
          this.$store.commit('inPrivateMode', !this.inPrivateMode)
          if (this.inPrivateMode) {
            this.showPrivateModePrompt()
          } else {
            Toast('进入正常模式')
          }
        }
      }
      this.lastClickTime = new Date()

    },
    GetStockStr(item) {
      let params = {
        quantity: item.stock_qty,
        unit_factor: "1",
        b_unit_factor: item.b_unit_factor,
        m_unit_factor: item.m_unit_factor,
        s_unit_factor: item.s_unit_factor,
        b_unit_no: item.b_unit_no,
        m_unit_no: item.m_unit_no,
        s_unit_no: item.s_unit_no
      }
      return globalVars.getUnitQtyFromSheetRow(params) == '' ? '0' : globalVars.getUnitQtyFromSheetRow(params)
    },
    IsStockEnough(item) {
      console.log(item)
      var stock = parseFloat(item.stock_qty)
      var qty = parseFloat(item.quantity) * parseFloat(item.unit_factor)
      return stock >= qty
    },
    pageSayCode(result) {
      this.searchStr = result;
      this.queryScan()
    },
    appendixtip_click() {

    },
    /** 通过本单据的SheetType获取查看单据中的Tab页关键字 */
    // getSheetTypeTab() {
    //   return this.sheet.sheetType == 'X' ? 'saleSheet' :
    //     this.sheet.sheetType == 'T' ? 'returnSheet' :
    //       this.sheet.sheetType == 'XD' ? 'saleOrderSheet' :
    //         this.sheet.sheetType == 'TD' ? 'returnOrderSheet' :
    //           this.sheet.sheetType == 'CG' ? 'buySheet' :
    //             this.sheet.sheetType == 'CT' ? 'buySheet' :
    //               ''
    // },
    //切换新查单页面
    getSheetTypeTab() {
      return this.sheet.sheetType == 'X' ? 'sheetSale' :
        this.sheet.sheetType == 'T' ? 'sheetReturn' :
          this.sheet.sheetType == 'XD' ? 'sheetSaleOrder' :
            this.sheet.sheetType == 'TD' ? 'sheetReturnOrder' :
              this.sheet.sheetType == 'CG' ? 'sheetBuy' :
                this.sheet.sheetType == 'CT' ? 'sheetReturnBuy' :
                  this.sheet.sheetType == 'BQ' ? 'sheetLabelPrint' :
                    ''
    },
    menuGo({ name, url }) {
      console.warn('Route url:', url)
      this.$router.push(url)
    },
    //计算大中小单位数量
    calculateSumQuantityUnitConv() {
      var _esp = 0.0001
      var bQuantity = 0, mQuantity = 0, sQuantity = 0;
      var bQuantityT = 0, mQuantityT = 0, sQuantityT = 0;
      this.sheet.sheetRows.forEach(sheetRow => {
        var bf = parseFloat(sheetRow.b_unit_factor);
        var mf = parseFloat(sheetRow.m_unit_factor);
        var sf = 1.0;
        if (isNaN(bf)) bf = 0;
        if (isNaN(mf)) mf = 0;
        if (Number(sheetRow.quantity) > 0) {
          if (Math.abs(bf - sheetRow.unit_factor) < _esp) {
            bQuantity += parseFloat(sheetRow.quantity);
          } else if (Math.abs(mf - sheetRow.unit_factor) < _esp) {
            mQuantity += parseFloat(sheetRow.quantity);
          } else {
            sQuantity += parseFloat(sheetRow.quantity);
          }
        } else {
          if (Math.abs(bf - sheetRow.unit_factor) < _esp) {
            bQuantityT += parseFloat(sheetRow.quantity);
          } else if (Math.abs(mf - sheetRow.unit_factor) < _esp) {
            mQuantityT += parseFloat(sheetRow.quantity);
          } else {
            sQuantityT += parseFloat(sheetRow.quantity);
          }
        }
      });
      var msgShow = {
        'X': '销: ',
        'XD': '销: ',
        'T': '退: ',
        'TD': '退: ',
        'CG': '采: ',
        'CT': '采退: ',
        'DH': '销: '
      }
      var sum = "";
      var sumT = "";

      if (bQuantity != 0) {
        sum += toMoney(bQuantity) + "大";
      }
      if (mQuantity != 0) {
        sum += toMoney(mQuantity) + "中";
      }
      if (sQuantity != 0) {
        sum += toMoney(sQuantity) + "小";
      }

      if (bQuantityT != 0) {
        sumT += toMoney(bQuantityT) + "大";
      }
      if (mQuantityT != 0) {
        sumT += toMoney(mQuantityT) + "中";
      }
      if (sQuantityT != 0) {
        sumT += toMoney(sQuantityT) + "小";
      }
      if (sum !== '') {
        sum = (msgShow[this.sheet.sheetType]||'') + sum
      }
      if (sumT !== '') {
        sumT = ' 退:' + sumT
      }
      this.sumQuantityUnitConv = sum + sumT;
    },
    //选择送货员
    selectSenders(index) {
      this.$refs.sendersCheckboxes[index].toggle();
      setTimeout(() => {
        var ids = [];
        var names = [];
        this.selectedSenders.forEach(sender => {
          ids.push(sender.senders_id);
          names.push(sender.senders_name);
        });
        this.sheet.senders_id = ids.join(',');
        this.sheet.senders_name = names.join(',');
      }, 200);
    },
    // 初始化
    loadSheet(sheetType, sheetID, fromOrderSheet = false, fromOtherSheet = false) {
      this.isChangingSheetByCode = true
      this.sheet.sheetType = sheetType
      this.onloadPayways = []

      let params = {
        sheetID: sheetID,
        sheetType: sheetType,
      };
      this.copyFlag = false
      var that = this;

      var loadFunc;
      if (sheetType == "X" || sheetType == "T")//如果来自转单单据则加载销售订单
        loadFunc = fromOrderSheet ? AppSheetSaleOrderLoad : AppSheetSaleLoad;
      else if (sheetType == "XD" || sheetType == "TD")
        loadFunc = AppSheetSaleOrderLoad;
      else if (sheetType == "CG" || sheetType == "CT")
        loadFunc = AppSheetBuyLoad;
      else if(sheetType=="CD")
        loadFunc = AppSheetBuyOrderLoad;
      else if (sheetType == "DH")
        loadFunc = AppSheetOrderItemLoad;
      // todo AppSheetLabelPrintLoad

      loadFunc(params).then(async (res) => {
        //console.warn('LoadSheet res:', res)
        if (res.result !== "OK") {
          // Toast.fail("请求失败");
          return;
        }
        if (!res.sheet) {
          Toast.fail("无表单数据");
          return;
        }
        // 数据
        let sheet = res.sheet;
        if(sheet.prepay_sheet_info) this.prepay_sheet_infos = JSON.parse(sheet.prepay_sheet_info)

        if (res.sheet.approve_time && res.sheet.supcust_id == '0') {
          res.sheet.sup_name = '散客'
        }

        if (sheet.tempHappenTime && !sheet.approve_time) {
          sheet.happen_time = ''
        }

        //  处理丢失代码，上传
        this.$store.commit("attrOptions", res.attrOptions)
        //清空单据状态
        sheet.displayGiveProofs = JSON.parse(sheet.display_give_proofs ? sheet.display_give_proofs : '[]')
        if (fromOrderSheet) {
          sheet.order_sheet_id = sheet.sheet_id;
          sheet.sheet_id = "";
          sheet.order_sheet_no = sheet.sheet_no;
          sheet.sheet_no = "";
          sheet.sheetType = sheetType;
          if (sheet.move_stock && sheet.move_stock.toLowerCase() == 'true') {
            if (sheet.van_id) {
              sheet.branch_id = sheet.van_id
              sheet.branch_name = sheet.van_name
              sheet.sheetRows.forEach(e => {
                e.branch_position = "0"
                e.branch_position_name = ""
              })
            }
          }
          sheet.make_time = "";
          sheet.marker_id = "";
          sheet.marker_name = "";
          sheet.approve_time = "";
          sheet.approver_id = "";
          sheet.approver_name = "";
          sheet.happen_time = "";
          await this.handleFromOrderSheet(sheet)
        }
        var appendixOrigin = JSON.parse(sheet.appendix_photos ? sheet.appendix_photos : "[]")
        if(appendixOrigin.photos) appendixOrigin=appendixOrigin.photos
        var obsUrls = appendixOrigin.filter(imgurl => !ImageUtil.isBase64(imgurl)).map(imgurl => {
          let path = imgurl.includes("uploads") ? imgurl : "/uploads" + imgurl;
          return path.startsWith("/") ? globalVars.obs_server_uri + path : globalVars.obs_server_uri + "/" + path;
        })
        // const originBase64Images = appendixOrigin.filter(imgurl => ImageUtil.isBase64(imgurl))
        sheet.appendixPhotos = []
        sheet.appendixPhotos = sheet.appendixPhotos.concat(obsUrls)
        console.log('obsUrls', obsUrls)

        if (!sheetID) {
          sheet.visit_id = this.$route.query.visit_id ? this.$route.query.visit_id : "";
          if (this.canAppSelectGetter) {
            sheet.getter_id = this.$store.state.operInfo.oper_id;
            sheet.getter_name = this.$store.state.operInfo.oper_name;
          }
        }
        console.log("this.$route.query:"+this.$route.query.supcust_id)
        console.log("this.$route.query:"+this.$route.query.sup_name)
        console.log("this.$route.query:"+this.$route.query.sheetToOtherParams)

        //
       // sheet.supcust_id = this.$route.query.supcust_id ? this.$route.query.supcust_id : sheet.supcust_id;
       // sheet.sup_name = this.$route.query.sup_name ? this.$route.query.sup_name : sheet.sup_name;
        if (!sheetID) {
          if(sheet.sheetType=='X' || sheet.sheetType=='XD' ||sheet.sheetType=='T' || sheet.sheetType=='TD'|| sheet.sheetType=='DH'){
            var retailWholesaleFlag=''
            var visitRecord=window.getVisitRecord()
            if(visitRecord){
              sheet.supcust_id = visitRecord.shop_id
              sheet.sup_name = visitRecord.shop_name
              
              // 检查是否有不拜访开单/开订单的权限
              var allowNoVisitSale = hasRight("delicacy.allowNoVisitSale.value")
              var allowNoVisitOrder = hasRight("delicacy.allowNoVisitOrder.value")
              
              if(sheet.sheetType=='XD' || sheet.sheetType=='TD'){
                // 销售订单：如果有不拜访开订单权限，则允许切换客户
                if(!allowNoVisitOrder){
                  this.canNotChangeClientReason = '已拜访门店无法切换客户'
                }
              } else if(sheet.sheetType=='X' || sheet.sheetType=='T'){
                // 销售单：如果有不拜访开单权限，则允许切换客户
                if(!allowNoVisitSale){
                  this.canNotChangeClientReason = '已拜访门店无法切换客户'
                }
              } else {
                // 其他类型（如DH）：保持原有逻辑
                this.canNotChangeClientReason = '已拜访门店无法切换客户'
              }
            }
            else
            {
              const {canNewWholesaleSheet,canNewRetailSheet, msg} = window.canNewSheet(sheet.sheetType,retailWholesaleFlag)
              if(!canNewWholesaleSheet && canNewRetailSheet){
                  sheet.supcust_id =0
                  sheet.sup_name = '散客'
                  this.canNotChangeClientReason = '必须先拜访才能选客户开单'
              }
              else if(this.$route.query.supcust_id){
                  sheet.supcust_id = this.$route.query.supcust_id
                  sheet.sup_name = this.$route.query.sup_name

              }
            }
          }
        }

        sheet.acct_type = this.$route.query.acct_type ? this.$route.query.acct_type : sheet.acct_type;
        sheet.sheetType = sheetType;
        sheet.sheetRows.forEach(item => {
          if((sheetType === 'X' || sheetType === 'XD') )
          {
             if (Number(item.quantity) < 0 && !item.trade_type) {
              item.trade_type = 'T'
             }
          }

          sheetImages.handleImage(item)
        })
        /**
         * 订单转车后，送货员修改
         */
        if (sheet?.assigned_senders_id !== undefined && sheet.assigned_senders_id !== "") {
          sheet.senders_id = sheet.assigned_senders_id
          sheet.senders_name = sheet.assigned_senders_name
        }

        if (fromOrderSheet && !sheet.senders_id) {//送货签收时，如果没有送货员，就以操作人作为送货员
          sheet.senders_id = this.$store.state.operInfo.oper_id
          sheet.senders_name = this.$store.state.operInfo.oper_name
        }

        // 拿到返回值
        that.isLoadingSheet = true
        that.sheet = sheet;
        setTimeout(() => {
          that.isLoadingSheet = false
        }, 300)

        if (that.order_source)
          that.sheet.order_source = that.order_source
        // 备注方式
        that.onloadBrief = res.brief;
        // 支付方式
        that.onloadPayways = res.payways;
        console.log('that.onloadPayways:', that.onloadPayways)
        that.paywaysForSelect = []
        var payWithFeeOutAcct = window.getRightValue('delicacy.payWithFeeOutAcct.value') || ''
        payWithFeeOutAcct = payWithFeeOutAcct.toLowerCase() == 'true'
        that.onloadPayways.forEach(sub => {
          //console.log(that.paywaysForSelect)
          sub.subInfoToShow = sub.sub_name
          if (sub.payway_type == 'QT' || sub.payway_type == 'QTSR')
            that.paywaysForSelect.push(sub)
          else if (',X,T,XD,TD,'.indexOf(',' + this.sheet.sheetType + ',') >= 0 && payWithFeeOutAcct && sub.payway_type == 'ZC' && sub.is_ks != 'true') {
            that.paywaysForSelect.push(sub)
          }
          else if (',CG,CT,CD,'.indexOf(',' + this.sheet.sheetType + ',') >= 0 && sub.payway_type == 'SR' && sub.is_ks != 'true') {
            that.paywaysForSelect.push(sub)
          }
        })
        console.log("paywaysForSelect1",that.paywaysForSelect)
        // 处理已使用的促销活动
        var sheetAttribute = sheet.sheet_attribute
        if (sheetAttribute) {
          try {
            const attributes = JSON.parse(sheetAttribute)
            if (attributes?.promotion_fulldisc_used) {
              that.sheet.promotion_fulldisc_used = attributes.promotion_fulldisc_used === true || attributes.promotion_fulldisc_used === 'true'
            }
            if (attributes?.promotion_fullgift_content) {
              that.sheet.promotion_fullgift_content = attributes.promotion_fullgift_content
            }
          }
          catch (e) { }
        }
        console.warn('sheet.promotion_fulldisc_used:', that.sheet.promotion_fulldisc_used)
        console.warn('sheet.promotion_fullgift_content:', that.sheet.promotion_fullgift_content)

        // 处理已使用的促销活动
        var sheetAttribute = sheet.sheet_attribute
        if (sheetAttribute) {
          try {
            const attributes = JSON.parse(sheetAttribute)
            if (attributes?.promotion_fulldisc_used) {
              that.sheet.promotion_fulldisc_used = attributes.promotion_fulldisc_used === true || attributes.promotion_fulldisc_used === 'true'
            }
            if (attributes?.promotion_fullgift_content) {
              that.sheet.promotion_fullgift_content = attributes.promotion_fullgift_content
            }
          }
          catch (e) { }
        }

        console.warn('sheet.promotion_fulldisc_used:', that.sheet.promotion_fulldisc_used)
        console.warn('sheet.promotion_fullgift_content:', that.sheet.promotion_fullgift_content)

        // 客损单独赋值

        that.ksPayWayObj = null
        that.onloadPayways.some((sub) => {
          if (sub.payway_type == 'ZC' && sub.is_ks == 'true') {
            that.ksPayWayObj = sub; return
          }
        })

        // 送货员
        that.senderList = res.senders
        if (!sheetID) {
          if (JSON.stringify(that.$store.state.salesSender) !== '{}' && that.$store.state.salesSender !== undefined && that.$store.state.salesSender.senders_id !== '') { // onConfirmSender, cancelSenderPicker这里两个方法好像不会调用了
            //切换账户，缓存判断
            var result = null
            if (that.senderList) {
              result = that.senderList.some(item => {
                if (item.senders_id === that.$store.state.salesSender.senders_id) {
                  return true
                }
              })
            }
            if (result) {
              that.sheet.senders_id = that.$store.state.salesSender.senders_id;
              that.sheet.senders_name = that.$store.state.salesSender.senders_name;
            } else {
              that.sheet.senders_id = "";
              that.sheet.senders_name = "";
            }
          }
          if(that.sheet.senders_id == "" && (that.canAppSelectSender || that.canAppSelectSender === "true")) {
              if(this.is_sender && this.appOperIsDefSender && (that.sheet.sheetType === "X" || that.sheet.sheetType === "T")){
                this.sheet.senders_name =  this.$store.state.operInfo.oper_name
                this.sheet.senders_id = this.$store.state.operInfo.oper_id
              }
            }
        }

        if (!that.sheet.payway1_id) {
          var payway
          for (var i = 0; i < that.paywaysForSelect.length; i++) {
            if (that.paywaysForSelect[i].payway_type != 'YS') {
              payway = that.paywaysForSelect[i]
              break
            }
          }
          if(payway !== undefined) {
            that.sheet.payway1_id = payway.sub_id;
            that.sheet.payway1_name = payway.sub_name;
            this.qrcode1 = payway.qrcode_uri;
          } else {
            Toast.fail("没有可用支付方式！")
          }

        }

        that.showPayWay1 = true;

        if (that.sheet.payway2_amount != 0) {
          that.showPayWay2 = true;
        }

        if (that.sheet.payway3_amount != 0) {
          that.showPayWay3 = true;
        }

        if (sheetType !== "DH") {
          that.loadBranches();
        } else {
          that.OrderAccountList = res.order_account;
        }
        if (!sheetID) {
          if (sheetType !== "DH") {
            var branchValid = false;
            var sheetBranches = window.getCompanyStoreValue("c_sheetBranches", {})
            var cachedBranch = sheetBranches[sheetType] || {}
            //var cachedBranch = that.$store.state.sheetBranches[sheetType]
            //if (!cachedBranch) cachedBranch = that.$store.state.salesBranch;

            var branchValid = window.hasBranchSheetRight(
              cachedBranch.branch_id,
              that.sheet.sheetType
            );
            if (branchValid) {
              that.sheet.branch_id = cachedBranch.branch_id;
              that.sheet.branch_name =
                cachedBranch.branch_name || "";
            }
          }

          that.showSheetsFromCache();
        } else {
          that.unsubmitedSheets = [];
          that.showUnsubmitedSheets = false;
        }

        if (!that.sheet.approve_time)//避免打开历史单据查看的时候，把当前单据设置为查看的单据，这样保存缓存就会把这个单据保存了
          window.g_SaleSheet = that;


        // 如果明细行商品开启产期但没有填写，自动转化为无产期，否则保存的单子明细无产期会消失
        if (this.sheet.sheetType === 'X' || this.sheet.sheetType === 'T') {
          //debugger
          this.sheet.sheetRows.forEach((r) => {
          if (r.batch_level && !r.produce_date) {
            r.produce_date = '无产期'
          }
        })
        }


        this.loadPromotions()
        //if (sheetID) {
        this.handleClientChange(true)
        // }
        if (fromOtherSheet) {
          this.showUnsubmitedSheets = false
          this.handleFromOtherSheet()

        }
        //
        if (!this.showUnsubmitedSheets) {
          this.showPrivateModePrompt()
        }

        // 处理检查银行系统订单的支付状态
        // * 处理银盛支付的状态显示
        // 直接判断sheet.payb_status即可
        // * 处理云涟的状态显示和退款信息配置
        that.paywaysForSelect.forEach(payway => {
          if (payway.pay_channel_id) {
            if (that.sheet.payway1_id && that.sheet.payway1_id === payway.sub_id)
              that.sheet.payway1_channel = payway.pay_channel_id
            if (that.sheet.payway2_id && that.sheet.payway2_id === payway.sub_id)
              that.sheet.payway2_channel = payway.pay_channel_id
            if (that.sheet.payway3_id && that.sheet.payway3_id === payway.sub_id)
              that.sheet.payway3_channel = payway.pay_channel_id
          }
        })
        that.loadPaywayStatus()

        // * 2024.01.03 平波粮油优化:预先加载打印模板
        // 在客户网络不佳时,保存后立即打印可能会打印模板还未加载完成
        // 因此,如果客户的默认打印机使用模板打印,那么在加载时就顺带加载模板
        console.warn('on loadSheet, this.defaultPrinter:\n', JSON.stringify(that.defaultPrinter))
        if (that.defaultPrinter?.useTemplate) {
          that.loadPrintTemplates(data => {
            const printer_kind = that.defaultPrinter.kind
            that.tmplist = printer_kind == 'tiny' ? that.printTemplatesTiny : that.printTemplates
          })
        }

        if (sheetType === "X" && that.isSaleNeedMarkIOU && !that.sheet.iou_get_time && this.sheet.approve_time) {

          that.noArrearsBills = true
        }

        setTimeout(() => {
          this.isChangingSheetByCode = false
        }, 100)

       window.checkToUpdateApp(sheet.server_uri)


      })


    },
    loadPaywayStatus() {
      let channels = []
      // todo: 暂时一单只允许一个银行支付方式,暂时不需要考虑多支付方式时的效率问题
      if (this.sheet.payway1_channel) channels.push(1)
      if (this.sheet.payway2_channel) channels.push(2)
      if (this.sheet.payway3_channel) channels.push(3)
      if (channels.length) {
        channels.forEach(index => {
          const params = {
            operKey: this.$store.state.operKey,
            sheetId: this.sheet.sheet_id,
            sheetType: this.sheet.sheetType,
            payChannel: this.sheet[`payway${index}_channel`]
          }
          GetBillStatus(params).then(res => {
            console.log('[loadPaywayStatus] Res:', res)
            if (res.result === 'OK') {
              this.sheet[`payway${index}_status`] = res.msg
              this.calBillPaid()
              this.$forceUpdate()
              //this.isBillPaid = true
            }
          }).catch(err => {
            console.error('[loadPaywayStatus] Err:', err)
          })
        })
      }
    },
    syncTicketAccess_click() {
      const sheet = this.sheet
      console.log({ sheet })
      const ticketAccess = new TicketAccess()
      ticketAccess.funUploadSheetToPiaoZhengTong(sheet)
    },
    //展示付款码
    btnShowSubCode_click(qrcodeFlag) {
      this.imageSrc1 = globalVars.obs_server_uri + "/" + this[`${qrcodeFlag}`]
      this.popupSubImage1 = true
      // if (qrcodeFlag === 'qrcode1') {
      //   this.imageSrc1 = globalVars.obs_server_uri + "/" + this.qrcode1
      //   this.popupSubImage1 = true;
      // } else {
      //   this.imageSrc2 = globalVars.obs_server_uri + "/" + this.qrcode2
      //   this.popupSubImage2 = true;
      // }
    },
    //点击关闭
    returnVanClose() {
      this.showPrePaySheet = false;
    },
    //模拟用户输入
    imitateUserInput() {
      this.$nextTick(() => {
        let event = new Event('input', { bubbles: true });
        this.$refs.paywayInput.dispatchEvent(event);
      });
    },
    // 确定按钮的逻辑
    btnPrepayDetailAlloc_click() {
      var pay_amount = 0
      console.log('输出：', this.prepayBalanceDetail)
      var subId = ""
      var preSheets = []
      this.prepayBalanceDetail.forEach(item => {
          if (item.amount != '') {
              pay_amount += parseInt(item.amount)
              if (!subId) subId = item.sub_id
          }
          preSheets.push(item)
      })
      if (!this.prepay_sheet_infos[this.current_subId]) {
          this.prepay_sheet_infos[this.current_subId] = [];
      }
      this.prepay_sheet_infos[this.current_subId] = preSheets;

      if (this.payamounts.length > 0) {
          if (this.payamounts[this.payamounts.length - 1] === 'payway1_id') {
              this.sheet.payway1_amount = pay_amount
              this.imitateUserInput()
          }
          if (this.payamounts[this.payamounts.length - 1] === 'payway2_id') {
              this.sheet.payway2_amount = pay_amount
              this.imitateUserInput()
          }
          if (this.payamounts[this.payamounts.length - 1] === 'payway3_id') {
              this.sheet.payway3_amount = pay_amount
              this.imitateUserInput()
          }

      }

      this.showPrePaySheet = false
    },
    // 跳转预收款单
    goPrepaySheet(sheetId,sheetType) {
      let query = {
        supcustID: this.sheet.supcust_id,
        sheetID: sheetId,
        sheetType:sheetType
      }
      if(sheetType=="YS")
        this.$router.push({ path: "/PrepaySheet", query: query })
      else if(sheetType=="T")
        this.$router.push({ path: "/SaleSheet", query: query })
    },
    //输入金额自动分配
    inputPayWayAmount(payway_fld, sub_id, isPrepay) {
      var inputAmount = 0


      if (payway_fld === 'payway1_id') {
        inputAmount = parseFloat(this.sheet.payway1_amount)
      } else if (payway_fld === 'payway2_id') {
        inputAmount = parseFloat(this.sheet.payway2_amount)
      } else if (payway_fld === 'payway3_id'){
        inputAmount = parseFloat(this.sheet.payway3_amount)
      }

      var allocAmount = 0
      if(isPrepay) {
        this.prepay_sheet_infos[sub_id].forEach(item => {
          if (item.amount != '') {
            allocAmount += parseFloat(item.amount)
          }
        })

        if(inputAmount !== allocAmount){
          this.prepay_sheet_infos[sub_id].forEach(item => {
            if (inputAmount >= 0) {
              if (inputAmount > parseFloat(item.balance)) {
                inputAmount -= item.balance
                item.amount = item.balance
              } else {
                item.amount = inputAmount
                inputAmount = 0
              }
            }
          })
        }

      }


    },
    loadOrderSheet(sheetId) {
      // 加载订单表格的逻辑
    },

    //获取预收款详情
    getPrePay(sub_id) {
      var that = this
      const params = {
            operKey: this.$store.state.operKey,
            sub_id: sub_id,
            supcust_id: this.sheet.supcust_id,
            companyId: this.$store.state.operInfo.companyID
          }
      this.prepayBalanceDetail = []  //清空上次选择
      GetPrepaySheetForSub(params).then(res =>{
        if (res.length >= 0) {
          res.forEach(item => {
            if (that.prepay_sheet_infos[params.sub_id]) {
              var existingItem = that.prepay_sheet_infos[params.sub_id].find(i => (i.sub_id === item.sub_id) && (i.sheet_id === item.sheet_id))
              if (existingItem) {
                item.amount = existingItem.amount;
              } else {
                item.amount = '';
              }
            } else {
              item.amount = '';
            }
            //if (item.balance < 0) item.amount = item.balance
            that.prepayBalanceDetail.push(item)
          })
          if (!that.prepay_sheet_infos[params.sub_id]){
              that.prepay_sheet_infos[params.sub_id] = that.prepayBalanceDetail
          }
          if(that.sheet.payway1_id==sub_id){
            that.inputPayWayAmount('payway1_id', params.sub_id, true)
          }
          else if(that.sheet.payway2_id==sub_id){
            that.inputPayWayAmount('payway2_id', params.sub_id, true)
          }
          else if(that.sheet.payway3_id==sub_id){
            that.inputPayWayAmount('payway3_id', params.sub_id, true)
          }
        }
      })
    },
    //展示预收款详情
    showPrePaySheet_click(sub_id, payway) {
      this.showPrePaySheet = true;
      this.current_subId = sub_id
      this.payamounts.push(payway)//存放准备赋值的payway_id
      this.getPrePay(sub_id)
    },
    //begin:计算整单折扣或优惠金额
    btnCalcDiscount_click() {
      const hasSubItem = this.checkHasSubItem()
      if (hasSubItem) {
        Toast.fail("有定货会商品，不能折扣")
        return
      }
      this.needKeyboardField = 'discount'
      this.keyboardText = ''
    },
    btnCalcDiscAmt_click() {
      if (this.sheet.sheetType === 'X') {
        var allowSheetDiscAmount = window.getRightValue('delicacy.allowSheetDiscAmount.value')
        if (allowSheetDiscAmount === 'true') {
          this.needKeyboardField = 'now_disc_amount'
          this.keyboardText = ''
        } else {
          Toast.fail('暂无优惠权限');
          this.sheet.now_disc_amount = 0
        }
      } else {
        this.needKeyboardField = 'now_disc_amount'
        this.keyboardText = ''
      }


    },
    onKeyboardInputFinished() {
      var that = this
      let realDiscount = 1;
      if (this.keyboardText) {
        realDiscount = this.getRealDiscount(Number(this.keyboardText))
      }
      if (this.needKeyboardField == 'now_disc_amount') {
        var noDiscAmount = toMoney(this.sheet.total_amount * realDiscount)
        this.sheet.now_disc_amount = toMoney(this.sheet.total_amount - noDiscAmount)
        if (realDiscount < 1) this.sheet.make_brief = this.keyboardText + '折优惠'; else if (this.sheet.make_brief.indexOf('折优惠') > 0) this.sheet.make_brief = ''
      }
      else if (this.needKeyboardField == 'discount') {
        let realDiscount = 1;
        if (this.keyboardText) {
          realDiscount = this.getRealDiscount(Number(this.keyboardText));
        }
        /*
        const maxSaleDiscPrice = window.getRightValue("delicacy.maxSaleDiscPrice.value")
        if(maxSaleDiscount&&realDiscount < this.getRealDiscount( Number(maxSaleDiscPrice))){
              that.$toast(`超过最大折扣额度，您最大折扣额度为${maxSaleDiscPrice}折`)
            return
        }*/

        this.sheet.sheetRows.map((item) => {
          /*if (item.orig_price && item.trade_type !== 'J' && item.trade_type !== 'H') {
            item.real_price = toMoney(Number(item.orig_price) * realDiscount)
            item.sub_amount = toMoney(item.real_price * item.quantity);
          }*/
          if (item.trade_type !== 'J' && item.trade_type !== 'H') {
            if (parseFloat(item.real_price) > 0 && !item.discounted) {
              item.orig_price = item.real_price
              item.discounted = true
            }

            item.real_price = toMoney(Number(item.orig_price) * realDiscount)
            item.sub_amount = toMoney(item.real_price * item.quantity);
          }
        });
        if (realDiscount < 1) this.sheet.make_brief = this.keyboardText + '折'; else if (this.sheet.make_brief.indexOf('折') > 0) this.sheet.make_brief = ''
      }
      this.onSheetRowsChanged()
      this.needKeyboardField = ''
    },
    checkHasSubItem() {
      let orderSubItemIdList = []
      this.sheet.sheetRows.forEach((row) => {
        if (row.order_sub_id) {
          orderSubItemIdList.push(row.order_sub_id)
        }
      })
      return orderSubItemIdList.length > 0
    },
    checkHasKSItem() {
      return this.sheet.sheetRows.some(item => {
        return item.trade_type == 'KS'
      })
    },
    getRealDiscount(discountPercent) {
      return discountPercent > 10 ? discountPercent / 100 : discountPercent / 10;
    },
    //end:计算整单折扣或优惠金额
    showSheetsFromCache() {

      var key = "unsubmitedSheets_" + this.sheet.sheetType;
      if (this.$store.state[key] && this.$store.state[key].length > 0) {
        if (this.$route.query.showUnsubmitedSheetsQureyFlag !== undefined) {
          let cacheSheetsFlag = this.$route.query.showUnsubmitedSheetsQureyFlag == 'false' ? false : true
          if (!cacheSheetsFlag) { // 拜访门店进入，只显示自己的缓存
            this.unsubmitedSheets = this.$store.state[key].filter(item => item.supcust_id == this.$route.query.supcust_id)
          }
        } else {
          this.unsubmitedSheets = this.$store.state[key];
        }
        if (this.unsubmitedSheets.length > 0) {
          this.showUnsubmitedSheets = true;
        } else {
          this.showUnsubmitedSheets = false;
        }
      }
    },
    addSupcust() {
      this.$router.push({
        path: "/CustomerArchivesSon",
        query: {
          source: "SaleSheet",
        },
      });
    },
    // 弹出用户选择框
    onSelectClient() {
      console.log(111)
      if (this.canNotChangeClientReason) {
        this.$toast.fail(this.canNotChangeClientReason)
        return
      }

      if (this.sheet.approve_time) return;
      this.bPopupClientSelectDialog = true;

    },
    onInputSendTime() {
      this.bPopupSelectSendTime = true
    },
    onSortByItemName() {
      this.sheet.sheetRows = this.sheet.sheetRows.sort((a, b) => a.item_name.localeCompare(b.item_name, 'zh')); //a~z 排序
      this.$forceUpdate()
    },
    // 获取选择用户信息
    onClientSelected(obj) {


      var appSheetNeedClientApproved = window.getRightValue('delicacy.appSheetNeedClientApproved.value')
      console.log('appSheetNeedClientApproved:',appSheetNeedClientApproved)
      if(appSheetNeedClientApproved === 'true' && obj.approve_status === "wait approve"){
          Toast.fail("客户未通过审核,不能选择")
          return;
      }

      this.clientInfoObj=obj
       console.log(this.sheet)
      this.sheet.acct_type_name = obj.acct_type=='pay'?'现结':obj.acct_type=='arrears'?'欠款':''
      this.sheet.acct_way_name=obj.acct_way_name
      // 切换用户，将把旧客户的定货会商品转换为普通商品
      let isShowChangeDialog = false
      var that = this
      this.bPopupClientSelectDialog = false
      console.log('client:', { obj })
      this.$store.state.supcust_id = obj.ids //添加客户信息

      if (this.sheet.sheetRows.length > 0) {
        var bHaveOrderItem = false, bHaveDisplay = false
        for (let i = 0; i < this.sheet.sheetRows.length; i++) {
          var row = this.sheet.sheetRows[i]
          if (row.order_sub_id) bHaveOrderItem = true
          if (row.disp_flow_id) bHaveDisplay = true
        }
        this.bPopupAfterChangeClient = true

        that.loadPromotions()
      }
      else {
        this.handleChangeSupcustInfo(obj)
        this.doChangeClient()

      }
      console.log("选择客户后", this.sheet)
      // function handleChangeSupcustInfo() {
      //   that.sheet.license_no = obj.license_no
      //   that.sheet.sup_name = obj.titles;
      //   that.sheet.supcust_id = obj.ids;
      //   that.sheet.mobile = obj.mobile;
      //   that.sheet.sup_addr = obj.sup_addr;
      //   that.sheet.acct_type = obj.acct_type
      // }

      // function doChangeClient(hasRows) {
      //   if(that.canAppSelectSeller && obj.charge_seller && obj.charge_seller_name ){
      //     that.sheet.seller_id=obj.charge_seller
      //     that.sheet.seller_name=obj.charge_seller_name
      //   }
      //   if (that.sheet.supcust_id === 0) {
      //     that.handleAllowRetail()
      //     that.onSheetRowsChanged()

      //   } else {
      //     that.handleClientChange(true)
      //   }
      //   that.loadPromotions()

      // }
      //this.sheet.receive_addr = obj.receive_addr;
    },
    handleChangeSupcustInfo(obj) {


      this.sheet.supcust_id = obj.ids;
      this.sheet.sup_name = obj.titles;

       // 添加统一收款信息 X,XD
      delete this.sheet.acct_cust_id
      delete this.sheet.acct_cust_name
      this.sheet.acct_supcust_id=''
      this.sheet.acct_supcust_name=''

      if (this.sheet.sheetType == "X" || this.sheet.sheetType == "XD") {
        if (obj.acct_cust_id !== '') {
          this.sheet.acct_cust_id = obj.acct_cust_id //暂时保留，兼容老服务器
          this.sheet.acct_cust_name = obj.acct_cust_name//暂时保留，兼容老服务器
          this.sheet.acct_supcust_id = obj.acct_cust_id
          this.sheet.acct_supcust_name = obj.acct_cust_name
        }
      }

      if (!this.sheet.acct_supcust_id ) {
        this.sheet.acct_supcust_id =this.sheet.supcust_id
        this.sheet.acct_supcust_name =this.sheet.sup_name
      }

      this.sheet.license_no = obj.license_no

      this.sheet.mobile = obj.mobile;
      this.sheet.sup_addr = obj.sup_addr;
      this.sheet.receive_addr = obj.receive_addr;
      this.sheet.acct_type = obj.acct_type
    },
     doChangeClient() {
        if(this.canAppSelectSeller && this.clientInfoObj.charge_seller && this.clientInfoObj.charge_seller_name ){
          this.sheet.seller_id=this.clientInfoObj.charge_seller
          this.sheet.seller_name=this.clientInfoObj.charge_seller_name
        }
        if (this.sheet.supcust_id === 0) {
          this.handleAllowRetail()
          this.onSheetRowsChanged()
          this.loadPromotions()
        } else {
          this.handleClientChange(true)
        }

    },
    handleAllowRetail() {
      this.sheet.acct_cust_name = ""
      this.sheet.arrearsInfo = ""
      this.sheet.prepayInfo = ""
      this.sheet.dispInfo = ""

      this.paywaysForSelect = this.onloadPayways.filter((item) => item.payway_type == 'QT');
    },
    handleClientChange(isLoadingSheet) {
      // 进行支付方式等若干信息获取

      if (',CG,CT,CD,'.indexOf(',' + this.sheet.sheetType + ',') >= 0) {
        this.handleSupplierChange(isLoadingSheet)
        return
      }

      if (this.sheet.supcust_id == 0) return
      let params = {
        supcust_id: this.sheet.supcust_id
      }

      var that = this
      SheetSaleGetClientAccountInfo(params).then(res => {

        // 筛选数据
        that.sheet.mobile = res.mobile
        that.sup_rank = res.sup_rank
        that.sup_group = res.sup_group
        that.sup_regions = res.sup_regions
        if(this.sheet.sheetType=="X" || this.sheet.sheetType=="XD"){
          that.sheet.clientAllowChangePrice = res.allowChangePrice
        }

        that.dispAccounts = res.disp

        // 在这里试探性给结算方式和客户地址赋值，以解决从签到进入销售单不会触发客户选择事件的问题
        if (res.sup_addr) {
          that.sheet.sup_addr = res.sup_addr
        }
        if (res.acct_type) {
          that.sheet.acct_type_name = res.acct_type=='pay'?'现结':res.acct_type=='arrears'?'欠款':''
        }

        //加入普通支付方式
        var payways_select = that.onloadPayways.filter(item => item.payway_type == 'QT')
        if (that.sheet.sheetType != "DH") {
          //加入有余额的预收款科目
          var prepaySubsWithBal
          if (',CG,CT,'.indexOf(',' + this.sheet.sheetType + ',') >= 0) {
            prepaySubsWithBal = res.prepay.filter((item) => item.payway_type == 'YF')
          } else {

            prepaySubsWithBal = res.prepay.filter(item => item.payway_type == 'YS' && item.is_order != 'True')
          }
          payways_select = payways_select.concat(prepaySubsWithBal)
          console.log('prepaySubsWithBal:', prepaySubsWithBal)
          console.log('payways_select:', payways_select)
          //加入没有余额的预收款科目
          if (",X,".indexOf(',' + this.sheet.sheetType + ',') >= 0) {

              if(Number(this.sheet.total_amount)<=0)
            {
              let payways_noBalance = that.onloadPayways.filter(sub => sub.payway_type == 'YS' && !prepaySubsWithBal.find(subBal => subBal.sub_id == sub.sub_id))
              payways_select = payways_select.concat(payways_noBalance)
            }
          }
          if (",T,TD,".indexOf(',' + this.sheet.sheetType + ',') >= 0) {
            let a = that.onloadPayways
            let payways_noBalance = that.onloadPayways.filter(sub => sub.payway_type == 'YS' && !prepaySubsWithBal.find(subBal => subBal.sub_id == sub.sub_id))
            payways_select = payways_select.concat(payways_noBalance)
          }
          //加入陈列协议
          if (res.disp)
            payways_select = payways_select.concat(res.disp)

        }



        var payways_fee_out = that.onloadPayways.filter(item => item.payway_type == 'ZC' && item.is_ks != "true")
        payways_select = payways_select.concat(payways_fee_out)

        //#region 显示预收款和陈列协议的余额
        payways_select.forEach(payway => {
          if (payway.balance !== undefined && payway.balance !== '') {
            payway.subInfoToShow = payway.sub_name + "【" + payway.balance + "】"
          } else {
            payway.subInfoToShow = payway.sub_name
          }
        })
        this.paywaysForSelect = payways_select
        //#endregion
        console.log("paywaysForSelect", this.paywaysForSelect)
        //#region 显示arrears,prepay,disp余额
        that.arrearsInfo = ""
        if (res.arrears.length > 0 && hasRight("report.arrearsBlance.see")) {
          that.arrearsInfo = '应收款:' + toMoney(res.arrears[0].balance)
        }
        that.prepayInfo = ""
        that.dispInfo = ""
        that.sheet.prepaySubs = res.prepay
        that.weChatInfo = res.weChatInfo


        if (res.prepay.length > 0) {
          for (var i = 0; i < res.prepay.length; i++) {
            //var sub_id = res.prepay[i].sub_id
            var sub_name = res.prepay[i].sub_name
            var prepay_balance = res.prepay[i].balance
            if (!sub_name && prepay_balance) sub_name = '预收款'
            if (Number(prepay_balance) != 0) {
              that.prepayInfo += sub_name + ':' + toMoney(prepay_balance)
              that.prepayInfo += "\u3000"
            }
          }
        }
        if (res.disp.length > 0) {
          res.disp.forEach(item => {
            if (Number(item.balance) >= 0) {
              that.dispInfo += item.sub_name + ":" + item.balance + "\u3000"
            }
          })
        }


        //#endregion

        this.onSheetRowsChanged(isLoadingSheet)
        this.loadPromotions()

      }).catch(() => {
      })
    },
    handleSupplierChange(isLoadingSheet) {
      // 进行支付方式等若干信息获取

      if (this.sheet.supcust_id == 0) return

      let params = {
        supcust_id: this.sheet.supcust_id
      }
      var that = this
      ApiSheetBuyGetSupplierAccountInfo(params).then(res => {
        // 筛选数据
        that.sheet.mobile = res.mobile
        that.sheet.sup_rank = res.sup_rank
        // that.sheet.sup_group = res.sup_group
        // that.sheet.sup_regions = res.sup_regions
        //加入普通支付方式
        var payways_select = that.onloadPayways.filter(item => item.payway_type == 'QT')


        //加入有余额的预收款科目
        var prepaySubsWithBal

        prepaySubsWithBal = res.prepay.filter((item) => item.payway_type == 'YF')

        payways_select = payways_select.concat(prepaySubsWithBal)
        //加入有余额的预收款科目
        if (",CT,".indexOf(that.sheet.sheetType) >= 0) {
          let payways_noBalance = that.onloadPayways.filter(sub => sub.payway_type == 'YF' && !prepaySubsWithBal.find(subBal => subBal.sub_id == sub.sub_id))
          payways_select = payways_select.concat(payways_noBalance)
        }





        var payways_fee_in = that.onloadPayways.filter(item => item.payway_type == 'SR' && item.is_ks != "true")
        payways_select = payways_select.concat(payways_fee_in)

        //#region 显示预收款和陈列协议的余额
        payways_select.forEach(payway => {
          if (payway.balance !== undefined && payway.balance !== '') {
            payway.subInfoToShow = payway.sub_name + "【" + payway.balance + "】"
          } else {
            payway.subInfoToShow = payway.sub_name
          }
        })

        //#endregion
        this.paywaysForSelect = payways_select

        //#region 显示arrears,prepay,disp余额
        that.arrearsInfo = ""
        if (res.arrears.length > 0 && hasRight("report.arrearsBlance.see")) {
          that.arrearsInfo = '应付款:' + toMoney(res.arrears[0].balance)
        }
        that.prepayInfo = ""
        that.dispInfo = ""
        that.sheet.prepaySubs = res.prepay
        that.weChatInfo = res.weChatInfo
        if (res.prepay.length > 0) {
          for (var i = 0; i < res.prepay.length; i++) {
            //var sub_id = res.prepay[i].sub_id
            var sub_name = res.prepay[i].sub_name
            var prepay_balance = res.prepay[i].balance
            if (!sub_name && prepay_balance) sub_name = '预付款'
            if (Number(prepay_balance) != 0) {
              that.prepayInfo += sub_name + ':' + toMoney(prepay_balance)
              that.prepayInfo += "\u3000"
            }
          }
        }
        //#endregion

        this.onSheetRowsChanged(isLoadingSheet)

      }).catch(() => {
      })
    },
    onSelectBranch() {
      if (this.sheet.approve_time) return;
      this.bPopupBranchPicker = true;
    },
    onSelectOrderAccount() {
      if (this.sheet.approve_time) return;
      this.bPopupOrderAccountPicker = true;
    },
    onSelectSender() {
      if (this.sheet.approve_time) return;

      this.bPopupSenderPicker = true;
      var ids = this.sheet.senders_id.split(',');
      var names = this.sheet.senders_name.split(',');
      for (let i = 0; i < this.senderList.length; i++) {
        const el = this.senderList[i];
        var exist = false;
        ids.some(id => {
          if (id == el.senders_id) {
            exist = true;
            return;
          }
        })
        if (exist) {
          setTimeout(() => {
            this.$refs.sendersCheckboxes[i].toggle(true);
          }, 200);
        }
      }
    },
    remove_photo(idx) {
      console.log({ idx })
      this.sheet.appendixPhotos.splice(idx, 1)
    },
    preview_photo(idx) {
      ImagePreview({
        images: this.sheet.appendixPhotos,
        startPosition: idx,
        closeable: true,
        // asyncClose: true
      });
    },
    onImageWaySelected(item) {
      switch (item.name) {
        case '拍照':
          this.takeAppendixPhotos(Camera.PictureSourceType.CAMERA)
          break;
        case '相册':
          this.takeAppendixPhotos(Camera.PictureSourceType.PHOTOLIBRARY)
          break;
      }
    },
    async takeAppendixPhotos(sourceType) {
      const that = this;
      const originBase64 = await TakePhoto.takePhotos(sourceType)
      const compressBase64 = await ImageUtil.compress(originBase64)
      SaveSingleImage({
        operKey: this.$store.state.operKey,
        imageBase64: compressBase64
      }).then((res) => {
        if (res?.result === 'OK' && res?.data) {
          const image = res.data
          Toast.success({ message: '上传成功', duration: 500 })
          console.log('[上传图片] 成功', res)
          that.sheet.appendixPhotos.push(image)
        } else {
          const msg = res.msg ?? '图片上传失败'
          // Toast.fail(msg)
          console.warn('[上传图片] 失败', res)
          Toast.fail('[上传图片] 失败' + res)
          // that.sheet.appendixPhotos.push(compressBase64)
        }
      }).catch((err) => {
        Toast.fail('[上传图片] 失败' + err)
        console.error('[上传图片] 网络错误/通信失败' + err)
        // Toast.fail('上传失败,请检查网络连接')
        // that.sheet.appendixPhotos.push(compressBase64)
      })
    },
    onConfirmBranch(value) {
      this.sheet.branch_id = value.branch_id;
      this.sheet.branch_name = value.branch_name;
      this.bPopupBranchPicker = false;
      this.branchList.some(b => {
        if (b.branch_id == value.branch_id) {
          this.$store.commit("setCurBranchPositionList", b.branch_position)
          return true
        }
      })
      /*
      var sheetBranches = this.$store.state.sheetBranches;//[sheetType]
      sheetBranches[this.sheet.sheetType] =
      {
        branch_id: this.sheet.branch_id,
        branch_name: this.sheet.branch_name
      }
      this.$store.commit("sheetBranches", sheetBranches)
      */
      //var sheetBranches = this.$store.state.sheetBranches;//[sheetType]
      var sheetBranches = window.getCompanyStoreValue("c_sheetBranches", {})

      sheetBranches[this.sheet.sheetType] =
      {
        branch_id: this.sheet.branch_id,
        branch_name: this.sheet.branch_name
      }
      window.setCompanyStoreValue("c_sheetBranches", sheetBranches)
      this.loadPromotions();
    },
    onConfirmOrderAccount(value) {
      this.sheet.prepay_sub_id = value.sub_id;
      this.sheet.prepay_sub_name = value.sub_name;
      this.bPopupOrderAccountPicker = false;
      let objs = {
        prepay_sub_id: this.sheet.prepay_sub_id,
        prepay_sub_name: this.sheet.prepay_sub_name,
      };
      this.$store.commit("salesOrderAccount", objs);
    },
    onConfirmSender(value) {
      this.sheet.senders_id = value.senders_id;
      this.sheet.senders_name = value.senders_name;
      this.bPopupSenderPicker = false;
      let objs = {
        senders_id: this.sheet.senders_id,
        senders_name: this.sheet.senders_name
      }
      this.$store.commit("salesSender", objs)
    },
    cancelSenderPicker() {
      this.sheet.senders_id = "";
      this.sheet.senders_name = "";
      this.bPopupSenderPicker = false;
      let objs = {
        senders_id: this.sheet.senders_id,
        senders_name: this.sheet.senders_name
      }
      this.$store.commit("salesSender", objs)

    },
    // 根据权限获取仓库列表
    loadBranches() {

      logUserAction({ tm: new Date().format("yyyy-MM-dd h:m"), act: 'enter' + '_' + this.sheet.sheetType, sn: this.sheet.sheet_no })


      this.showCustomer = false;
      let params = {};
      var that = this;
      GetBranchList(params).then((res) => {
        this.branchList = [];
        this.branchListAll = [];
        this.branchListForReturn = [];
        this.branchListForSale=[];
        if (res.result === "OK") {
          for (var i = 0; i < res.data.length; i++) {
            var branch = res.data[i];
            let branchPosition = JSON.parse(branch.branch_position)
              let newBranchPosition = []
              branchPosition.forEach(e=>{
                if(e.branch_position !=="0"){
                  newBranchPosition.push(e)
                }
              })
              this.branchListAll.push({//得到所有仓库的
                branch_id:branch.branch_id,
                branch_name:branch.branch_name,
                branch_position:newBranchPosition,
                branch_type:branch.branch_type,
                negative_stock_accordance:branch.negative_stock_accordance,
              })
              let sheetTypeReplace=that.sheet.sheetType=="CD"?"CG":that.sheet.sheetType;//仓库权限没写采购订单的，用采购单的代替
              var branchValid = window.hasBranchSheetRight(//判断是否是销售仓专用
                branch.branch_id,
                sheetTypeReplace
              );
            var returnSheetType='T'
            if(this.sheet.sheetType=='XD' ||this.sheet.sheetType=='TD'){
              returnSheetType='TD'
            }
            var branchForReturnValid = window.hasBranchSheetRight(//判断是否是退货仓专用
              branch.branch_id,
              returnSheetType
            )
            // if (branchValid) this.branchList.push(branch);
            if (branchValid) {//放进销售单专用的仓库
             this.branchListForSale.push({
                branch_id:branch.branch_id,
                branch_name:branch.branch_name,
                branch_position:newBranchPosition,
                branch_type:branch.branch_type,
                negative_stock_accordance:branch.negative_stock_accordance,
              })
            }

            if (branchForReturnValid) {//放进退货单专用的仓库
              this.branchListForReturn.push({
                branch_id:branch.branch_id,
                branch_name:branch.branch_name,
                branch_position:newBranchPosition,
                branch_type:branch.branch_type,
                negative_stock_accordance:branch.negative_stock_accordance,
              })
            }
          }
          if (that.sheet.sheetType == "T" || that.sheet.sheetType == "TD") {
            this.setBranchForReturn()
          }
          if (this.sheet.branch_id) {
            this.branchList.some(b => {
              if (b.branch_id.toString() == this.sheet.branch_id.toString()) {
                this.$store.commit("setCurBranchPositionList", b.branch_position)
                return true
              }
            })
          }
          this.branchList=this.branchListForSale
          this.$store.commit("setBranchList",{
            branchList:this.branchList,
            branchListAll:this.branchListAll,
            branchListForReturn:this.branchListForReturn,
            branchListForSale:this.branchListForSale
          })
          return this.branchList,this.branchListAll,this.branchListForReturn,this.branchListForSale;
        }
      });
    },
    saveCurSheetToCache() {
      var key = "unsubmitedSheets_" + this.sheet.sheetType;
      var unsubmitedSheets = this.$store.state[key];
      if (!unsubmitedSheets) unsubmitedSheets = [];
      var sheet = this.getSheet();
      Date.prototype.format = function (fmt) {
        var o = {
          "M+": this.getMonth() + 1, //月份
          "d+": this.getDate(), //日
          "h+": this.getHours(), //小时
          "m+": this.getMinutes(), //分
          "s+": this.getSeconds(), //秒
          "q+": Math.floor((this.getMonth() + 3) / 3), //季度
          S: this.getMilliseconds(), //毫秒
        };
        if (/(y+)/.test(fmt))
          fmt = fmt.replace(
            RegExp.$1,
            (this.getFullYear() + "").substr(4 - RegExp.$1.length)
          );
        for (var k in o)
          if (new RegExp("(" + k + ")").test(fmt))
            fmt = fmt.replace(
              RegExp.$1,
              RegExp.$1.length == 1
                ? o[k]
                : ("00" + o[k]).substr(("" + o[k]).length)
            );
        return fmt;
      };
      sheet.saveTime = new Date().format("yyyy-MM-dd h:m");
      for (var i = unsubmitedSheets.length - 1; i >= 0; i--) {
        var sht = unsubmitedSheets[i];
        if (sht.sup_name == sheet.sup_name) {
          unsubmitedSheets.splice(i, 1);
        }
      }
      unsubmitedSheets.unshift(sheet);
      if (unsubmitedSheets.length > 5)
        unsubmitedSheets.splice(5, unsubmitedSheets.length - 5);
      this.$store.commit(key, unsubmitedSheets);
    },
    removeCurSheetFromCache() {
      var key = "unsubmitedSheets_" + this.sheet.sheetType;
      var unsubmitedSheets = this.$store.state[key];
      if (!unsubmitedSheets) unsubmitedSheets = [];
      var sheet = this.getSheet();

      Date.prototype.format = function (fmt) {
        var o = {
          "M+": this.getMonth() + 1, //月份
          "d+": this.getDate(), //日
          "h+": this.getHours(), //小时
          "m+": this.getMinutes(), //分
          "s+": this.getSeconds(), //秒
          "q+": Math.floor((this.getMonth() + 3) / 3), //季度
          S: this.getMilliseconds(), //毫秒
        };
        if (/(y+)/.test(fmt))
          fmt = fmt.replace(
            RegExp.$1,
            (this.getFullYear() + "").substr(4 - RegExp.$1.length)
          );
        for (var k in o)
          if (new RegExp("(" + k + ")").test(fmt))
            fmt = fmt.replace(
              RegExp.$1,
              RegExp.$1.length == 1
                ? o[k]
                : ("00" + o[k]).substr(("" + o[k]).length)
            );
        return fmt;
      };
      sheet.saveTime = new Date().format("yyyy-MM-dd h:m");
      for (var i = unsubmitedSheets.length - 1; i >= 0; i--) {
        var sht = unsubmitedSheets[i];
        if (sht.sup_name == sheet.sup_name) {
          unsubmitedSheets.splice(i, 1);
        }
      }
      this.$store.commit(key, unsubmitedSheets);
    },
    showPrivateModePrompt() {
      if (this.inPrivateMode) {
        Toast.success({ message: '进入隐私模式, 双击顶部标题返回', duration: 3000 })
      }
    },
    // 获取缓存
    onUnsubmitedSheetSelected(obj) {

      this.copyFlag = true
      this.sheet = obj;
      //this.updateTotalAmount();
      //this.updatePayAmount();
      this.showUnsubmitedSheets = false;
      this.handleClientChange(true)

      //this.calcSaleAndReturnAmount();

      // this.changeHeight();
      this.showPrivateModePrompt()
      this.loadPromotions();
    },
    onUnsubmitedSheetDelete(index) {
      var key = "unsubmitedSheets_" + this.sheet.sheetType;
      this.unsubmitedSheets.splice(index, 1);
      this.$store.commit(key, this.unsubmitedSheets);
    },
    // 头部搜索条件显示
    /*onBranchSelected(obj) {
      this.sheet.branch_id = obj.branch_id || "";
      this.sheet.branch_name = obj.branch_name || "";
      this.sheet.sup_name = obj.sup_name || "";
      this.sheet.supcust_id = obj.supcust_id || "";
      this.sheet.mobile = obj.mobile || "";
    },*/
    // 添加商品
    btnClassView_click() {

      let selectSenderRight = window.hasRight("delicacy.appSelectSender.value")
      let isNecessarySelectSender = window.getSettingValue('saleSheetNeedSender')

      if (this.sheet.approve_time) {
        return
      }
      if (this.sheet.supcust_id !== 0 && !this.sheet.supcust_id) {
        Toast.fail("请选择客户");
      } else if (this.sheet.sheetType == 'X' && selectSenderRight && isNecessarySelectSender.toLowerCase() == 'true' && !this.sheet.senders_id) {
        Toast.fail("请选择送货员");
      } else if (!this.sheet.branch_id && this.sheet.sheetType !== 'DH') {
        Toast.fail("请选择仓库");
      } else if (this.sheet.sheetType === 'DH' && !this.sheet.prepay_sub_id) {
        Toast.fail("请选择定货会账户");
      } else {
        if (this.sheet.sheetType === 'DH') {
          this.sheet.branch_id = -1
        }
        let query = {
          //branch_id:this.sheet.branch_id,
          //branch_name:this.sheet.branch_name,
          supcust_id: this.sheet.supcust_id,
          sup_name: this.sheet.sup_name,
          searchStr: this.searchStr || "",
         // onload_remarks: this.onloadBrief,
        //  sheet: this.sheet,
          // branchList:this.branchList,
        };
        this.$store.commit("currentSheet", this.sheet);
        this.$router.push({
          name: "SelectItems",
          query: query,
          params: {
            data:
            {
              salePromotionCombines: this.salePromotionCombines,
              salePromotionSecKills: this.salePromotionSecKills,
              salePromotionFullDiscs: this.salePromotionFullDiscs,
              salePromotionFullGifts: this.salePromotionFullGifts,
              salePromotionCashPrizes: this.salePromotionCashPrizes
            },
            sheet:this.sheet,
            onload_remarks:this.onloadBrief
          }
        });
      }
    },
    onSearchInputKeyDown(e) {
      if (e.keyCode == 13) {
        this.bluetoothDeviceInputFlag = true  // 判定是键盘设备输入，如蓝牙、系统键盘回车
        //this.btnClassView_click();
        this.queryScan()
      }
      //  console.log('keydown',e)
    },
    // btnScanBarcode_click() {
    //   var that = this;
    //   if (that.sheet.approve_time) return;
    //   scanBar(
    //     (res) => {
    //       that.searchStr = res;
    //       that.btnClassView_click();
    //     },
    //     (err) => {
    //       console.log("barcode scan fail:", err);
    //     }
    //   );
    // },
    getScanBarResult(unit_type = '') {
      return new Promise((resolve, reject) => {
        const supportFormat =  {
                Code128: true,
                Code39: true,
                Code93: true,
                CodaBar: true,
                DataMatrix: true,
                EAN13: true,
                EAN8: true,
                ITF: true,
                QRCode: false,
                UPCA: true,
                UPCE: true,
                PDF417: true,
                Aztec: true,
              }
        const androidconfig = {
              barcodeFormats:supportFormat,
              beepOnSuccess: true,
              vibrateOnSuccess: false,
              detectorSize: .6,
              rotateCamera: false,
          // preferFrontCamera: false, // iOS and Android
          // showFlipCameraButton: true, // iOS and Android
          // showTorchButton: true, // iOS and Android
          // torchOn: false, // Android, launch with the torch switched on (if available)
          // saveHistory: true, // Android, save scan history (default false)
          // prompt: "Place a barcode inside the scan area", // Android
          // resultDisplayDuration: 1000, // Android, display scanned text for X ms. 0 suppresses it entirely, default 1500
          // // formats: "", // default: all but PDF_417 and RSS_EXPANDED
          // orientation: "portrait", // Android only (portrait|landscape), default unset so it rotates with the device
          // disableAnimations: true, // iOS
          // disableSuccessBeep: false // iOS and Android
        }
        const iosconfig = {
          preferFrontCamera: false, // iOS and Android
          showFlipCameraButton: false, // iOS and Android
          showTorchButton: true, // iOS and Android
          torchOn: false, // Android, launch with the torch switched on (if available)
          disableAnimations: false, // iOS
          disableSuccessBeep: false // iOS and Android
        }
        let barcodeScannerAndroidConfig = {
          preferFrontCamera: false, // iOS and Android
          showFlipCameraButton: true, // iOS and Android
          showTorchButton: true, // iOS and Android
          torchOn: false, // Android, launch with the torch switched on (if available)
          saveHistory: true, // Android, save scan history (default false)
          prompt: "Place a barcode inside the scan area", // Android
          resultDisplayDuration: 1000, // Android, display scanned text for X ms. 0 suppresses it entirely, default 1500
          // formats: "", // default: all but PDF_417 and RSS_EXPANDED
          orientation: "portrait", // Android only (portrait|landscape), default unset so it rotates with the device
          disableAnimations: true, // iOS
          disableSuccessBeep: false, // iOS and Android
          barcodeFormats:supportFormat
        }


        const config = isiOS ? iosconfig : (typeof cordova.plugins.mlkit?.barcodeScanner == 'undefined' ? androidconfig : barcodeScannerAndroidConfig)
        const plugin = isiOS || typeof cordova.plugins.mlkit?.barcodeScanner == 'undefined' ? cordova.plugins.barcodeScanner : cordova.plugins.mlkit.barcodeScanner
        if (isiOS) {
          plugin.scan(
            async (result) => {
              const res = { unit_type, code: result.text, format: result.format }
              resolve(res)
            },
            async (res) => {
              reject(res)
            },
            config
          );

        } else {
          const useOldPlugin = typeof cordova.plugins.mlkit?.barcodeScanner == 'undefined'
          console.log(useOldPlugin)
          if (useOldPlugin) {
            plugin.scan(

              async (result) => {
                const res = { unit_type, code: result.text, format: result.format }
                resolve(res)
              },
              async (res) => {
                reject(res)
              },
              config
            );
          } else {
            plugin.scan(
              config,
              async (result) => {
                const res = { unit_type, code: result.text, format: result.format }
                resolve(res)
              },
              async (res) => {
                reject(res)
              }
            );
          }
        }
      })
    },

    async btnScanBarcode_click() {
      if (this.sheet.approve_time) return
      const result = await this.getScanBarResult()
      /*const SUPPORT_CODE_TYPE=['EAN_13',"EAN_8","ITF"]
      if (SUPPORT_CODE_TYPE.indexOf(result.format)===-1) {
        this.$toast("请扫描8、13或14位条码")
        return
      }*/
      // this.searchStr = result.code;
      // this.btnClassView_click();
      this.pageSayCode(result.code)
    },
    // 打开编辑页面
    onRowEditDone(sheetRow) {
      this.sheet.sheetRows[this.EditSheetRowIndex] = sheetRow;
      this.popupEditSheetRowPannel = false;
      // this.updateTotalAmount();
      // this.updatePayAmount();
      //this.handleAcctType()
      // this.calcSaleAndReturnAmount();
      // this.handleSelectedSheetRows()
      // this.calculateSumQuantityUnitConv();
      this.onSheetRowsChanged()
    },
    btnGetCustomHeader() {
      let params = {}

      GetCustomImportHeader(params).then(res => {

      console.log("customHeader" + res)
      if(res !== ""){
        this.customHeader.item_name = res.item_name
      this.customHeader.item_spec = res.item_spec
      this.customHeader.unit_no = res.unit_no
      this.customHeader.barcode = res.barcode
      this.customHeader.quantity = res.quantity
      this.customHeader.real_price = res.real_price
      this.customHeader.sub_amount = res.sub_amount
      this.customHeader.use_s_unit = res.use_s_unit
      }

      })
      this.bPopupCustomHeaderInput = true

    },
    btnUploadImage_click() {
      if (this.sheet.approve_time) {
        return
      }
      if (this.sheet.supcust_id !== 0 && !this.sheet.supcust_id) {
        Toast.fail("请选择客户");
      } else if (!this.sheet.branch_id && this.sheet.sheetType !== 'DH') {
        Toast.fail("请选择仓库");
      } else if (this.sheet.sheetType === 'DH' && !this.sheet.prepay_sub_id) {
        Toast.fail("请选择定货会账户");
      } else {
        this.btnGetCustomHeader()
      }
      // this.bPopupUploadMethodSelect = true
    },
    async onConfirmUploadMethod(item) {
      console.log(item)
      let image = ''
      const ocrEngineSetting = this.appOcrEngine
      console.log(ocrEngineSetting)
      switch (item.name) {
        case '拍照':
          const originBase64 = await TakePhoto.takePhotos(Camera.PictureSourceType.CAMERA)
          const compressBase64 = await ImageUtil.compress(originBase64, 2400, 500)
          switch (ocrEngineSetting) {
            case 'k':
              image = originBase64
              break
            case 'h':
              image = compressBase64
              break
            default:
              image = compressBase64
              break
          }
          break;
        case '相册':
          image = await TakePhoto.takePhotos(Camera.PictureSourceType.PHOTOLIBRARY)
          break;
      }

      console.log("图片base64:" + image.split(',')[1])
      let params = {
        fileType: "base64",
        // base64字符串要截掉头部
        supcustID: this.sheet.supcust_id,
        base64Image: image.split(',')[1],
        sheetHeader: JSON.stringify(this.customHeader)

      }
      Toast.loading("解析中..."); // 第二个参数 0 表示持续显示，直到手动关闭
      this.startParsing();
      switch(ocrEngineSetting) {
        case 'h':
        BuySheetImportItems(params).then(res => {

          console.log(res)
          if (res.result === "OK") {
            this.addImportRows(res.sheetRows)
            this.completeParsing()
          }
          else {
            if (res.notMetSheetRows) {
              this.notMetItemHtml = res.notMetItemsHtml

              this.notMetItemText = this.notMetItemHtml.replaceAll("<p>","").replaceAll("</p>","\n")
              this.bPopupUpNotMetHint = true
              this.addImportRows(res.metSheetRows)
              this.completeParsing()
            }
            else {
              Toast.fail(res.msg)
              this.completeParsing()
            }
          }
      })
        break
        case 'k':
        KimiTableRecognition(params).then(res => {

          console.log(res)
          if (res.result === "OK") {
            this.addImportRows(res.sheetRows)
            this.completeParsing()
          }
          else {
            if (res.notMetSheetRows) {
              this.notMetItemHtml = res.notMetItemsHtml

              this.notMetItemText = this.notMetItemHtml.replaceAll("<p>","").replaceAll("</p>","\n")
              this.bPopupUpNotMetHint = true
              this.addImportRows(res.metSheetRows)
              this.completeParsing()
            }
            else {
              Toast.fail(res.msg)
              this.completeParsing()
            }
          }
      })
        break
        default:
        BuySheetImportItems(params).then(res => {

          console.log(res)
          if (res.result === "OK") {
            this.addImportRows(res.sheetRows)
            this.completeParsing()
          }
          else {
            if (res.notMetSheetRows) {
              this.notMetItemHtml = res.notMetItemsHtml

              this.notMetItemText = this.notMetItemHtml.replaceAll("<p>","").replaceAll("</p>","\n")
              this.bPopupUpNotMetHint = true
              this.addImportRows(res.metSheetRows)
              this.completeParsing()
            }
            else {
              Toast.fail(res.msg)
              this.completeParsing()
            }
          }
      })
      break
      }
      // this.bPopupUploadMethodSelect = false
    },
    copyNotMetItems() {
      let clipboard = new Clipboard('#notMetItem')
      clipboard.on('success', e => {
        console.log('复制成功', e)
        Toast({
          message: '复制成功'
        })
        // 释放内存
        clipboard.destroy()
      })
      clipboard.on('error', e => {
        // 不支持复制
        Toast({
          message: '手机权限不支持复制功能'
        })
        console.log('该浏览器不支持自动复制')
        // 释放内存
        clipboard.destroy()
      })

    },
    addImportRows(importRows) {
      console.log(importRows)
      importRows.forEach((row, rowIndex) => {
        this.sheet.sheetRows.push(row)
      })
    },
    startParsing() {
      this.showProgress = true;
      this.progressPercentage = 0;
      this.updateProgress();
    },
    updateProgress() {
      //let step = 1;
      this.intervalId = setInterval(() => {
        if (this.progressPercentage < 90) {
          this.progressPercentage += Math.floor(Math.random() * 1.6);
        } else if (this.progressPercentage < 99) {
          this.progressPercentage += Math.floor(Math.random() * 1.2);
        } else {
          this.progressPercentage = 100;
          clearInterval(this.intervalId);
        }
      },100)
    },
    completeParsing() {
      this.progressPercentage = 100;
      clearInterval(this.intervalId);
      setTimeout(() => {
      this.showProgress = false;
      },500)
    },
    // 开始对每个商品进行编辑
    onRowEdit(item, index) {
      //debugger
      // if (!navigator.onLine) {
      //   this.$toast("网络未连接，请检查您的网络连接。");
      //   return;
      // }
      var readonly = false;
      let readonly_type = ''; // 只读类型,用于修改之后的提示文本
      if (item.promotion_type) {
        readonly = true; readonly_type = 'promotion';
      }
      if (item.trade_type === 'H') {
        readonly = true; readonly_type = 'trade_h';
      }
      if (this.sheet.approve_time) {
        readonly = true; readonly_type = 'approved'; // * 已审的提醒优先级高于其他
      }
      this.popupEditSheetRowPannel = true;
      this.EditSheetRowIndex = index;
      let editingItem = {
        datas: item,
        onloadBrief: this.onloadBrief,
        readonly: readonly,
        readonly_type: readonly_type
      };
      console.log({editingItem})
      this.editingItem = editingItem;
      // if (item.disp_flow_id) {
      //   for (let index = 0; index < this.item.itemPrices.length; index++) {
      //     let tempRow = this.editingItem.unitPriceRows[index]
      //     this.showPopoverObj[tempRow.unit_no] = {
      //       showPopoverFlag: false,
      //       actions: [
      //         { text: `价格方案:`, price: itemPrices.planPrice },
      //         { text: `最近售价:`, price: tempRow.itemPrices.recentPrice },
      //         { text: `零售价:`, price: tempRow.itemPrices.lPrice },
      //         { text: `批发价:`, price: tempRow.itemPrices.pPrice },
      //         { text: `特价:`, price: tempRow.itemPrices.specialPrice }]
      //     }
      //     if (this.canSeeInPrice) {
      //       /*this.showPopoverObj[tempRow.unit_no].actions.push({
      //         text: `进价:`, price: tempRow.itemPrices.buyPrice
      //       })*/
      //       this.showPopoverObj[tempRow.unit_no].actions.push({
      //         text: `成本价:`, price: tempRow.itemPrices.costPrice
      //       })

      //     }
      //   }
      // }
    },

    // 打开支付方式
    onSelectPayway(paywayIndex) {

      if (this.sheet.approve_time) return;
      this.selectedPayway = paywayIndex;
      var isOrder = this.sheet[`payway${paywayIndex}_is_order`]
      if (isOrder && isOrder.toString().toLowerCase() == 'true') {
        this.popupPaywayPannel = false
        return
      }
      this.selectingPaywayIndex = paywayIndex
      if (!this.shouldPaywayDisabled(paywayIndex))
        this.popupPaywayPannel = true

    },
    // 选择支付方式
    onPaywaySelected(value) {
      this.popupPaywayPannel = false
      const index = this.selectingPaywayIndex
      if (value.pay_channel_id) {
        this.sheet[`payway${index}_channel`] = value.pay_channel_id
      } else {
        this.sheet[`payway${index}_channel`] = null
      }
      this.sheet[`payway${this.selectedPayway}_name`] = value.sub_name
      this.sheet[`payway${this.selectedPayway}_id`] = value.sub_id
      this.sheet[`payway${this.selectedPayway}_type`] = value.payway_type
      this.sheet[`payway${this.selectedPayway}_hasBalance`] = value.balance ? true : false

      this[`qrcode${this.selectedPayway}`] = value.qrcode_uri
      var payway = this.paywaysForSelect.find(p => p.sub_id == value.sub_id)
      if (payway) this[`payway${this.selectedPayway}_balance`] = payway.balance
      if(value.payway_type === 'YS') {  //在选择支付方式的时候预先将里面的预收款拿出来暂存
        //this.showPrePaySheet_click(value.sub_id, `payway${this.selectedPayway}_id`)
        this.getPrePay(value.sub_id)

        // if(this.sheet.payway1_amount) {
        //   this.sheet.payway1_amount = 0
        // }
      }

    },
    onPaywayCancel() {
      this.popupPaywayPannel = false
    },

    onSheetRowsChanged(isLoadingSheet) {

      this.calcSaleAndReturnAmount()
      this.calculateSumQuantityUnitConv()
      this.handleSelectedSheetRows()
      this.updateTotalAmount()
      if (!isLoadingSheet && !this.isLoadingSheet) {
        this.changPayWayBySheetRows()
      }

      this.handleActionsWorkContent()
    },
    changPayWayBySheetRows() {
      if (this.sheet.approve_time) return  //尽量不要减少大括号的使用，层级越少，结构越清晰


      let total = Number(this.sheet.total_amount);
      let disc_amount = Number(this.sheet.now_disc_amount);




      var usedPayWays = []
      var hasOrder = false, hasKS = false;

      for (var i = 0, len = this.sheet.sheetRows.length; i < len; i++) {
        var row = this.sheet.sheetRows[i]

        if (row.order_sub_id && row.order_sub_id != "-1") { // 定货会商品
          let sub = usedPayWays.find(p => p.sub_id = row.order_sub_id)
          if (sub) {
            sub.amount = Number(sub.amount || 0)
            sub.amount += Number(row.sub_amount)
          }
          else {
            hasOrder = true
            usedPayWays.push({ sub_id: row.order_sub_id, sub_name: row.order_sub_name, amount: row.sub_amount, type: 'orderItem' })
          }

        }
        else if (row.trade_type == "KS") {
          if (this.ksPayWayObj) {
            let sub = usedPayWays.find(p => p.sub_id = this.ksPayWayObj.sub_id)
            if (sub) {
              sub.amount = Number(sub.amount || 0)
              sub.amount += Number(row.sub_amount)
            }
            else {
              hasKS = true
              usedPayWays.push({ sub_id: this.ksPayWayObj.sub_id, sub_name: this.ksPayWayObj.sub_name, amount: row.sub_amount, type: 'ks' })
            }

          }
        }
      }

      if (usedPayWays.length > 2) {
        var msg = ''
        if (hasOrder && !hasKS) {
          msg = '定货会账号不能超过2个'
        }
        else if (hasOrder && hasKS) {
          msg = '定货会,客损的科目不能超过2个'
        }
        if (msg)
          Toast.fail(msg)
      }

      for (var i = 1; i <= 3; i++) {
        this.sheet[`payway${i}_amount`] = 0
        this.sheet[`payway${i}_is_order`] = false
        this.sheet[`payway${i}_id`] = ''
        this.sheet[`payway${i}_name`] = ''
        if (i > 1) this[`showPayWay${i}`] = false
      }

      var usedAmt = 0
      for (var i = 1; i <= usedPayWays.length; i++) {
        var p = usedPayWays[i - 1]
        this.sheet[`payway${i}_id`] = p.sub_id
        this.sheet[`payway${i}_name`] = p.sub_name
        this.sheet[`payway${i}_amount`] = p.amount
        if (p.type == 'orderItem') {
          this.sheet[`payway${i}_is_order`] = true
        }

        this[`showPayWay${i}`] = true
        usedAmt += p.amount
        usedAmt=parseFloat(toMoney(usedAmt))
      }



      if (this.sheet.acct_type === "arrears" && this.canAllowSheetArrears) {
        this.sheet.left_amount = Number(this.sheet.total_amount) - disc_amount - Number(usedAmt)
      }
      else{
        this.sheet.left_amount=0
      }

      let left_amount = Number(this.sheet.left_amount);
      this.sheet.now_pay_amount = Number(toMoney(total - left_amount - disc_amount));

      var leftAmt = Number(this.sheet.now_pay_amount) - Number(usedAmt)
      var lp = 0
      if (Math.abs(leftAmt) > 0.001) {//显示除定货会/客损以外的其他支付的金额
        var i = usedPayWays.length + 1
        var p = this.onloadPayways[lp]
        lp = 1
        this.sheet[`payway${i}_id`] = p.sub_id
        this.sheet[`payway${i}_name`] = p.sub_name
        this.sheet[`payway${i}_amount`] = leftAmt
        this[`showPayWay${usedPayWays.length + 1}`] = true
        this[`qrcode${i}`] = p.qrcode_uri
        usedPayWays.push(p)
      }

      for (var pi = usedPayWays.length + 1; pi <= 3; pi++) {
        if (!this.sheet[`payway${pi}_id`]) {
          if (lp < this.onloadPayways.length) {
            var p = this.onloadPayways[lp]
            this.sheet[`payway${pi}_id`] = p.sub_id
            this.sheet[`payway${pi}_name`] = p.sub_name
            this[`qrcode${i}`] = p.qrcode_uri
          }
        }
        lp++
      }

      this.sheet.paid_amount = this.sheet.now_pay_amount;
      this.sheet.disc_amount = this.sheet.now_disc_amount;
      if (this.sheet.sheetType == "DH" && this.sheet.prepay_sub_id == "-1") {
        this.sheet.payway1_amount = 0
        this.sheet.payway2_amount = 0
        this.sheet.payway3_amount = 0
        this.sheet.now_pay_amount = 0
        this.sheet.paid_amount = 0
      }

    },
    onLeftAmountChange() {
      if (this.g_bIgnoreInput) return
      this.updatePayAmount();
    },
    onDiscAmountChange() {
      const nowDiscAmount = this.sheet.now_disc_amount
      const maxSaleDiscAmt = window.getRightValue("delicacy.maxSaleDiscAmt.value")
      console.log(maxSaleDiscAmt)
      if (maxSaleDiscAmt) {
        var amt = 0
        if (maxSaleDiscAmt.indexOf('%') == -1) {
          amt = Number(maxSaleDiscAmt)
        }
        else {
          var bl = parseFloat(maxSaleDiscAmt.replace('%', '')) / 100
          amt = this.sheet.total_amount * bl
        }
        if (nowDiscAmount > amt) {
          Toast(`不能超过最大优惠额${maxSaleDiscAmt}`)
          this.sheet.now_disc_amount = 0
          return
        }
      }
      this.updatePayAmount();
      // this.calcSaleAndReturnAmount();
    },
    ignoreInput() {
      this.g_bIgnoreInput = true
      setTimeout(() => { this.g_bIgnoreInput = false }, 100)
    },
    onPaywayAmount(index, paywayId, sub_id, isprepay) {
       var toChangePaywayIndex = 0
      if(sub_id !== "") {
        this.inputPayWayAmount(paywayId, sub_id, isprepay)
      }
      var visiblePayWays = 0
      for (var i = 1; i <= 3; i++) {
        if (!this.shouldPaywayDisabled(i) && this[`showPayWay${i}`]) {
        //if (this[`showPayWay${i}`]) {
           if (toChangePaywayIndex == 0 && i != index) toChangePaywayIndex = i
              visiblePayWays++
        }
      }
      var bChangeBetweenPayway=false
      if(visiblePayWays > 1 && this.sheet.left_amount==0 && index>toChangePaywayIndex){
        bChangeBetweenPayway=true
      }
     // var paywayChangeStyle=window.getSettingValue('paywayChangeStyle')
      if(bChangeBetweenPayway){



      //原方式为所有支付方式联动
       if (visiblePayWays > 1 && toChangePaywayIndex > 0) {
          var otherPayWayAmount = 0
          for (var i = 1; i <= 3; i++) {
            if (i != toChangePaywayIndex) {
              otherPayWayAmount += Number(this.sheet[`payway${i}_amount`] || 0)
            }
          }
          this.sheet.now_pay_amount = Number(this.sheet.total_amount) - Number(this.sheet.now_disc_amount) - Number(this.sheet.left_amount)
          this.sheet[`payway${toChangePaywayIndex}_amount`] = this.sheet.now_pay_amount - otherPayWayAmount
        }
        else {
          var payAmt = (parseFloat(this.sheet.payway1_amount) || 0) + (parseFloat(this.sheet.payway2_amount) || 0) + (parseFloat(this.sheet.payway3_amount) || 0)
          payAmt = parseFloat(toMoney(payAmt))
          this.sheet.now_pay_amount = payAmt
          var leftAmt = parseFloat(this.sheet.total_amount) - (parseFloat(this.sheet.now_pay_amount) || 0) - (parseFloat(this.sheet.now_disc_amount) || 0)
          leftAmt = parseFloat(toMoney(leftAmt))
          this.sheet.left_amount = leftAmt
        }
      }
      else{
        var PayWayAmount = 0
        for (var i = 1; i <= visiblePayWays; i++) {
          PayWayAmount += Number(this.sheet[`payway${i}_amount`])
        }
        this.sheet.now_pay_amount = PayWayAmount
        this.sheet.left_amount = Number(this.sheet.total_amount) - Number(this.sheet.now_disc_amount) - PayWayAmount
      }
      //现为支付金额总和确定后，改动欠款


      //this.sheet.now_pay_amount = Number(this.sheet.total_amount) - Number(this.sheet.now_disc_amount) - Number(this.sheet.left_amount)

      this.ignoreInput()

    },
    // 删除
    btnRemoveRow_click(keys) {

      const targetRow = this.sheet.sheetRows[keys]
      if (targetRow?.deleteLinkId) {
        // ! 促销活动 - 组合/兑奖, 须连锁删除
        this.sheet.sheetRows = this.sheet.sheetRows.filter((_row) =>
          !_row?.deleteLinkId || _row.deleteLinkId !== targetRow.deleteLinkId
        )
      } else {
        this.sheet.sheetRows.splice(keys, 1)
      }
    },
    delIndexItem() {
      const targetRow = this.sheet.sheetRows[this.EditSheetRowIndex]
      if (targetRow?.deleteLinkId) {
        // ! 促销活动 - 组合/兑奖, 须连锁删除
        this.sheet.sheetRows = this.sheet.sheetRows.filter((_row) =>
          !_row?.deleteLinkId || _row.deleteLinkId !== targetRow.deleteLinkId
        )
      } else {
        this.sheet.sheetRows.splice(this.EditSheetRowIndex, 1)
      }
      this.popupEditSheetRowPannel = false
      this.handleSelectedSheetRows()
    },
    btnCopySheet_click() {
      logUserAction({ tm: new Date().format("yyyy-MM-dd h:m"), act: "copyclick", sn: this.sheet.sheet_no })
      Dialog.confirm({
        title: "复制单据",
        message: "请确认是否复制?",
        width: '320px'
      }).then(() => {
        this.doCopySheet()
        Toast("复制成功");
        logUserAction({ tm: new Date().format("yyyy-MM-dd h:m"), act: "copyed", sn: this.sheet.sheet_no })
      })

    },
    doCopySheet() {
      this.sheet.sheet_id = "";
      this.sheet.sheet_no = "";
      if (this.sheet.red_flag != "2" && this.sheet.red_flag != "1") {
        this.sheet.order_sheet_id = "";
        this.sheet.order_sheet_no = "";
      }

      this.sheet.approve_time = "";
      this.sheet.approver_id = "";
      this.sheet.happen_time = "";
      this.sheet.make_time = "";
      this.sheet.maker_id = "";
      this.sheet.red_flag = "";
      this.sheet.red_sheet_id = "";
      window.g_SaleSheet = this
      /*
      let ksFlag = false
      for (let i = 0; i < this.sheetRowCount; i++) {
        if (this.sheet.sheetRows[i].trade_type == 'KS') {
          ksFlag = true
          break
        }
      }
      this.existKSItem = ksFlag
      */
      //this.changeKsPayWay();
      //this.saveCurSheetToCache();
      //this.handleClientChange()
    },
    // 清空单据
    onEmpty() {
      Dialog.confirm({
        title: "清空单据",
        message: "请确认是否清空单据？",
        width: '320px'
      }).then(() => {
        this.sheet.sheetRows = []
        this.popupSubmitPannel = false
        this.clearShoppingCarCache()
        Toast.success("已清空")
      })

    },
    btnOut() {
      this.$store.commit("sheet", { noRed: true, sheetType: "" });
      myGoBack(this.$router);
    },
    handleInvite() {
      if (this.sheet.sup_name === '') {
        Toast('请选择客户')
        return
      }
      this.$refs.weChatSaleSheetQrCodeRef.handleOpenDialog()
    },
    calculateProfit(item) {
      debugger
    if (item.itemPrices && item.itemPrices.costPrice !== undefined) {
        let costPrice = item.itemPrices.costPrice || 0;
        return (item.itemPrices.pPrice - costPrice).toFixed(2);
    }
    else {
        return item.profit !== undefined ? item.profit.toFixed(2) : "0.00";
      }
    },
     //计算利润率
    calculateProfitMargin(item) {

    let profit = this.calculateProfit(item);
    let profitMargin = (profit /item.real_price) * 100;
    return profitMargin.toFixed(2);
     },
    // 计算总价和总利润

    updateTotalAmount() {
      let amount = 0;
      let totalWeight = 0;
      let total_amount =0;
      this.sheet.sheetRows.map((item) => {
        if (item.trade_type !== "KS") {
          amount += Number(item.sub_amount);
          total_amount += Number(item.profit);
        }
        if (item.quantity !== ""){
          totalWeight += Math.abs(Number(item.quantity) * Number(item.unit_weight))
        }
      })
      this.sheet.total_weight = parseFloat(totalWeight || '0').toFixed(3)
      this.sheet.total_amount = toMoney(amount);
      this.sheet.total_profit = toMoney(total_amount);
      this.calculateSumQuantityUnitConv();
    },
    calculateTotalProfit() {
      debugger;
      let amount = 0;
      let cost = 0;
      let totalProfit = 0;

      this.sheet.sheetRows.forEach((item) => {
        console.log("item", item);

        if (item.trade_type !== "KS") {
          amount += Number(item.sub_amount);
          // 如果 item.itemPrices 存在并且 costPrice 有值，使用 costPrice，否则使用 item.profit
          if (item.itemPrices && item.itemPrices.costPrice !== undefined) {
            cost += Number(item.itemPrices.costPrice) || 0;
            totalProfit = amount - cost;
          } else {
            totalProfit += Number(item.profit) || 0;
          }
        }
      });
      return totalProfit.toFixed(2);
    },

    updatePayAmount() {
      if (this.sheet.approve_time) return;
      let total = Number(this.sheet.total_amount);
      let disc_amount = Number(this.sheet.now_disc_amount);
      let left_amount = Number(this.sheet.left_amount);
      this.sheet.now_pay_amount = Number(toMoney(total - left_amount - disc_amount));
      var editablePaywayIndex = 0

      for (var i = 1; i <= 3; i++) {
        if (!this.shouldPaywayDisabled(i)) {
          editablePaywayIndex = i
          break
        }
      }

      if (editablePaywayIndex > 0) {
        var otherPaywayAmt = 0
        for (var i = 1; i <= 3; i++) {
          if (editablePaywayIndex != i) {
            var amt = parseFloat(this.sheet[`payway${i}_amount`])
            if (amt) otherPaywayAmt += amt
          }
        }
        this[`showPayWay${editablePaywayIndex}`] = true
        this.sheet[`payway${editablePaywayIndex}_amount`] = Number(toMoney(Number(this.sheet.now_pay_amount) - otherPaywayAmt))
      }

      this.sheet.paid_amount = this.sheet.now_pay_amount;
      this.sheet.disc_amount = this.sheet.now_disc_amount;
      if (this.sheet.sheetType == "DH" && this.sheet.prepay_sub_id == "-1") {
        this.sheet.payway1_amount = 0
        this.sheet.payway2_amount = 0
        this.sheet.payway3_amount = 0
        this.sheet.now_pay_amount = 0
        this.sheet.paid_amount = 0
      }
    },
    getSheet() {
      if (this.sheet.length <= 0) {
        Toast.fail("请添加商品");
      } else {
        let dataSum = this.sheet.sheetRows;

        if (this.sheet.sheetType !== 'DH') {
          dataSum.map((item) => {
            item.sub_amount = item.sub_amount ? item.sub_amount : 0;
            item.real_price = item.real_price ? item.real_price : 0;
            item.quantity = item.quantity ? item.quantity : 0;
          })
        }
        else if (this.sheet.sheetType == 'DH') {
          dataSum.map((item) => {
            item.sub_amount = item.sub_amount ? item.sub_amount : 0
            item.real_price = item.real_price ? item.real_price : 0
            item.quantity = item.quantity ? item.quantity : 0
            // item.b_unit_factor = item.bfactor ? item.bfactor : "";
            // item.b_unit_factor = item.b_unit_factor || ""
          });
        }


        if (this.sheet.sheetType == 'X') {
          this.sheet.prepay_sheet_info = JSON.stringify(this.prepay_sheet_infos)  //将预收款单转换成字符串
          if (this.sheet.prepaySubs) {
            this.sheet.prepaySubs.map((sub) => {
              var useAmt = 0
              if (this.submitClicked && this.sheet.approve_time) {
                if (this.sheet.payway1_id == sub.sub_id) {
                  useAmt += this.sheet.payway1_amount
                }
                else if (this.sheet.payway2_id == sub.sub_id) {
                  useAmt += this.sheet.payway2_amount
                }
                else if (this.sheet.payway3_id == sub.sub_id) {
                  useAmt += this.sheet.payway3_amount
                }
              }
              sub.now_balance = parseFloat(sub.balance) - parseFloat(useAmt)
              sub.now_balance = toMoney(sub.now_balance)
            })
          }

        }
        dataSum.map((item) => {
          if (item.bfactor) item.b_unit_factor = item.bfactor
          if (item.mfactor) item.m_unit_factor = item.mfactor
        })


        if (!this.sheet.payway2_amount) this.sheet.payway2_amount = 0;
        if (!this.sheet.left_amount) this.sheet.left_amount = 0;
        if (!this.sheet.disc_amount) this.sheet.disc_amount = 0;
        if (!this.sheet.now_disc_amount) this.sheet.now_disc_amount = 0;
        if (!this.sheet.maker_name)
          this.sheet.maker_name = this.$store.state.operInfo.oper_name;

        this.sheet.operKey = this.$store.state.operKey;
        //this.updateTotalAmount();
        //this.updatePayAmount();
        // this.calcSaleAndReturnAmount();
        if (this.sheet.payway1_amount == "") this.sheet.payway1_amount = 0;
        this.sheet.total_quantity = this.sumQuantityUnitConv
        return this.sheet;
      }
    },
    checkJobBeforeSave(cbContinue) {
      if (this.sheet.sheetType === 'X' || this.sheet.sheetType === 'XD') {
        var need_confirm = false;

        // 处理促销活动满赠逻辑(须防止保存后重复添加满赠)
        var need_save_promotionfullgift = false;
        var promotionFullGifts = this.$store.state.salePromotionFullGifts;
        promotionFullGifts?.forEach(giftItem => {
          giftItem.items.forEach(gift => {
            if (gift.isActive) {
              need_save_promotionfullgift = true;
            }
          })
        });
        if (need_save_promotionfullgift) {
          this.sheet.promotion_fullgift_content = JSON.stringify(promotionFullGifts)
        }

        // 处理促销活动满减逻辑
        if (!this.sheet.promotion_fulldisc_used) {
          var curr_sale = Number(this.sheet.total_amount) - Number(this.sheet.now_disc_amount);
          var promotionFullDiscs = this.$store.state.salePromotionFullDiscs;
          var maxDiscount = 0; var discountInfo = ''
          console.warn(promotionFullDiscs)
          promotionFullDiscs?.forEach((fulldisc) => {
            console.warn(fulldisc)
            fulldisc?.items?.forEach((fd) => {
              if (curr_sale >= Number(fd.threshold) && Number(fd.discount) > maxDiscount) {
                maxDiscount = Number(fd.discount)
                discountInfo = fulldisc.name + '活动的满减(满' + fd.threshold + '减' + fd.discount + ')'
              }
            })
          })

          if (maxDiscount > 0) {
            need_confirm = true
            Dialog.confirm({
              title: '确认促销活动',
              message: '本次销售可以使用' + discountInfo + '。是否添加?',
              confirmButtonText: '是',
              cancelButtonText: '否',
              width: '320px'
            })
              .then(() => {
                this.sheet.now_disc_amount = Number(this.sheet.now_disc_amount) + maxDiscount
                this.sheet.promotion_fulldisc_used = true
                this.sheet.make_brief = '使用了' + discountInfo + (this.sheet.make_brief ? '; ' + this.sheet.make_brief : '')
                Toast.success('已成功添加,请检查后再保存')
              })
              .catch(() => {
                Toast('操作取消,继续保存...')
                //this.handleSheetSave()
                cbContinue()
              })
          }
        }
        if (!need_confirm) {
          cbContinue()
        }
      }
      else {
        cbContinue()
      }

    },
    btnSave_click() {
      this.doSave()

    },
    doSave(cbDone) {
      this.checkJobBeforeSave(() => {
        this.handleSheetSave(cbDone)
      }
      )
    },
    btnApprove_click() {
      this.checkJobBeforeSave(() => {
        this.approveSheet(false)
      })

    },
    async handleSheetSave(cbDone) {
      logUserAction({ tm: new Date().format("yyyy-MM-dd h:m"), act: 'saveClick', sn: this.sheet.sheet_no })
      var sheet = JSON.parse(JSON.stringify(this.getSheet()));

      sheet.sheetRows.forEach(sheetRow => {
        if (sheetRow.attr_qty) {
          sheetRow.attr_qty = typeof sheetRow.attr_qty == 'string' ? sheetRow.attr_qty : JSON.stringify(sheetRow.attr_qty)
        }
        if(!sheetRow.output_tax_rate) sheetRow.output_tax_rate=0;
        if(!sheetRow.input_tax_rate) sheetRow.input_tax_rate=0;
        delete sheetRow.mum_attributes
      })


      var err = this.checkSheetValid();
      if (err) {
        Toast.fail(err);
        return;
      }
      //sheet.happen_time = "";
      this.IsSubmiting = true;
      if (!Number(sheet.payway2_amount)) {
        this.sheet.payway2_id = "";
        this.sheet.payway2_name = "";
        this.showPayWay2 = false;
      }

      var submitFunc;
      if (sheet.sheetType == "X" || sheet.sheetType == "T") {
        //sheet.prepay_sheet_info = JSON.stringify(prepay_sheet_infos)  //将预收款单转换成字符串
        sheet = this.handleVisitID(sheet)
        sheet.sheet_attribute = ""  //防止保存打开重复赋值
        // sheet = this.handleConfirmWorkContentResul(sheet)
        submitFunc = AppSheetSaleSave;

      } else if (sheet.sheetType == "XD" || sheet.sheetType == "TD") {
        sheet = this.handleVisitID(sheet)
        submitFunc = AppSheetSaleOrderSave;

      } else if (sheet.sheetType == "CG" || sheet.sheetType == "CT") {
        submitFunc = AppSheetBuySave;
      } else if (sheet.sheetType == "CD") {
        submitFunc = AppSheetBuyOrderSave;
      } else if (sheet.sheetType == "DH") {
        sheet = this.handleVisitID(sheet)
        submitFunc = AppSheetOrderItemSaleSave;

        this.sheet.getter_id = ""
      }
      var happenTimeOnSave = window.getSettingValue('happenTimeOnSave')
      if (happenTimeOnSave.toLowerCase() == 'true') {
        //如果设置了保存单据 保存单据时自动把交易日期弄成当下时间
        sheet.TempHappenTime = false
      }

      try {
        let res = await submitFunc(sheet)
        if (res.result === "OK") {
          Toast.success("保存成功");


          logUserAction({ tm: new Date().format("yyyy-MM-dd h:m"), act: 'saved', sn: res.sheet_no })
          this.removeCurSheetFromCache();
          this.IsSubmiting = false;
          this.sheet.make_time = res.make_time;
          this.sheet.happen_time = res.happen_time;
          this.sheet.sheet_no = res.sheet_no;
          this.sheet.sheet_id = res.sheet_id;

          setTimeout(() => {
            this.sheetHasChange = false  //终结其他操作变化
            if (cbDone) cbDone()
          }, 100)

        } else {
          this.IsSubmiting = false;
          Toast.fail(res.msg);
        }
      } catch (error) {
        this.IsSubmiting = false;
        Toast.fail(error);
      }

      // submitFunc(sheet)
      //   .then((res) => {
      //     if (res.result === "OK") {
      //       Toast.success("保存成功");
      //       logUserAction({ tm: new Date().format("yyyy-MM-dd h:m"), act: 'saved', sn: res.sheet_no })
      //       this.removeCurSheetFromCache();
      //       this.IsSubmiting = false;
      //       this.sheet.make_time = res.make_time;
      //       this.sheet.happen_time = res.happen_time;
      //       this.sheet.sheet_no = res.sheet_no;
      //       this.sheet.sheet_id = res.sheet_id;

      //       this.sheetHasChange = false
      //       this.saveSuccessFlag = true
      //     } else {
      //       this.IsSubmiting = false;
      //       Toast.fail(res.msg);
      //     }
      //   })
      //   .catch((error) => {
      //     this.IsSubmiting = false;
      //     Toast.fail(error);
      //   });
    },
    checkSheetValid() {
      // if (Math.abs(Number(this.sheet.left_amount) + Number(this.sheet.now_disc_amount) -Number(this.sheet.total_amount))>0.01) {
      //   return "欠款与优惠总额不能超过单据总额";
      // }
      if (this.sheet.sheetRows.length <= 0) {
        return "请添加商品";
      }
      if (this.sheet.payway1_id == "" && parseFloat(this.sheet.payway1_amount || 0) != 0) {
        return "请选择支付方式";
      }
      if (this.sheet.payway2_id == "" && parseFloat(this.sheet.payway2_amount || 0) != 0) {
        return "请选择支付方式二";
      }
      if (this.sheet.payway3_id == "" && parseFloat(this.sheet.payway3_amount || 0) != 0) {
        return "请选择支付方式三";
      }

      if (this.sheet.sheetType === 'X' || this.sheet.sheetType === 'T') {
        // 增加产期检查逻辑
        for (let i = 0; i < this.sheet.sheetRows.length; i++) {
          let r = this.sheet.sheetRows[i]
          if (r.batch_level && !r.produce_date) {
            return `请输入第${i + 1}商品【${r.item_name}】的生产日期，或选择“无产期”`
          }
        }
      }
      var haveOrderItems = false
      this.sheet.sheetRows.forEach(row => {
        if (row.trade_type === 'DH' && parseFloat(row.real_price) > 0 && row.order_sub_id && row.order_sub_id != "-1") haveOrderItems = true
      })


      var isOrderPayWay = index => {
        var paywayID = this.sheet[`payway${index}_id`]
        var paywayAmount = parseFloat(this.sheet[`payway${index}_amount`] || 0)
        if (this.sheet.prepaySubs) {
          var payway = this.sheet.prepaySubs.find(sub => sub.sub_id == paywayID)
          if (paywayAmount != 0 && payway && payway.is_order && payway.is_order.toLowerCase() == 'true') {
            return true
          }
        }
        return false
      }
      var haveOrderPayWay = isOrderPayWay(1) || isOrderPayWay(2) || isOrderPayWay(3)

      if (',X,XD,T,TD,'.indexOf(this.sheet.sheetType) >= 0) {
        if (haveOrderItems && !haveOrderPayWay) return "定货会商品需要用定货会账户支付"
        if (!haveOrderItems && haveOrderPayWay) return "没有定货会商品无法使用定货会账户支付"
      }


      var judgeBal = index => {
        var paywayID = this.sheet[`payway${index}_id`]
        var paywayAmount = parseFloat(this.sheet[`payway${index}_amount`] || 0)
        if (paywayAmount != 0 && !this.canNagativePrepay) {
          var sub = this.paywaysForSelect.find(s => s.sub_id == paywayID)
          if (sub && parseFloat(sub.balance || 0) != 0 && paywayAmount > parseFloat(sub.balance)) {
            return `${sub.sub_name}支付金额不能大于余额${sub.balance}`;
          }
        }
        return ''
      }
      var err = judgeBal(1) || judgeBal(2) || judgeBal(3)
      if (err) {
        return err
      }


      const typeAction = this.displayGiveProofsType.key
      if (this.$refs['displayActionsListRef']) {
        err = this.$refs['displayActionsListRef'].handleCheckOutAction()
        if (err !== '') {
          return err
        }
      }
      err = this.handleCheckSeparateDisplayAndSale(this.sheet.sheetRows)
      if (err !== '') {
        return err
      }
      err = this.checkAttrQtyValid()
      if (err) {
        return err
      }

      // 允许开单优惠与合计收支不一致
      if (!this.negativeDiscAmt && Number(this.sheet.now_disc_amount) !== 0) {
        if (!checkSignConsistency(this.sheet.now_disc_amount, this.sheet.total_amount)) {
          return '不允许负优惠'
        }
      }
      // 允许开单支付与合计收支不一致
      if (!this.negativePayAmt && Number(this.sheet.payway1_amount) !== 0) {
        if (!checkSignConsistency(this.sheet.payway1_amount, this.sheet.total_amount)) {
          return '不允许负支付'
        }
      }
      if (!this.negativePayAmt && Number(this.sheet.payway2_amount) !== 0) {
        if (!checkSignConsistency(this.sheet.payway2_amount, this.sheet.total_amount)) {
          return '不允许负支付'
        }
      }
      if (!this.negativePayAmt && Number(this.sheet.payway3_amount) !== 0) {
        if (!checkSignConsistency(this.sheet.payway3_amount, this.sheet.total_amount)) {
          return '不允许负支付'
        }
      }
      // 允许开单欠款与合计收支不一致
      if (!this.negativeLeftAmt && Number(this.sheet.left_amount) !== 0) {
        if (!checkSignConsistency(this.sheet.left_amount, this.sheet.total_amount)) {
          return '不允许负欠款'
        }
      }

      //如果使用陈列协议的钱支付的话，需要校验这个钱的余额是否足够
      console.log('this.sheet.payway1_hasBalance', this.sheet.payway1_hasBalance)
      if (this.dispAccounts) {
        for(var row of this.dispAccounts){
          if ((this.sheet.payway1_id == row.sub_id&&this.sheet.payway1_hasBalance && parseFloat(this.sheet.payway1_amount || 0) > parseFloat(row.balance)) || (this.sheet.payway2_id && this.sheet.payway2_id == row.sub_id &&this.sheet.payway2_hasBalance&& parseFloat(this.sheet.payway2_amount) > parseFloat(row.balance))) {
              return  '请勿超过' + row.sub_name + '支出账户的余额';
            }
        }
      }
      /*
          判断两个数的符号是否一致
          参数：
          a: 第一个数
          b: 第二个数
          返回值：
          true：如果两个数的符号一致（都为非负数或都为负数）
          false：如果两个数的符号不一致
          */
      function checkSignConsistency(a, b) {
        a = Number(a)
        b = Number(b)
        if ((a >= 0 && b >= 0) || (a < 0 && b < 0)) {
          return true;
        } else {
          return false;
        }
      }
      return "";
    },
    getPayChannels() {
      const payChannels = new Map()
      for (let i = 1; i <= 3; i++) {
        const channel = this.sheet[`payway${i}_channel`]
        if (channel) {
          const key = 'channel_' + channel
          const value = this.sheet[`payway${i}_amount`]
          if (payChannels.has(key))
            payChannels.set(key, payChannels.get(key) + value)
          else
            payChannels.set(key, value)
        }
      }
      console.log('payChannels:', payChannels)
      return payChannels
    },
    stringifyMap(map) {
      let o = {}
      for (const key of map.keys())
        o[key] = map.get(key)
      return JSON.stringify(o)
    },
    btn_pay_OR_unpay_click() {
      console.log('sheet.payway1_channel:', this.sheet.payway1_channel)
      if (this.sheet.sheetType !== 'X') return;
      if (this.sheet.payway1_channel == 2 || this.sheet.payway2_channel == 2 || this.sheet.payway3_channel == 2) {
        console.log('银盛支付暂不加入APP支付...')
        return;
      }
      this.isBillPaid ? unpay(this) : pay(this)

      function unpay(_this) {
        Dialog.confirm({
          title: '订单已支付。',
          message: '确认要进行退款吗?\n退款后仍可再次支付。',
          width: '320px'
        }).then(() => {
          _this.refundBill()
        }).catch(() => { })
      }
      function pay(_this) {
        if (_this.isBillPaid) {
          Toast.success('已支付'); return
        }

        const payChannels = _this.getPayChannels()
        const params = {
          operKey: _this.$store.state.operKey,
          supcustId: _this.sheet.supcust_id,
          sheetId: _this.sheet.sheet_id,
          sheetType: _this.sheet.sheetType,
          items: '',
          payChannels: _this.stringifyMap(payChannels)
        }
        GetBillPayCode(params).then(res => {
          console.log('[GetBillPayCode] Res:', res)
          if (res.result === 'OK') {
            if (res.msg) {
              // *后台校验发现订单已经被支付过了等情况
              // *此时无需额外处理,提示即可,大不了让用户退出重进
              Toast.success(res.msg); return
            }
            const payCodes = JSON.parse(res.data)
            for (var c in payCodes) {
              // todo: 一单多付有一些复杂逻辑需要处理，以后再做
              _this.billPayPanel.payChannel = c
              _this.billPayPanel.payAmount = payChannels.get(c)
              _this.billPayPanel.qrCode = payCodes[c]
            }
            _this.billPayPanel.show = true
          } else {
            Toast.fail(res.msg)
          }
        }).catch(err => {
          console.error('[GetBillPayCode] Err:', err)
          Toast.fail(err)
        })
      }
    },
    refundBill() {
      if (!this.isBillPaid) {
        Toast.fail('未支付'); return
      }

      const payChannels = this.getPayChannels()
      const params = {
        operKey: this.$store.state.operKey,
        sheetId: this.sheet.sheet_id,
        sheetType: this.sheet.sheetType,
        payChannels: this.stringifyMap(payChannels)
      }

      RefundBill(params).then(res => {
        console.log('[RefundBill] Res:', res)
        if (res.result === 'OK') {
          const refundMsgs = JSON.parse(res.data)
          let errmsg = ''
          for (var c in refundMsgs) {
            errmsg += refundMsgs[c]
          } // todo: 适配一单多付时须改造此部分逻辑。
          if (errmsg) {
            Toast.fail(errmsg)
          } else {
            Toast.success('退款成功')
            const arr = [1, 2, 3]
            arr.forEach(i => {
              this.sheet[`payway${i}_status`] = ''
            })
            this.calBillPaid()
            this.$forceUpdate()
          }
        } else {
          Toast.fail(res.msg)
        }
      }).catch(err => {
        console.error('[RefundBill] Err:', err)
        Toast.fail(err)
      })
    },
    checkBillPaid(unpaidCb, errCb) {
      const params = {
        operKey: this.$store.state.operKey,
        sheetId: this.sheet.sheet_id,
        sheetType: this.sheet.sheetType,
        payChannel: this.billPayPanel.payChannel
      }
      GetBillStatus(params).then(res => {
        console.log('[checkBillPaid] Res:', res)
        if (res.result === 'OK') {
          Toast.success(res.msg)

          // console.log('this.billPayPanel.payChannel:', this.billPayPanel.payChannel)
          for (let i = 1; i <= 3; i++) {
            if (this.sheet[`payway${i}_channel`] === this.billPayPanel.payChannel.replace('channel_', ''))
              this.sheet[`payway${i}_status`] = '已支付'
            // console.log('payway' + i + '_channel:', this.sheet[`payway${i}_channel`])
            // console.log('payway'+i+'_status:', this.sheet[`payway${i}_status`])
          }
          //this.isBillPaid = true
          this.calBillPaid()
          this.$forceUpdate()

          this.billPayPanel.show = false
        } else {
          if (unpaidCb) unpaidCb(res.msg)
        }
      }).catch(err => {
        console.error('[checkBillPaid] Err:', err)
        if (errCb) errCb(err)
      })
    },
    btnPaid_click() {
      this.checkBillPaid(Toast.fail, Toast.fail)
    },

    btnReview_click() {
      let needBatchOnReview = false
      if (this.$store.state.operInfo.setting && this.$store.state.operInfo.setting.needBatchOnReview && this.$store.state.operInfo.setting.needBatchOnReview == "True") {
        needBatchOnReview = true
      }
      if (needBatchOnReview) {
        let flag = this.sheet.sheetRows.some((sheetRow, index) => {
          if (sheetRow.batch_level && !sheetRow.produce_date) {
            Toast(`您开启了复核时检查产期，请输入第${index + 1}行商品的生产日期`)
            return true
          }
          if (sheetRow.batch_level == "2" && !sheetRow.batch_no) {
            Toast(`您开启了复核时检查产期，请输入第${index + 1}行商品的批次`)
            return true
          }
        })
        if (flag) return
      }

      if (this.sheet.approve_time) {
        var apiReviewFun
        if (',X,T,'.indexOf(this.sheet.sheetType) >= 0) {
          apiReviewFun = ApiSheetSaleReview
        }
        else if (',XD,TD,'.indexOf(this.sheet.sheetType) >= 0) {
          apiReviewFun = ApiSheetSaleOrderReview
        }
        apiReviewFun({ sheet_id: this.sheet.sheet_id }).then((res) => {
          if (res.result === "OK") {
            Toast.success("复核成功");
            logUserAction({ tm: new Date().format("yyyy-MM-dd h:m"), act: 'submitted', sn: res.sheet_no })
            this.IsSubmiting = false;
            this.sheet.review_time = res.review_time;

          } else {
            this.IsSubmiting = false;
            Toast.fail(res.msg);
          }
        }).catch((error) => {
          this.IsSubmiting = false;
          Toast.fail(error);
        })
      }
      else {
        this.approveSheet(true)
      }

      return "";
    },


    async approveSheet(bReview,cbDone) {
      var newSheetAfterChecked=window.getRightValue("delicacy.newSheetAfterChecked.value")
      if(newSheetAfterChecked && newSheetAfterChecked.toLowerCase()=='notallow')
      {
        const res =  await ApiCanApproveSheetToday()
        if (res.result !== "OK") {
          Toast.fail(res.msg)
          return
        }
      }


      logUserAction({ tm: new Date().format("yyyy-MM-dd h:m"), act: 'submitClick', sn: this.sheet_no })
      /*
      if (this.sheet.left_amount < -0.01) {
        Toast.fail('欠款金额不能小于0')
        return
      }*/
      if (',X,XD,T,TD,'.indexOf(this.sheet.sheetType) >= 0) {
        var allowSheetArrears = window.getRightValue('delicacy.allowSheetArrears.value')
        var allowSheetDiscAmount = window.getRightValue('delicacy.allowSheetDiscAmount.value')
        if (allowSheetArrears == 'false' && Number(this.sheet.left_amount) !== 0) {
          Toast.fail('您没有欠款权限');
          return;
        }
        if (allowSheetDiscAmount === 'false' && Number(this.sheet.now_disc_amount) !== 0) {
          Toast.fail('您没有优惠权限');
          return;
        }
      }

      if (this.sheet.sheetType === 'DH') {
        var allowSheetArrearsDH = window.getRightValue('delicacy.allowSheetArrears.value')
        if (allowSheetArrearsDH === 'false' && Number(this.sheet.left_amount) !== 0) {
          Toast.fail('暂无欠款权限');
          this.sheet.left_amount = 0
          this.updatePayAmount();
          return;
        }
      }

      var sheet = JSON.parse(JSON.stringify(this.getSheet()))
      if (bReview) sheet.bReview = true
      sheet.sheetRows.forEach(sheetRow => {
        if (sheetRow.attr_qty) {
          sheetRow.attr_qty = typeof sheetRow.attr_qty == 'string' ? sheetRow.attr_qty : JSON.stringify(sheetRow.attr_qty)
        }
        delete sheetRow.mum_attributes
      })
      var err = this.checkSheetValid()
      if (err) {
        Toast.fail(err)
        return
      }

      this.IsSubmiting = true
      if (!Number(sheet.payway2_amount)) {
        this.sheet.payway2_id = "";
        this.sheet.payway2_name = "";
        this.showPayWay2 = false;
      }
      var submitFunc;


      if (sheet.sheetType == "X" || sheet.sheetType == "T") {
        sheet.company_name = this.$store.state.account.companyName
        //sheet.prepay_sheet_info = JSON.stringify(this.prepay_sheet_infos)  //将预收款单转换成字符串
        sheet = this.handleVisitID(sheet)
        sheet.noArrearsBillsChecked = this.noArrearsBills === true ? "true":"false"
        submitFunc = AppSheetSaleSubmit;
      } else if (sheet.sheetType == "XD" || sheet.sheetType == "TD") {
        sheet = this.handleVisitID(sheet)
        submitFunc = ApiSheetSaleOrderApprove;
      } else if (sheet.sheetType == "CG" || sheet.sheetType == "CT") {
        submitFunc = AppSheetBuySubmit;
      } else if (sheet.sheetType == "CD") {
        submitFunc = AppSheetBuyOrderSubmit;
      } else if (sheet.sheetType == "DH") {
        sheet = this.handleVisitID(sheet)
        submitFunc = AppSheetOrderItemSubmit;
        this.sheet.getter_id = ""
      }
      // DH
      // if(this.sheet.sheetType == "DH") {
      //   var sheetDH = JSON.parse(JSON.stringify(sheet));
      //   sheetDH.sheetRows.forEach(sheetRow => {
      //     // 转换大单位
      //     if(sheetRow.unit_no == sheetRow.s_unit_no) {
      //       sheetRow.quantity = Number(sheetRow.quantity) * Number(sheetRow.sfactor ) / Number(sheetRow.bfactor)
      //       sheetRow.real_price = Number(sheetRow.real_price) / Number(sheetRow.sfactor ) * Number(sheetRow.bfactor)
      //       sheetRow.unit_factor = sheetRow.bfactor


      //     } else if(sheetRow.unit_no == sheetRow.m_unit_no) {
      //       sheetRow.quantity = Number(sheetRow.quantity) * Number(sheetRow.mfactor )/ Number(sheetRow.bfactor)
      //       sheetRow.real_price = Number(sheetRow.real_price) / Number(sheetRow.mfactor ) * Number(sheetRow.bfactor)
      //       sheetRow.unit_factor = sheetRow.bfactor
      //     }
      //   })
      // }
      //submitFunc(this.sheet.sheetType == "DH" ? sheetDH : sheet)
      console.log(sheet)


      submitFunc(sheet).then((res) => {
        if (res.result === "OK") {
          Toast.success("审核成功");
          logUserAction({ tm: new Date().format("yyyy-MM-dd h:m"), act: 'submitted', sn: res.sheet_no })
          this.removeCurSheetFromCache();
          this.IsSubmiting = false;
          this.sheet.approve_time = res.approve_time;
          this.sheet.happen_time = res.happen_time;

          if (bReview) this.sheet.review_time = res.review_time;
          this.sheet.sheet_no = res.sheet_no;
          this.sheet.sheet_id = res.sheet_id;

          this.clearShoppingCarCache()

          this.submitClicked = true
          if (window.g_curSheetInList) window.g_curSheetInList.state = "submited";
          var ld = window.g_listDealer
          if (ld && (this.sheet.sheetType == 'X' || this.sheet.sheetType == 'T') && ld.list && ld.list.length > 0 && ld.action == 'orderToSale') {
            var orderSheetID = ld.fieldValue
            var index = ld.list.findIndex(sht => sht[ld.fieldName] === orderSheetID);
            if (index !== -1)
              ld.list.splice(index, 1)
            window.g_listDealer = null
          }
          var sheets = this.sheetsFromeShowAccount
          var sheet = this.sheet
          var index = this.redSheetIndex
          if (this.fromShowAccount) {
            sheet.isChecked = true
            if (index !== -1) {
              sheets.splice(index, 0, sheet)
            } else {
              sheets.unshift(sheet)
            }

            this.sheetsFromeShowAccount = sheets
          }

          if (sheet.payway1_channel || sheet.payway2_channel || sheet.payway3_channel) {
            this.btn_pay_OR_unpay_click()
          }
           this.sheetHasChange = false
          if(cbDone) cbDone()
        } else {
          this.IsSubmiting = false;
          Toast.fail(res.msg);
        }
      }).catch((error) => {
        this.IsSubmiting = false;
        Toast.fail(error);
      })
    },
    handleVisitID(sheet) {
      let visitRecord = this.$store.state.visitRecord
      if (visitRecord && this.sheet.supcust_id === visitRecord.shop_id && this.sheet.visit_id === '' && visitRecord.visit_id) {
        sheet.visit_id = visitRecord.visit_id
      }
      return sheet
    },
    btnRed_click() {
      this.redBill()
    },
    // btnRedAndModify_click() {
    //   this.redBill(true)
    // },
   async btnRedAndModify_click(){
      var action = '冲改'
      $('#red-brief').val('')
      var redSheetNoBrief = window.getRightValue('delicacy.redSheetNoBrief.value')
      redSheetNoBrief = redSheetNoBrief == 'true'
      if (this.sheet.sheetType === "X" || this.sheet.sheetType === "T"||this.sheet.sheetType === "XD" || this.sheet.sheetType === "TD")
      {var res=await this.limitBothRedAndChange();
        if(res.result!='OK'&&res.msg!='')
        {
          Toast.fail(res.msg)
          return;
        }
      }
      if(!redSheetNoBrief)
      {
        Dialog.confirm({
          allowHtml: true,
          title: `输入${action}原因`,
          // message: '<van-field v-model="redBrief" type="tel" placeholder="请输入冲改原因" />',
          message: `<div><input id="red-brief" style="border-radius:4px;border:1px solid #ddd;line-height:40px;" v-model="redBrief" placeholder="${action}原因" /></div>`,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          showCancelButton: true,
          width: '320px'
        }).then(() => {

          var redBrief = $('#red-brief').val()

          if (!redBrief) {
            Toast.fail(`请输入${action}原因`)
            return
          }
          redBrief = `${action}原因: ${redBrief}`
          this.doRedAndModify(redBrief)
          this.popupSubmitPannel = false


        }).catch(() => {

        })
      }
      else {
        this.doRedAndModify('')
      }

    },
    doRedAndModify(redBrief) {
      window.redChangeSheet=JSON.parse(JSON.stringify(this.sheet))
      this.sheet.make_brief += redBrief
      this.sheet.isRedAndChange = true
      this.sheet.old_sheet_id = this.sheet.sheet_id
      this.sheet.sheet_id = ""

      this.sheet.review_time = ""
      this.sheet.reviewer_id = ""
      this.sheet.reviewer = ""

      this.sheet.approve_time = ""
      this.sheet.approver_id = ""
      this.sheet.approver_name = ""

      this.sheet.make_time = ""
      this.sheet.maker_id = ""
      this.sheet.maker_name = ""

    },
  async  redBill() {
      /* 现在红冲会自动退款,就不卡它了
      if (this.isBillPaid) {
        Toast.fail('请先退款'); return
      }*/
      if (this.sheet.sheetType === "X" || this.sheet.sheetType === "T"||this.sheet.sheetType === "XD" || this.sheet.sheetType === "TD")
      {var res=await this.limitBothRedAndChange();
        if(res.result!='OK'&&res.msg!='')
        {
          Toast.fail(res.msg)
          return;
        }
      }
      logUserAction({ tm: new Date().format("yyyy-MM-dd h:m"), act: 'redClick', sn: this.sheet_no })
      var action = '红冲'

      $('#red-brief').val('')
      var redSheetNoBrief = window.getRightValue('delicacy.redSheetNoBrief.value')
      redSheetNoBrief = redSheetNoBrief == 'true'
      if (redSheetNoBrief) {
        Dialog.confirm({
          title: "",
          message: `确定${action}吗?`,
          width: '320px'
        }).then(() => {
          this.doRedSheet('')
        })
      }
      else {
        Dialog.confirm({
          allowHtml: true,
          title: `输入${action}原因`,
          // message: '<van-field v-model="redBrief" type="tel" placeholder="请输入红冲原因" />',
          message: `<div><input id="red-brief" style="border-radius:4px;border:1px solid #ddd;line-height:40px;" v-model="redBrief" placeholder="${action}原因" /></div>`,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          showCancelButton: true,
          width: '320px'
        }).then(() => {

          var redBrief = $('#red-brief').val()
          if (!redBrief) {
            Toast.fail(`请输入${action}原因`)
            return
          }
          redBrief = `${action}原因: ${redBrief}`
          this.doRedSheet(redBrief)
        }).catch(() => {

        })
      }
    },
    async limitBothRedAndChange()
    {
      var noRedSheetAfterRed=window.getRightValue("delicacy.allowRedSheetAfterPrint.value")
      noRedSheetAfterRed = noRedSheetAfterRed.toLowerCase() == 'notallow'
      let res_temp =
            {
               result:'OK',
               msg:'',
            }
      if(noRedSheetAfterRed)
        {
          let params =
            {
              operKey: this.$store.state.operKey,
              sheetID: this.sheet.sheet_id,
            }
          if (this.sheet.sheetType === "X" || this.sheet.sheetType === "T")
            {
                var res=await ApiNoRedSheetAfterPrint(params);
              if(res.result!='OK')
              {
                res_temp.result=res.result;
                res_temp.msg=res.msg;
              }
            }
          else if (this.sheet.sheetType === "XD" || this.sheet.sheetType === "TD")
            {

              var res=await ApiNoRedOrderSheetAfterPrint(params);
              if(res.result!='OK')
              {
                res_temp.result=res.result;
                res_temp.msg=res.msg;
              }
            }
      }
      return res_temp;
    },

    doRedSheet( redBrief) {
      var redFunc;
      if (this.sheet.sheetType === "X" || this.sheet.sheetType === "T")
        redFunc = SheetSaleRed;
      else if (this.sheet.sheetType === "XD" || this.sheet.sheetType === "TD")
        redFunc = SheetSaleOrderRed;
      else if (this.sheet.sheetType === "CG" || this.sheet.sheetType === "CT")
        redFunc = SheetBuyRed;
      else if (this.sheet.sheetType === "CD")
        redFunc = SheetBuyOrderRed;
      else if (this.sheet.sheetType === "DH") {
        redFunc = SheetOrderItemRed;
      }
      let params = {
        operKey: this.$store.state.operKey,
        sheetID: this.sheet.sheet_id,
        redBrief: redBrief
      }

      redFunc(params).then((res) => {
        if (res.result === "OK") {
          this.sheet.red_flag = "1";

          if (this.sheet.make_brief) this.sheet.make_brief += ' '
          this.sheet.make_brief += redBrief


          var oldSheetID = this.sheet.sheet_id
          if (window.g_curSheetList) {
            var redSheet = JSON.parse(JSON.stringify(this.sheet));
            redSheet.sheet_id = res.sheet_id;
            redSheet.happen_time = res.happen_time;
            redSheet.oper_name = this.$store.state.operInfo.oper_name;
            redSheet.state = "red";
            window.g_curSheetList.unshift(redSheet);
            window.g_curSheetInList.state = "reded";
          }



          logUserAction({ tm: new Date().format("yyyy-MM-dd h:m"), act: 'reded', sn: this.sheet.sheet_no })
          var sheets = this.sheetsFromeShowAccount
          if (this.fromShowAccount && sheets.length > 0) {
            for (var tempi = 0; tempi < sheets.length; tempi++) {
              if (oldSheetID == sheets[tempi].sheet_id) {
                sheets.splice(tempi, 1)
                this.redSheetIndex = tempi
                break
              }
            }
            this.sheetsFromeShowAccount = sheets
          }

          Toast.success("红冲成功");

        } else {
          Toast.fail("红冲失败:" + res.msg);
        }
      }).catch((err) => {
        Toast("红冲失败了" + err);
      })
    },

    //打印  不按照模板
    async print_noTemplate(printCount) {

      this.isPrinting = true
      var that = this
      var sheet_id = this.sheet.sheet_id
      var sheet =JSON.parse(JSON.stringify(this.getSheet()))

      const defaultPrinter = window.getDefaultPrinter()
      const printerType = defaultPrinter.type
      console.log('defaultPrinter:', defaultPrinter)
      // 判断是否为云打印
      var printer_kind = defaultPrinter.kind
      if (!printer_kind) {
        if (printerType == "cloud")
          printer_kind = '24pin'
        else
          printer_kind = 'tiny'
      }

      // 不使用模板打印(小票打印)的场合，直接发送打印请求
      if (printerType == "cloud") { // 云打印机 不使用次数
        var that = this
        var imageBase64 = "";
        if (that.$store.state.company_cachet)
          imageBase64 = "data:image/png;base64," + that.$store.state.company_cachet;
        sheet = JSON.parse(JSON.stringify(sheet))
        var errMsg = that.checkAttrQtyValid()
        if (errMsg) {
          Toast.fail(errMsg)
          return
        }
        that.handlePrintInfoToAttrQty(sheet)
        let b64Esc_tinyTicket = await Printing._sheetToB64Esc(sheet, that.printBarcodeStyleForSale, that.printBarcodePic)

        var params = {
          operKey: this.$store.state.operKey,
          tmp: { width: '80', height: '100' }, // 后端接收directPrint的ESC指令时只会读取模板宽高 所以随意指定一个值
          sheet: sheet,
          printer_brand: defaultPrinter.brand,
          device_id: defaultPrinter.cloudID,
          check_code: defaultPrinter.cloudCode,
          cus_orderid: this.sheet.sheet_no,
          copies: "1",  // 云打印暂不启用
          //directPrint:true,
          escCommand: b64Esc_tinyTicket,
        }
        AppCloudPrint_escCmd(params).then(res => {
          console.log(res);
          this.showCloudPrintResult(res)
        })
        this.isPrinting = false
      }
      else { // 蓝牙打印机 启用多次打印
        /*if (!defaultPrinter.bluetoothID) {
          Toast.fail("未设置蓝牙打印机")
          return
        }*/
        var setting = this.$store.state.operInfo.setting || {}
        params = {
          supcustID: this.sheet.supcust_id,
          printArrearsBalance: setting.receiptShowArrearsBalance && setting.receiptShowArrearsBalance.toLowerCase() == 'true',
          printPrepayBalance: setting.receiptShowPrepayBalance && setting.receiptShowPrepayBalance.toLowerCase() == 'true',
          printOrderItemsBalance: setting.receiptShowOrderItemsBalance && setting.receiptShowOrderItemsBalance.toLowerCase() == 'true',
          copies: "1"
        }
        var that = this

        if (params.printArrearsBalance || params.printPrepayBalance || params.printOrderItemsBalance) {
          AppSheetGetPrintInfo(params).then(res => {
            // 筛选数据
            sheet.balanceInfo = { arrearsBalance: res.arrearsBalance, prepayBalance: res.prepayBalance, orderItemsBalance: res.orderItemsBalance }
            print() // 蓝牙多次打印
          }).catch(() => {
          })
        }
        else {
          print()
        }
      }

      // 底层打印方法
      function print() {
        var errMsg = that.checkAttrQtyValid()
        if (errMsg) {
          Toast.fail(errMsg)
          return
        }
        var imageBase64 = "";
        if (that.$store.state.company_cachet) imageBase64 = "data:image/png;base64," + that.$store.state.company_cachet;
        console.log("saleSheet", imageBase64)

        that.loadingMsg = '正在打印'

        try {
          sheet = JSON.parse(JSON.stringify(sheet))
        }
        catch (e) {
          Toast.fail({ message: '转换错误:' + e.message, duration: 8000 })
        }

        try {
          that.handlePrintInfoToAttrQty(sheet)
        }
        catch (e) {
          Toast.fail({ message: '处理口味错误:' + e.message, duration: 8000 })
        }



        try {
          Printing.printSaleSheet(sheet, that.printBarcodeStyleForSale, that.printBarcodePic, imageBase64,
            res => {
              that.loadingMsg = ''
              that.isPrinting = false
              if (res.result == "OK") {
                that.MarkPrint()
                if (!that.donePrintCount) that.donePrintCount = 1
                if (that.donePrintCount < that.printCount) {
                  that.donePrintCount++
                  print()
                }
              }
              else {
                Toast.fail({ message: res.msg, duration: 4000 })
              }
            }
          )
        }
        catch (e) {
          Toast.fail({ message: '打印错误:' + e.message, duration: 8000 })
        }

      }
      // }
    },
    //模板打印
    btnPrint_ByTemplate(tmp, printCount) {
      const that = this
      const printErrorHandler = (error) => {
        handlePrintComplete()
        Toast.fail(error)
      }
      const handlePrintComplete = () => {
        this.loadingMsg = ''
        this.isPrinting = false
      }
      console.log('Print Template:', tmp)
      // 检查单据状态
      if (!this.sheet.sheet_id) {
        Toast.fail("请先保存单据");
        return;
      }

      const defaultPrinter = window.getDefaultPrinter()
      this.loadingMsg = '正在打印' // 显示打印信息

      // 为打印机类型赋默认值(如果未定义)
      var printer_kind = defaultPrinter.kind
      if (!printer_kind) {
        if (defaultPrinter.type == "cloud")
          printer_kind = '24pin'
        else
          printer_kind = 'tiny'
      }
      // 组装单据校验的请求参数
      this.isPrinting = true
      var sTmp = tmp.template_content
      tmp = JSON.parse(tmp.template_content);
      var printTemplate = []
      const isCloud = defaultPrinter.type == 'cloud'
      if (sTmp.indexOf('"prepay_balance"') !== -1) printTemplate.push({ name: "prepay_balance" })
      if (sTmp.indexOf('"arrears_balance"') !== -1) printTemplate.push({ name: "arrears_balance" })
      if (sTmp.indexOf('"print_count"') !== -1) printTemplate.push({ name: "print_count" })
      if (sTmp.indexOf('"name":"give_qty_unit"') >= 0) printTemplate.push({ name: "give_qty_unit" })

      var smallUnitBarcode = false
      if (this.printBarcodeStyleForSale?.length) {
        if (this.printBarcodeStyleForSale.indexOf('smallUnit') !== -1) {
          smallUnitBarcode = true
        }
      }
      console.warn('smallUnitBarcode:', smallUnitBarcode)

      // 与后端通信校验单据内容
      var params = {
        sheetType: this.sheet.sheetType,
        sheet_type: this.sheet.sheet_type,
        sheet_id: this.sheet.sheet_id,
        smallUnitBarcode: smallUnitBarcode,
        printTemplate: JSON.stringify(printTemplate),
        copies: "1"
      }
      // 使用POST方法避免printTemplate参数过长导致的问题
      AppGetSheetToPrint_Post(params).then(data => {
        if (data.result != 'OK') {
          printErrorHandler(data.msg)
        }
        else {
          var sheet = data.sheet

          // 处理云打印的打印逻辑
          if (isCloud) {
            let default_params = {
              operKey: this.$store.state.operKey,
              tmp: tmp,
              sheet: sheet,
              printer_brand: defaultPrinter.brand,
              device_id: defaultPrinter.cloudID,
              check_code: defaultPrinter.cloudCode,
              cus_orderid: this.sheet.sheet_no,
              copies: "1",
              directPrint: false,
              escCommand: null,
            }
            // 当要用小票云打印机打印时，ESC指令应本地生成。
            if (printer_kind == 'tiny') {
              var comm = []
              var params = {
                operKey: this.$store.state.operKey,
                tmp: tmp,
                sheet: sheet
              }
              AppSheetToImages(params).then(async res => {
                var escCommand = ''
                var jr = []
                jr = res//JSON.parse(res);
                console.log(jr);
                for (var i = 0; i < jr.length; i++) {
                  let imgb64 = `data:image/jpg;base64,${jr[i]}` // 重要! base64ToPrintBytesPromise须使用合法的src作为图片源并处理
                  var imageBytes = await Printing.base64ToPrintBytesPromise(imgb64)
                  var s = Printing.ab2hex(imageBytes)
                  comm.push(imageBytes)
                }
                var temparr = [27, 64] // 1b 40 用来调整偏移
                for (var i = 0; i < comm.length; i++) {
                  let uint8array = comm[i]
                  for (var u = 0; u < uint8array.length; u++) {
                    temparr.push(uint8array[u])
                  }
                  // escCommand += btoa(this.uint8ArrayToBase64(comm[i]))
                  // BASE64加密不能拼接：Base64将每3个字节转换为4个字符。如果输入数据不是3字节的倍数，则编码将添加1或2个零字节作为填充。然后在流的末尾用一个或两个'='字符表示。
                  // 如果尝试解码多个串联的代码块，则流中很可能会有'='字符，这是非法的。
                }
                var finalUint8Array = Uint8Array.from(temparr)
                escCommand = btoa(this.uint8ArrayToBase64(finalUint8Array))
                //default_params.directPrint = true;
                default_params.escCommand = escCommand;
                AppCloudPrint_escCmd(default_params).then(res => {
                  console.log(res)
                  handlePrintComplete()
                  this.showCloudPrintResult(res)
                }).catch(err => { printErrorHandler(err); })
              }).catch(err => { printErrorHandler(err); })
            } else {
              // 若为针式云打印机，将变量发往后端，剩余逻辑均在后端处理。
              AppCloudPrint_sheetTmp(default_params).then(res => {
                console.log(res)
                handlePrintComplete()
                this.showCloudPrintResult(res)
              }).catch(err => { printErrorHandler(err); })
            }
          }
          else
          {
            // 蓝牙打印

            let must24Pin = false // 必须使用针式打印机的图片打印指令
            let ignorePaperFeedCmd = false // 跳过换行、走纸、换页命令
            const pname = defaultPrinter.name
            // 2023.02.13 兼容ZICOX-CC4打印机
            if (pname.match('CC4_')) { // CC4_1656L
              ignorePaperFeedCmd = true
              must24Pin = true
            }

            var params = {
              operKey: this.$store.state.operKey,
              tmp: tmp,
              sheet: sheet,
              ignorePaperFeedCmd: ignorePaperFeedCmd
            }

            Printing.initPrinter()
            if (Printing.cmdMode === '_cpcl') {
              console.warn('正在使用CPCL打印机 | 蓝牙打印')
            }

            // 蓝牙打印需要区分小票打印机和标准(针式)打印机。
            // >> 小票打印机需要后端传回图片->前端处理成ESC并打印
            // >> 针式打印机需要后端传回ESC指令->前端执行打印
            if (must24Pin || ignorePaperFeedCmd ||
              (printer_kind === '24pin' && Printing.cmdMode !== '_cpcl')
            ) {
              // * 从后端获取打印指令
              var blocks = []
              AppSheetToEsc(params).then(res => {
                var imageBytes = this.base64ToUint8Array(res)
                blocks.push({ imageBytes: imageBytes })
                Printing.printSheetOrInfo(blocks, function c(e) {
                  console.log(e)
                  if (e.result == 'Error') { Toast.fail(e.msg) }
                  else { Toast.success(e.msg); that.MarkPrint() }
                  handlePrintComplete()
                })
              }).catch(err => { printErrorHandler(err); })
            } else {
              // * 在前端生成打印指令
              AppSheetToImages(params).then(async res => {
                var blocks = []
                var jr = []
                jr = res

                /** 处理图片转译的函数(须为async) */
                let imgFunc = Printing.base64ToPrintBytesPromise
                /** 图片指令是否应添加至content, 而非imageBytes */
                let addToContent = false
                if (Printing.cmdMode === '_cpcl') {
                  imgFunc = Printing.base64ToPrintBytesPromise_Cpcl
                  addToContent = true
                }

                for (var i = 0; i < jr.length; i++) {
                  let imgb64 = `data:image/jpg;base64,${jr[i]}` // 重要! base64ToPrintBytesPromise须使用合法的src作为图片源并处理
                  var imageBytes = await imgFunc(imgb64)
                  var s = Printing.ab2hex(imageBytes)
                  addToContent ? blocks.push({ content: imageBytes }) : blocks.push({ imageBytes: imageBytes })
                }

                Printing.printSheetOrInfo(blocks, (e) => {
                  if (e.result == 'Error') {
                    Toast.fail(e.msg)
                  } else {
                    Toast.success(e.msg)
                    this.sheet.print_count = Number(this.sheet.print_count || 1) + 1
                    ApiPrintMark(
                      {
                        operKey: this.$store.state.operKey,
                        sheetType: sheet.sheetType,
                        sheetIDs: sheet.sheet_id,
                        printEach: true,
                        printSum: false
                      }).then(() => { }).catch(() => { })

                  }
                  handlePrintComplete()
                })
              }).catch(err => { printErrorHandler(err); })
            }
          }
        }
      }).catch(err => { printErrorHandler(err); })
    },
    /** 记录打印次数 */
    MarkPrint() {
      this.sheet.print_count = Number(this.sheet.print_count || 1) + 1
      ApiPrintMark({
        operKey: this.$store.state.operKey,
        sheetType: this.sheet.sheetType,
        sheetIDs: this.sheet.sheet_id,
        printEach: true,
        printSum: false
      }).then(() => { }).catch(() => { })
    },
    showCloudPrintResult(res) {
      if (res.result == "OK") {
        var duration = 3000
        var msg = '打印成功'
        if (res.msg) msg = res.msg
        if (res.printSuccess == false) duration = 6000
        this.isInPrintView = false
        Toast.success({ message: msg, duration: duration })
      }
      else {
        Toast.fail({ message: res.msg, duration: 8000 })
      }
    },
    /**
     * 处理属性商品，打印
     */
    handlePrintInfoToAttrQty(sheet) {

      for (let i = 0; i < sheet.sheetRows.length; i++) {
        let sheetRow = sheet.sheetRows[i]
        let tempArr = []
        //if (sheetRow.attr_qty && sheetRow.attr_qty !== "[]" && (sheetRow.sale_print_combine_attr=='False' ||!sheetRow.sale_print_combine_attr )) {
        if (sheetRow.attr_qty && sheetRow.attr_qty !== "[]" && ((sheetRow.sale_print_combine_attr || false).toString().toLowerCase() != 'true')) {

          let attr_qty = typeof sheetRow.attr_qty == 'string' ? JSON.parse(sheetRow.attr_qty) : sheetRow.attr_qty
          attr_qty.forEach(attr => {
            let temp = JSON.parse(JSON.stringify(sheetRow))
            temp.quantity = attr.qty
            temp.sub_amount = toMoney(Number(attr.qty) * Number(temp.real_price))
            let avail_attr_combine = temp.avail_attr_combine && temp.avail_attr_combine !== '' ? JSON.parse(temp.avail_attr_combine): ''
            let mum_attributes = temp.mum_attributes && temp.mum_attributes !== '' ?   (typeof temp.mum_attributes === 'string' ? JSON.parse(temp.mum_attributes): temp.mum_attributes): ''
            // 用于兼容开单时候没有条码，开单后录入条码
            let attrBarcodeInfoItem = {bBarcode: '', mBarcode: '', sBarcode: ''}
            let combine = Object.keys(attr).filter(key => key.startsWith('optID_')).map(key => attr[key]).sort((a,b) => a - b)
            if (avail_attr_combine) {
              let tagCombine = avail_attr_combine.find(availItem => availItem.combine.join() == combine.join())
              if (tagCombine) {
                attrBarcodeInfoItem.bBarcode = tagCombine.bBarcode
                attrBarcodeInfoItem.mBarcode = tagCombine.mBarcode
                attrBarcodeInfoItem.sBarcode = tagCombine.sBarcode
              }
            } else if (mum_attributes && combine.length === 1) {
              // 只处理一维的情况
              const key = Object.keys(attr).find(k => /^optID_\d+$/.test(k));
              const attrID = key ? key.match(/^optID_(\d+)$/)[1] : null;
              if (attrID) {
                let mumItem = mum_attributes.find(mum_attr => mum_attr.attrID+'' === attrID+'')
                if (mumItem) {
                  let tagCombine = mumItem.options.find(options => options.optID === attr[key])
                  if (tagCombine) {
                    attrBarcodeInfoItem.bBarcode = tagCombine.bBarcode
                    attrBarcodeInfoItem.mBarcode = tagCombine.mBarcode
                    attrBarcodeInfoItem.sBarcode = tagCombine.sBarcode
                  }
                }
              }
            }
            temp.b_barcode = attr.bBarcode ? attr.bBarcode : attrBarcodeInfoItem.bBarcode
            temp.m_barcode = attr.mBarcode ? attr.mBarcode : attrBarcodeInfoItem.mBarcode
            temp.s_barcode = attr.sBarcode ? attr.sBarcode : attrBarcodeInfoItem.sBarcode

            let key = -1
            for (let j = 0; j < Object.keys(attr).length; j++) {
              if (Object.keys(attr)[j].startsWith("optName")) {
                key = j;
                break
              }
            }
            temp.item_name = temp.item_name + "(" + attr[Object.keys(attr)[key]] + ")"
            tempArr.push(temp)
          })
          sheet.sheetRows.splice(i, 1)
          tempArr.forEach((arrt, index) => {
            sheet.sheetRows.splice(i + index, 0, arrt)
          })
          i += tempArr.length - 1
        }
      }
    },
    checkAttrQtyValid() {
      var totalAmt = 0
      for (let i = 0; i < this.sheet.sheetRows.length; i++) {
        let sheetRow = this.sheet.sheetRows[i]
        if (sheetRow.attr_qty && sheetRow.attr_qty !== "[]" && !sheetRow.sale_print_combine_attr) {
          let attr_qty = typeof sheetRow.attr_qty == 'string' ? JSON.parse(sheetRow.attr_qty) : sheetRow.attr_qty
          var qtyInAttr = 0
          attr_qty.forEach(attr => {
            qtyInAttr += parseFloat(attr.qty)
          })
          if (qtyInAttr != sheetRow.quantity) {
            return `${sheetRow.item_name}的分口味数量和总数不一致，请删除该行重新录入`
          }
        }

        if (sheetRow.trade_type !== 'KS') {
          totalAmt += Number(sheetRow.sub_amount)
          var qty_c_Price = Number(sheetRow.real_price) * Number(sheetRow.quantity)
          if (Math.abs(qty_c_Price - Number(sheetRow.sub_amount)) > 1) {
            return `第${i + 1}行${sheetRow.item_name}的单价乘以数量${qty_c_Price}不等于金额${sheetRow.sub_amount}，请删除该行重新录入`
          }
        }
      }
      if (Math.abs(totalAmt - Number(this.sheet.total_amount)) > 1) {
        return `明细行合计${totalAmt}不等于总额${this.sheet.total_amount}，请删除最后一行重新录入`
      }
      return ''
    },
    stayOrLeave() {
      this.goback()

    },
    goback() {
      if (!this.isPopup) {

        this.$store.commit("sheet", { noRed: true, sheetType: "" });
        this.$store.commit("classId", this.$store.state.AllItemClassId);
        if(this.sheet.red_flag == "1"){
          const params = {
            rededSheet_id: this.sheet.sheet_id
          }
          this.myGoBack(this.$router, params)
        }else{
          this.myGoBack(this.$router)
        }
      } else {
        this.$emit("closePage")
      }

    },
    // 跳转应收款界面
    goGetArrearsSheet() {
      let query = {
        // branch_id:this.sheet.branch_id,
        // branch_name:this.sheet.branch_name,
        supcust_id: this.sheet.supcust_id,
        sup_name: this.sheet.sup_name,
      };
      if(this.sheet.sheetType == "CG"){
        this.$router.push({ path: "/GetAndPayArrearsSheet?sheetType=FK", query: query });
      }
      else
      this.$router.push({ path: "/GetAndPayArrearsSheet?sheetType=SK", query: query });
    },
    // 删除未审核单据
    btnDeleteSheet_click() {
      //sheet.sheet_id
      var that = this
      Dialog.confirm({
        title: '删除单据',
        message: '请确认是否删除',
        width: '320px'
      }).then(() => {

        var delFunc;
        if (this.sheet.sheetType === "X" || this.sheet.sheetType === "T")
          delFunc = SheetSaleDelete;
        else if (this.sheet.sheetType === "XD" || this.sheet.sheetType === "TD")
          delFunc = SheetSaleOrderDelete;
        else if (this.sheet.sheetType === "CG" || this.sheet.sheetType === "CT")
          delFunc = SheetBuyDelete;
        else if (this.sheet.sheetType === "DH")
          delFunc = SheetOrderItemDelete;
        let params = {
          operKey: this.$store.state.operKey,
          sheet_id: this.sheet.sheet_id,
          sheetType: this.sheet.sheetType
        };
        delFunc(params).then((res) => {
          if (res.result === "OK") {
            Toast.success("删除成功,即将退出该页面");
            setTimeout(function () {
              that.btnOut()
            }, 1000);
          } else {
            Toast.fail("删除失败:" + res.msg);
          }
        });

      })
        .catch(() => {
          Toast("取消删除");
        });
    },
    calcSaleAndReturnAmount() {
      var xAmountTotal = 0
      var tAmountTotal = 0
      this.sheet.sheetRows.forEach(sheetRow => {
        if (!sheetRow.order_sub_id && sheetRow.trade_type !== 'KS') { // 排除定货会商品
          if (sheetRow.sub_amount > 0) {
            xAmountTotal += Number(sheetRow.sub_amount)
          } else if (sheetRow.sub_amount < 0) {
            tAmountTotal += Number(sheetRow.sub_amount)
          }
        }
      })
      this.xAmount = Math.abs(toMoney(xAmountTotal))
      this.tAmount = Math.abs(toMoney(tAmountTotal))
    },
    // 根据id获取已选商品
    getItemsInfo(items_id, callback) {
      var that = this
      let params = {
        items_id: items_id,
        supcust_id: this.sheet.supcust_id,
        branch_id: this.sheet.branch_id
      }
      if (this.sheet.sheetType == 'CG' || this.sheet.sheetType=='CT') {
        BuySheetGetItemsInfo(params).then(res => {
          if (res.result == "OK") {
            that.sheet.sheetRows.forEach(sheetRow => {
              res.items[sheetRow.item_id].units.forEach(item => {
                if (sheetRow.unit_no === item.unit_no) {
                  //console.log("stock_qty of",sheetRow.item_id,": ", item.stock_qty)
                  sheetRow.orig_price = item.recent_orig_price.replace('.00', '')
                  sheetRow.sys_price = item.price.replace('.00', '')
                  sheetRow.real_price = sheetRow.sys_price
                  sheetRow.sub_amount = toMoney(Number(sheetRow.real_price) * Number(sheetRow.quantity))
                }
              })
            })
            if (callback) callback()
          } else {
            Toast.fail("获取商品失败,请重新刷新或者手动删除")
          }
          // orig_price:item.recent_orig_price?item.recent_orig_price.replace('.00',''):definedPrice.replace('.00',''),
          //     sys_price:item.price?item.b_recent_price.replace('.00',''):definedPrice.replace('.00',''),
        }).catch(err => {

        })

      } else {
        SaleSheetGetItemsInfo(params).then(res => {
          if (res.result == "OK") {
            var specialPriceItems = res.spItems
            that.sheet.sheetRows.forEach(sheetRow => {
              res.items[sheetRow.item_id].units.forEach(item => {
                if (sheetRow.unit_no === item.unit_no) {
                  //console.log("stock_qty of",sheetRow.item_id,": ", item.stock_qty)
                  sheetRow.orig_price = item.recent_orig_price.replace('.00', '')
                  sheetRow.sys_price = item.price.replace('.00', '')
                  sheetRow.real_price = sheetRow.sys_price
                  sheetRow.sub_amount = toMoney(Number(sheetRow.real_price) * Number(sheetRow.quantity))
                }
              })
              if (sheetRow.isSpecialPrice !== undefined) sheetRow.isSpecialPrice = undefined
              if (sheetRow.special_price !== undefined) sheetRow.special_price = undefined
              if (sheetRow.s_special_price !== undefined) sheetRow.s_special_price = undefined
              if (sheetRow.m_special_price !== undefined) sheetRow.m_special_price = undefined
              if (sheetRow.b_special_price !== undefined) sheetRow.b_special_price = undefined
              if (specialPriceItems.length > 0) {
                specialPriceItems.forEach(spItem => {
                  if (spItem.item_id === sheetRow.item_id) {
                    sheetRow.left_days = spItem.left_days
                    if (spItem.unit_type === "s") {
                      sheetRow.s_special_price = spItem.special_price
                      if (sheetRow.mfactor) sheetRow.m_special_price = Number(spItem.special_price) * Number(sheetRow.mfactor)
                      if (sheetRow.bfactor) sheetRow.b_special_price = Number(spItem.special_price) * Number(sheetRow.bfactor)
                    } else if (spItem.unit_type === "m") {
                      sheetRow.s_special_price = Number(spItem.special_price) / Number(sheetRow.mfactor)
                      sheetRow.m_special_price = spItem.special_price
                      sheetRow.b_special_price = Number(spItem.special_price) / Number(sheetRow.mfactor) * Number(sheetRow.bfactor)
                    } else {
                      sheetRow.s_special_price = Number(spItem.special_price) / Number(sheetRow.bfactor)
                      if (sheetRow.mfactor) sheetRow.m_special_price = Number(spItem.special_price) / Number(sheetRow.bfactor) * Number(sheetRow.mfactor)
                      sheetRow.b_special_price = spItem.special_price
                    }
                    if (sheetRow.unit_no === sheetRow.s_unit_no) sheetRow.special_price = sheetRow.s_special_price
                    else if (sheetRow.unit_no === sheetRow.m_unit_no) sheetRow.special_price = sheetRow.m_special_price
                    else sheetRow.special_price = sheetRow.b_special_price
                    if (Math.abs(Number(sheetRow.real_price) - Number(sheetRow.special_price)) < 0.01) sheetRow.isSpecialPrice = true
                  }
                })
              }


            })
            if (callback) callback()
          } else {
            Toast.fail("获取商品失败,请重新刷新或者手动删除")
          }
          // orig_price:item.recent_orig_price?item.recent_orig_price.replace('.00',''):definedPrice.replace('.00',''),
          //     sys_price:item.price?item.b_recent_price.replace('.00',''):definedPrice.replace('.00',''),
        }).catch(err => {

        })
      }

    },
    //
    // changeHeight() {
    //   this.publicBox3Height = Number(this.pageHeight) - 100 - Number(this.$refs.salesListFooter.offsetHeight) + 'px'
    // },
    submitSaleSheet() {
      this.popupSubmitPannel = !this.popupSubmitPannel;
      // this.$popup.show('popup1')
      // this.$forceUpdate()
      this.isChangingSheetByCode = true
      this.updatePayAmount();
      setTimeout(() => {
        this.isChangingSheetByCode = false
      }, 100)
    },
    handleAppendBrief() {
      this.showAppendBriefDialog = true
      console.log(this.showAppendBriefDialog)
    },
    confirmAppendBrief() {

      // 接口
      let func = ''
      if (this.sheet.sheetType == 'X') func = AppendBriefSheetX
      else if (this.sheet.sheetType == 'XD') func = AppendBriefSheetXD
      else if (this.sheet.sheetType == 'CG') func = AppendBriefSheetCG
      let params = {
        operKey: this.$store.state.operKey,
        sheetID: this.sheet.sheet_id,
        newBrief: this.appendBrief
      }
      func(params).then(res => {
        if (res.result == "OK") {
          this.sheet.make_brief += (" " + this.appendBrief)
          this.appendBrief = ""
          this.showAppendBriefDialog = false

          Toast.success('追加成功');
        } else {
          Toast.fail('追加失败，请重试');
          this.appendBrief = ""
        }
      }).catch(err => {

      })


    },
    cancelAppendBrief() {
      this.showAppendBriefDialog = false
      this.appendBrief = ""
    },
    clearShoppingCarCache() {
      this.$store.commit('clearPartOfSheetState', {})
      this.$store.commit('shoppingCarObj', {
        sheetType: this.sheet.sheetType,
        clearFlag: true
      })
    },
    handleToJSON(item) {
      console.log('attr_qty', item)
      return typeof item.attr_qty == 'string' ? JSON.parse(item.attr_qty) : item.attr_qty
    },
    handleAttrNameShow(attr) {
      if (attr === '') {
        return ''
      }
      let keyName = 'optName_'
      let tempName = []
      let temp = typeof attr == 'string' ? JSON.parse(attr) : attr
      Object.keys(temp).forEach(item => {
        if (item.substr(0, keyName.length) == keyName) {
          tempName.push(temp[item])
        }
      })
      return tempName.sort().join('_')
    },
    HandleNoStockAttrSplitShow(attr_qty) {
      attr_qty = typeof attr_qty == 'string' ? JSON.parse(attr_qty) : attr_qty
      const attr = attr_qty.length > 0 ? attr_qty[0] : 0
      if (this.noStockAttrSplitShow) {
        if (attr === '') {
          return ''
        }
        let keyName = 'optName_'
        let tempName = []
        let temp = typeof attr == 'string' ? JSON.parse(attr) : attr
        Object.keys(temp).forEach(item => {
          if (item.substr(0, keyName.length) == keyName) {
            tempName.push(temp[item])
          }
        })
        return '(' + tempName.sort().join('_') + ')'
      }
      return ''
    },
    shareWeChat() {
      this.$refs.wechatshare.changeShowShare()
    },
    callShareWeChatPrintPreview() {
      // 调用子组件中的shareWeChatPrintPreview方法
      this.$refs.previewPrintShare.changeShowShare();
    },
    queryScan() {
      const sortType = this.$store.state.queryItemSortType ?? "default"
      if (this.sheet.supcust_id !== 0 && !this.sheet.supcust_id) {
        Toast.fail("请选择客户");
      } else if (!this.sheet.branch_id && this.sheet.sheetType !== 'DH') {
        Toast.fail("请选择仓库");
      } else if (this.sheet.sheetType === 'DH' && !this.sheet.prepay_sub_id) {
        Toast.fail("请选择定货会账户");
      } else {
        this.$store.commit("currentSheet", this.sheet);
        let params = {
          sheetType: this.sheet.sheetType,
          searchStr: this.searchStr,
          branchID: this.sheet.branch_id,
          classID: '',
          showStockOnly: false,
          branch_name: this.sheet.branch_name,
          supcustID: this.sheet.supcust_id,
          sup_name: this.sheet.sup_name,
          querySearchStr: '',
          pageSize: 20,
          startRow: 0,
          noStockAttrSplitShow: this.noStockAttrSplitShow
        }
        let funcGetItemList = AppSheetSaleGetItemList
        if (this.sheet.sheetType === 'CG' || this.sheet.sheetType === 'CT') {
          funcGetItemList = AppSheetBuyGetItemList
        } else if (this.sheet.sheetType === 'DH') {
          funcGetItemList = AppSheetOrderItemGetItemList
        }
        let that = this
        funcGetItemList(params).then(res => {
          if (res.result === "OK") {
            if (res.data.length === 0) {
              Toast('未找到对应商品')
              return
            }
            if (res.data.length === 1) {

              this.$refs.inputCodesRef.blur();   // 失焦 ios测试
              let item = this.handleItemClickToKeyboard(res.data[0])
              if (this.popupAddSheetRow) {
                let el = this.$refs.addSheetRow
                let event = document.createEvent('Events');
                event.initEvent('touchstart', true, true);
                // 是否区分库存
                if (el.distinctStock) {
                  if (this.attrShowFlag) {
                    el.btnOK_clicked(event)
                  } else {
                    this.popupAddSheetRowFalse()
                  }
                } else {
                  el.btnOK_clicked(event)
                  if (this.attrShowFlag) {
                    this.popupAddSheetRowFalse()
                    this.attrShowFlag = false
                  }
                }
              }
              setTimeout(() => {
                let existItem = this.sheet.sheetRows.find(sheetRow => {
                  return sheetRow.item_id === item.item_id
                })
                if (existItem) {
                  // if (this.popupAddSheetRow) { // 打开
                  //   let el = this.$refs.addSheetRow
                  //   let event = document.createEvent('Events');
                  //   event.initEvent('touchstart', true, true);
                  //   // 是否区分库存
                  //   let distinctStock = false
                  //   if(item.mum_attributes) {
                  //     if(!item.mum_attributes.forEach) item.mum_attributes=JSON.parse(item.mum_attributes)
                  //     if(item.mum_attributes.find(attr=>attr.distinctStock)) {
                  //       distinctStock=true
                  //     }
                  //   }
                  //   if (distinctStock) {
                  //     if (this.attrShowFlag) {
                  //       el.btnOK_clicked(event)
                  //     } else {
                  //       this.popupAddSheetRowFalse()
                  //     }
                  //   } else {
                  //     el.btnOK_clicked(event)
                  //     if (this.attrShowFlag) {
                  //       this.popupAddSheetRowFalse()
                  //       this.attrShowFlag = false
                  //     }
                  //   }
                  // }
                  Dialog.confirm({
                    title: '继续录入',
                    message: '该商品已存在，继续录入，数量将累加',
                    width: "320px"
                  })
                    .then(() => {
                      if (this.searchStr.length >= 13) {
                        item.scanBarcode = this.searchStr
                      }
                      this.handleItemClik(item)
                    })

                } else { // 商品不存在
                  if (this.searchStr.length >= 13) {
                    item.scanBarcode = this.searchStr
                  }
                  if(!this.isLoadVirtualProduceDate){
                      item.virtual_produce_date = ""//cw虚拟产期是produce_date,branch_position虚拟产期是virtual_produce_date
                    }
                  this.handleItemClik(item)
                }
              }, 300)
            } else if (res.data.length > 1) {
              that.btnClassView_click();
            }

          }
        }).catch(err => {
          console.log(err)
        })
      }

    },
    handleItemClik(item) {
      var sheetItem = {
        itemUnitsInfo: item,
        supcust_id: this.sheet.supcust_id,
        sup_name: this.sheet.sup_name,
        branch_id: this.sheet.branch_id,
        branch_name: this.sheet.branch_name,
        onload_remarks: this.onloadBrief,
        stock: item.stock,
        sheet: this.sheet,
        selectDistinctStockFlag: ''
      }
      Promise.resolve().then(() => {
        if (!this.attrShowFlag) {
          this.$store.commit("activeSelectItem", item)
          let distinctStock = false
          if (item.mum_attributes) {
            if (!item.mum_attributes.forEach) item.mum_attributes = JSON.parse(item.mum_attributes)
            if (item.mum_attributes.find(attr => attr.distinctStock)) {
              distinctStock = true
            }
          }
          this.$store.commit("distinctStockFlag", distinctStock)
        }
        this.popupAddSheetRow = true
      }).then(() => {
        this.searchStr = ''
        this.$refs.addSheetRow.loadData(sheetItem)
        this.$store.commit("HRItem", sheetItem)
      })




    },
    addSheetRowClose() {
      if (!this.$store.state.btnSaveClick && this.$refs.addSheetRow.trade_type == "HC") {
        Toast('未确认换出商品，将删除已换入商品')
        this.sheet.sheetRows.splice(-1, 2);
      }
    },
    handleHC() {
      this.popupAddSheetRow = true;
      let data = JSON.parse(JSON.stringify(this.$store.state.HRItem));
      data.itemUnitsInfo.bReturn = false;
      data.itemUnitsInfo.unitPriceRows.forEach(item => {
        if (Math.abs(item.quantity) > 0) {
          item.quantity = Math.abs(item.quantity);
          item.sub_amount = Math.abs(item.sub_amount);
          item.remark = "换出";
          item.remarkid = "";
        }
      })
      this.$refs.addSheetRow.loadData(data);
    },
    onSheetRowAdd_Attr(obj) {
      // 属性下一个输入
      let trade_type = this.$store.state.trade_type
      if (trade_type !== "HR") {
        this.$refs.arrtMainToAddSheetRow.nextItemClick(obj)
      }
    },
    popupAddSheetRowFalse() {
      this.popupAddSheetRow = false
      this.multiSelectItemShow = false
      this.$store.commit("clearPartOfSheetState", '')

      this.handleSelectedSheetRows()
    },
    handleAttrShowFlagChange() {
      if (!this.attrShowFlag) {
        this.$refs.arrtMainToAddSheetRow.initData(() => { })
        this.attrShowFlag = !this.attrShowFlag
        this.$store.commit("attrShowFlag", this.attrShowFlag)
      } else {
        this.attrShowFlag = !this.attrShowFlag
        if (!this.multiSelectFlag) {
          this.popupAddSheetRowFalse()
        } else {
          this.handleItemClik(this.$store.state.activeSelectItem)
        }
      }
    },
    onSheetRowAdd_OK() {
      console.log('onSheetRowAdd_OK')
      this.popupAddSheetRow = false
      // 如果是蓝牙设备就进行聚焦输入框
      if (this.bluetoothDeviceInputFlag) {
        this.$refs.inputCodesRef.focus();   // 聚焦
      }
    },
    // 处理行为模板数据
    // 首先是初始化的时候，也就是初次进入
    // handleInitLoadActionsWorkContent() {
    //   // 是新开、复制、保存、审核
    //   if (this.sheet.sheet_id !== '') {
    //     this.sheetAttributeActionKey.forEach(key => {
    //       if (Object.prototype.hasOwnProperty.call(this.sheet.sheet_attribute, key)) {
    //         this.sheet.sheet_attribute[key].forEach(actionItem => {
    //           this[key].push(actionItem)
    //         })
    //       }
    //     })
    //   }
    // },
    handleActionsWorkContent() {
      console.log('handleActionsWorkContent')
      // this.sheet.displayGiveProofs = []
      // 判断是什么类型
      const typeAction = this.displayGiveProofsType.key
      if (this.sheet.sheetRows.length === 0) {
        this.sheet.displayGiveProofs = []
      }
      // 添加
      this.sheet.sheetRows.forEach(sheetRow => {
        if (sheetRow.disp_template_id) {
          const findItem = this.sheet.displayGiveProofs.find(item => item.disp_sheet_id === sheetRow.disp_sheet_id)
          if (!findItem && sheetRow[typeAction + '_actions'] && sheetRow[typeAction + '_actions'].length > 0) {
            const actionItem = {
              disp_sheet_id: sheetRow.disp_sheet_id,
              disp_sheet_no: sheetRow.disp_sheet_no,
              disp_template_name: sheetRow.disp_template_name,
              disp_template_id: sheetRow.disp_template_id,
              action: sheetRow[typeAction + '_actions'],
              work_content: [],
              need_review: sheetRow[typeAction + '_need_review'],
              reviewer: '',
              review_time: '',
              review_comment: '',
              review_refused: '',
            }
            actionItem.action.forEach(workContentItem => {
              let pushObj = {
                action: workContentItem,
                work_content: ''
              }
              if (workContentItem.type === 'photo') {
                pushObj.work_content = {
                  mandatory: [],  // 必选列表
                  mandatoryName: [],
                  optional: [],   // 可选列表
                  optionalName: [],
                }
                let num = pushObj.action.items.length === 0 ? Number(pushObj.action.minNum) : pushObj.action.items.length
                for (let i = 0; i < num; i++) {
                  pushObj.work_content.mandatory[i] = ''
                }
              } else {
                pushObj.work_content = ''
              }
              actionItem.work_content.push(pushObj)
            })
            this.sheet.displayGiveProofs.push(actionItem)
          }
        }
      })
      // 如果sheet没有,就需要删除掉
      if (this.sheet.displayGiveProofs) {
        for (let i = 0; i < this.sheet.displayGiveProofs.length; i++) {
          let displayGiveProof = this.sheet.displayGiveProofs[i]
          const findItem = this.sheet.sheetRows.find(item => item.disp_template_id === displayGiveProof.disp_template_id)
          if (!findItem) {
            this.sheet.displayGiveProofs.splice(i--, 1)
          }
        }
      }

    },
    async handleFromOrderSheet(sheet) {
      let templateIDArr = []
      let dispSheetIDArr = []
      sheet.displayGiveProofs = []
      sheet.sheetRows.forEach(sheetRow => {
        if (sheetRow.disp_template_id) {
          templateIDArr.push(sheetRow.disp_template_id)
          dispSheetIDArr.push(sheetRow.disp_sheet_id)
        }
      })
      let params = {
        operKey: this.$store.state.operKey,
        templateIDs: templateIDArr.join(','),
        dispSheetIDs: dispSheetIDArr.join(','),
      }
      await QueryDisplayTemplateActionsForFdSender(params).then(res => {
        if (res.result === 'OK') {
          const fdSenderTemplateList = res.fdSenderTemplateList
          const fdDisplayAgreementMain = res.fdDisplayAgreementMain
          sheet.sheetRows.forEach(sheetRow => {
            if (sheetRow.disp_template_id) {
              let fendSenderTemplateItem = fdSenderTemplateList.find(item => item.disp_template_id === sheetRow.disp_template_id)
              if (fendSenderTemplateItem) {
                sheetRow.fd_sender_actions = JSON.parse(fendSenderTemplateItem.fd_sender_actions)
                sheetRow.fd_sender_need_review = fendSenderTemplateItem.fd_sender_need_review === 'True'
              }
              let fdDisplayAgreementMainItem = fdDisplayAgreementMain.find(item => item.sheet_id === sheetRow.disp_sheet_id)
              if (fdDisplayAgreementMainItem) {
                sheetRow.disp_sheet_no = fdDisplayAgreementMainItem.sheet_no
              }
            }
          })
        }
      }).catch(() => {
        Toast('获取送货员模板失败，请重试')
      })

    },
    handlePopupEditSheetRowPannelFalse() {
      this.popupEditSheetRowPannel = false
    },
    // handleConfirmWorkContentResul(sheet) {
    //   const typeAction = this.displayGiveProofsType.key
    //   sheet.sheet_attribute[typeAction + '_actions'] = this[typeAction + '_actions']
    //   sheet.sheet_attribute = JSON.stringify(sheet.sheet_attribute)
    //   console.log('handleConfirmWorkContentResul', sheet)
    //   return sheet
    // },

    //   onBackPress(options) {
    //     console.log("不可以左划!!!!!!!!")
    // 	if (options.from === 'navigateBack') {


    //     return false;
    // 	}else{
    // 	this.onreturn();
    //     console.log("不可以左划")
    // 	return true;
    //   }
    //   },
    //   onreturn(){
    // 		uni.redirectTo({
    // 			url:'./index'
    // 		})
    // 	},

    // 处理转单
    async handleFromOtherSheet() {
      let sheetToOtherParams = this.$route.query.sheetToOtherParams
      this.onConfirmBranch({
        branch_id: sheetToOtherParams.branch_id,
        branch_name: sheetToOtherParams.branch_name
      })

      let items_id = sheetToOtherParams.sheetRows.map(item => item.item_id).join(',')
      let params = {
        sheetType: this.sheet.sheetType,
        searchStr: '',
        branchID: this.sheet.branch_id,
        classID: '0',
        showStockOnly: false,
        branch_name: this.sheet.branch_name,
        supcustID:this.$route.query.supcust_id?this.$route.query.supcust_id: '-1',
        sup_name: this.$route.query.sup_name?this.$route.query.sup_name:this.sheet.sup_name,
        querySearchStr: '',
        pageSize: sheetToOtherParams.sheetRows.length,
        startRow: 0,
        fromOtherSheetItemsId: items_id,
        sourcrSheet:sheetToOtherParams.sourceSheet?sheetToOtherParams.sourceSheet:null
      }
      await AppSheetSaleGetItemList(params)
        .then(res => {
          if (res.result === "OK") {
            this.handleOtherSheetRowsToAdd(sheetToOtherParams, res.data)
          } else {
            Toast.fail('转单失败')
          }
        })
        .catch(err => {
          console.log('err', err)
          Toast.fail('获取商品失败')
        })
    },

    handleOtherSheetRowsToAdd(sheetToOtherParams, getItemList) {
      let waitAddSheetRows = getItemList.map(item => {
        return this.handleItemClickToKeyboard(item)
      })
      sheetToOtherParams.sheetRows.forEach(sheetToOtherSheetRow => {
        let findItem = waitAddSheetRows.find(waitAddSheetRow => sheetToOtherSheetRow.item_id === waitAddSheetRow.item_id)
        if (findItem) {
          let unitPriceRows = 0
          // 处理价格/合计
          if (sheetToOtherSheetRow.unit_type === 'b') {
            unitPriceRows = 0
          }
          if (sheetToOtherSheetRow.unit_type === 'm') {
            unitPriceRows = 1
          }
          if (sheetToOtherSheetRow.unit_type === 's') {
            unitPriceRows = findItem.unitPriceRows.length - 1
          }
          findItem = JSON.parse(JSON.stringify(findItem))
          findItem.unitPriceRows[unitPriceRows].quantity = sheetToOtherSheetRow.quantity
          findItem.unitPriceRows[unitPriceRows].sub_amount = toMoney(Number(sheetToOtherSheetRow.quantity) * Number(findItem.unitPriceRows[unitPriceRows].real_price ? findItem.unitPriceRows[unitPriceRows].real_price : 0))
          // 将waitAddSheetRow确认加入sheetRow
          console.log('findItem', findItem)
          this.handleAddSheetRows(findItem)
        }
      })
      Toast.clear()
    },
    handleAddSheetRows(item, trade_type = 'X') {
      let sheetRows = []
      let inoutFlag = 1
      item.trade_type = trade_type
      if (item.unitPriceRows && item.unitPriceRows.length > 0) {
        item.unitPriceRows.map(unitRow => {
          if (unitRow.real_price && !checkInputValidity(unitRow.real_price)) {
            unitRow.real_price = toMoney(unitRow.real_price)
          }
          if (this.appUseVirtualProduceDate) {
            unitRow.virtual_produce_date = item.virtual_produce_date
            unitRow.valid_days = item.valid_days
            if (unitRow.valid_days) {
              var validDayType = window.getSettingValue('validDayType') || 'd'
              if (item.valid_day_type) validDayType = item.valid_day_type
              if (validDayType == 'm') validDayType = '月'; else if (validDayType == 'd') validDayType = '天'; else if (validDayType == 'y') validDayType = '年'
              unitRow.valid_days += validDayType
            }
          }
          if (this.appUseSn) {
            unitRow.sn_code = item.sn_code
          }
          unitRow.trade_type = trade_type
          unitRow.b_lowest_price = item.b_lowest_price ? item.b_lowest_price : ''
          unitRow.m_lowest_price = item.m_lowest_price ? item.m_lowest_price : ''
          unitRow.s_lowest_price = item.s_lowest_price ? item.s_lowest_price : ''
          unitRow.bfactor = (item.bfactor ? item.bfactor : (this.sheet.sheetType === 'DH') ? item.b_unit_factor : '');
          unitRow.mfactor = (item.mfactor ? item.mfactor : (this.sheet.sheetType === 'DH') ? item.m_unit_factor : '');
          if ((unitRow.sub_amount && unitRow.quantity)) {
            unitRow.quantity *= inoutFlag
            unitRow.sub_amount *= inoutFlag
          }
          unitRow.giftAway = false
          unitRow.item_id = item.item_id
          unitRow.item_name = item.item_name
          unitRow.isSelectFlag = item.isSelectFlag
          unitRow.son_mum_item = item?.son_mum_item !== undefined ? item.son_mum_item : item.item_id
          // 一品多口味
          // if (that.attrShowFlag) {
          //   unitRow.attr_qty = []
          //   if (item.attr_qty) {
          //     unitRow.attr_qty.push(JSON.parse(JSON.stringify(item.attr_qty)))
          //     unitRow.attr_qty[0].qty = unitRow.quantity
          //   }
          //   unitRow.son_options_id = item.son_options_id
          //
          //   unitRow.mum_attributes = item.mum_attributes
          //   item?.son_mum_item !== undefined && item.son_mum_item !== "" ? unitRow.son_mum_item = item.son_mum_item : ""
          // }
          if (item.scanBarcode) unitRow.scanBarcode = item.scanBarcode
          sheetRows.push(unitRow)
        })
      }
      // 库存问题
      // if ((this.sheet.sheetType == 'X' || this.sheet.sheetType == 'XD') && !isReturn && this.trade_type !== 'KS' && this.trade_type !== 'J' && this.trade_type !== 'HR' && this.trade_type !== 'HC') {
      //   var allowNegativeStock = window.getRightValue('delicacy.allowNegativeStock.value')
      //   var branchAllowNegative = window.branchAllowNegative(this.sheet.branch_name, this.sheet.sheetType)
      //   if (allowNegativeStock === 'false' || !branchAllowNegative) {
      //     if (this.smallUnitStock < -0.001) {
      //       Toast.fail('库存不足')
      //       return
      //     }
      //   }
      // }
      let addInfoSheetRow = sheetRows.filter(sheetRow => {
        return sheetRow.quantity || sheetRow.isSelectFlag
      })
      addInfoSheetRow = JSON.parse(JSON.stringify(addInfoSheetRow))
      addInfoSheetRow.forEach((row, rowIndex) => {
        if (row.isSelectFlag && Number(row.quantity) === 0) {
          row.sub_amount = 0
          row.quantity = 0
        }
        if ((row.quantity.toString().trim() === "" || isNaN(row.quantity))) return
        row.classId = item.class_id
        if (!row.real_price) {
          row.real_price = 0
          row.sub_amount = 0
        }
        if (!row.orig_price) {
          row.orig_price = row.real_price
        }
        var pd = row.virtual_produce_date
        if (pd && pd.length == 6) {
          pd = '20' + pd.substr(0, 2) + '-' + pd.substr(2, 2) + '-' + pd.substr(4, 2)
          row.virtual_produce_date = pd
        }

        if (item.attr_qty) {
          row.b_barcode = item.attr_qty.bBarcode
          row.m_barcode = item.attr_qty.mBarcode ? item.attr_qty.mBarcode : ''
          row.s_barcode = item.attr_qty.sBarcode
        }
        else {
          row.b_barcode = item.b_barcode
          row.m_barcode = item.m_barcode
          row.s_barcode = item.s_barcode
        }
        row.b_unit_no = item.bunit
        row.b_unit_factor = item.bfactor
        row.m_unit_no = item.munit
        row.m_unit_factor = item.mfactor
        row.s_unit_no = item.sunit
        row.order_sub_id = item.order_sub_id
        row.order_flow_id = item.order_flow_id
        row.order_sub_name = item.order_sub_name
        row.trade_type = item.trade_type
        if (row.disp_sheet_id) {
          row.trade_type = 'CL'
        }
        row.virtual_produce_date = item.virtual_produce_date
        row.sn_code = item.sn_code
        row.originalSelectItemInfo = item.originalSelectItemInfo
        row.item_images = item.item_images
        row.showImages = item.showImages
        this.sheet.sheetRows.push(row)
      });
    },

    calcAssistQty(sheetRow) {
      return globalVars.getUnitQtyFromSheetRow(sheetRow)
      // console.log(sheetRow)
      // let bf = parseFloat(sheetRow.b_unit_factor);
      // let mf = parseFloat(sheetRow.m_unit_factor);
      // let sf = 1.0;
      // let assistQty = ''
      // let qtyToAssist = Number(sheetRow.quantity)
      // if (bf > 0) {
      //   let bAssistQty = Math.floor(qtyToAssist % bf)
      //   assistQty += bAssistQty + sheetRow.b_unit_no
      //   qtyToAssist = Number(toMoney(qtyToAssist / bf, 2))
      // }
      // if (mf > 0) {
      //   let mAssistQty = Math.floor(qtyToAssist % mf)
      //   assistQty += mAssistQty + sheetRow.m_unit_no
      //   qtyToAssist = Number(toMoney(qtyToAssist / mf, 2))
      // }
      // if (qtyToAssist) {
      //   assistQty += qtyToAssist + sheetRow.s_unit_no
      // }
      // return assistQty
    },
    handleAutoBtnOkClick() {
      if(this.attrShowFlag) {
        this.$refs.addSheetRow.btnOK_clicked(null);
      }
    },
  }
}


</script>

<style lang="less" scoped>

/deep/ .van-overlay.van-image-preview__overlay { //打印预览的时候底部透明度不要那么高
  background-color: rgba(0, 0, 0, 0.8) !important;
}

/deep/ .mx-calendar-content .cell:hover {
  background-color: #fff;
}

/deep/ .mx-calendar-content .cell.active {
  border-radius: 100px;
  color: #fff;
  background-color: rgb(255, 153, 153);
}

/deep/ .mx-table-date .today {
  color: rgb(255, 153, 153);
}

/deep/ .mx-datepicker {
  width: 100%;
  height: 100%;
  box-sizing: border-box;

  .mx-input-wrapper {
    height: 40px;
    box-sizing: border-box;
    display: flex;

    .mx-input {
      outline: none;
      border: none;
      height: 100%;
      box-shadow: none;
      border-radius: 0;
    }
  }
  .mx-datepicker-main{
    border: none;
    border-top:1px solid #e8e8e8;
  }
  .mx-datepicker-content,.mx-calendar,.mx-datepicker-body,.mx-calendar {
    height: 100%;
    width: 100%;
    border: none;
    box-sizing: border-box;
  }

  .mx-calendar-header {
    height: 1.5rem;
    line-height: 1.5rem;
  }

  .mx-calendar-content {
    height:calc(100% - 1.5rem);
  }
  button {
    min-width: 0;
  }

  .mx-btn-icon-left,
  .mx-btn-icon-double-left,
  .mx-btn-icon-right,
  .mx-btn-icon-double-right {
    padding: 0 0.3rem;
  }
}
// 在线支付UI相关
.payb-status-text {
  color: green;
  font-weight: 600;
  padding-left: 15px;
  margin-right: 15px;
}
.close-circle {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #f2f2f2; /* 圈圈背景色 */
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); /* 给圈圈加阴影，增加立体感 */
}

.close-icon {
  font-size: 16px;
  color: #555; /* 图标颜色 */
  font-weight: bold;
  text-shadow: 1px 1px 2 #555, -1px -1px 2 #555; /* 模拟图标的粗体效果 */
}

//审核界面删除分享按钮样式
.approved_reded {
  position: absolute;
  left: 25px;
  top: 165px;
  margin-top: -40px;
  z-index: 100;
}

.approved {
  width: 105px;
  height: 60px;
  // top: 110px;
  // left: 50px;
}

.reded {
  width: 105px;
  height: 60px;
  // top: 110px;
  // left: 50px;
}

.btn_CRUD_type {
  border-radius: 12px;
  height: 40px;
}

.radio-tmp-position {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  height: 210px;
  overflow-y: auto;
}

.setHeight {
  height: 60px;
}

.radio-tmp-style {
  display: inline-block;
  position: relative;
  left: 0;
  margin: 5px 0;
  width: 50%;
  text-align: left;
}

.radio-tmp-name {
  color: #9999b8;
}

// 模板radio样式
.radio-tmp-type {
  width: 20px;
  height: 20px;
  appearance: none;
  position: relative;
}

.radio-tmp-type:before {
  content: "";
  width: 20px;
  height: 20px;
  border: 1px solid #8d8d8d;
  display: inline-block;
  border-radius: 50%;
  vertical-align: middle;
}

.radio-tmp-type:checked:before {
  content: "";
  width: 20px;
  height: 20px;
  border: 1px solid #8d8d8d;
  background: #8d8d8d;
  display: inline-block;
  border-radius: 50%;
  vertical-align: middle;
}

.radio-tmp-type:checked:after {
  content: "";
  width: 10px;
  height: 5px;
  border: 0.065rem solid white;
  border-top: transparent;
  border-right: transparent;
  text-align: center;
  display: block;
  position: absolute;
  top: 6px;
  left: 5px;
  vertical-align: middle;
  transform: rotate(-45deg);
}

/deep/ .van-overlay {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4) !important;
}

/deep/ .van-cell__title.van-field__label {
  width: 40px;
}

.black {
  fill: currentColor;
  // color: #757575;
  color: #8d8d8d;
}

@flex_w: {
  display: flex;
  flex-wrap: wrap;
};

@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
};

@flex_a_bw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
};

@posAblot: {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
};

// @scorllHeight: this.$refs.salesListFooter.offsetHeight + 'px';
.van-divider {
  margin-bottom: 90px;
}

.van-divider--hairline {
  margin-bottom: 90px;
}

/deep/.inputZf {
  .van-field__control {
    text-align: right;
  }
}

/deep/.van_search.van-cell.van-field {
  padding: 0px;
  border: 0px;
}

/deep/input.van-field__control.van-field__control--right {
  padding: 0px 10px 4px 0px;
}

/deep/.van-toast {
  z-index: 99999 !important;
}

/deep/.van-swipe-cell__right {
  .van-button {
    height: 100%;
  }
}

/deep/.price-col {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/deep/.price-col > div {
}

/deep/.price-col > div > del,
/deep/.price-col > div > span {
  margin-right: 2px;
}

.sys_price {
  color: #f88;
  font-size: 12px;
}

.color_fff {
  border-radius: 12px;
  border: 1px solid #ccc;
  background-color: #fff;
}

.color_ffcccc {
  border-radius: 12px;
}

.style_checkButton {
  border-radius: 12px;
  // width: 80px;
  height: 45px;
}
.button-row {
  display: flex;
  justify-content: center; /* 中间对齐 */
  align-items: center;
  position: relative; /* 保证按钮内部定位 */
  margin-top: 20px; /* 和内容保持一定距离 */
}

.btn-new {
  width: 150px; /* 固定宽度 */
  height: 40px;
  border-radius: 12px;
}



// 重新修改样式
.pages {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .public_box3 {
    width: 100%;
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
  }

  .total_money {
    //@posAblot();
    .sales_list_footer {
      border-top: 1px solid #f2f2f2;
      border-bottom: 0px solid #f2f2f2;
      min-height: 30px;
      line-height: 30px;
      font-size: 14px;
      text-align: left;
      background: #fff;
      box-shadow: 0 0px 0px #f2f2f2;
      padding-top: 6px;

      div {
        padding: 0 5px;
      }

      .sales_list_span {
        font-family: numFont;
        margin-right: 15px;
      }
    }

    .sales_footer {
      height: 55px;
      background: #ffffff;
      border-top: 0px solid #f2f2f2;

      @flex_a_bw();

      .footer_input {
        @flex_a_j();
        flex: 6;
        // width: 250px;
        height: 50px;
        text-align: left;

        input[type="text"] {
          width: 100%;
          font-size: 14px;
          border-style: none;
          border-bottom: 1px solid #eee;
          background-color: #eee;
          height: 35px;
        }
      }

      .footer_iconBt {
        @flex_a_j();
        flex: 1;
        background-color: transparent;
        border: none;
        // margin-left: 20px;
        width: 50px;
        height: 50px;
      }
    }
  }
}

.amount-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-left: 16px;
  margin-top: 10px;
  width: 100%;
}

.discount-btn {
  position: absolute;
  right: 30px;
  margin-top: 10px;
  height: 30px;
  width: 30px;
  border-radius: 6px;
  background-color: #ffcccc;
  line-height: 30px;
  color: #222;
  margin: 10px;
}

.sales_box_list_big {
  height: calc(100% - 40px - 37px - 30px);
}

.sales_box_list {
  height: calc(100% - 40px - 37px);
}

.van_popup {
  overflow: hidden;
}

.van_search_style {
  display: contents;
  font-size: 15px;
}

.van_search_btns {
  margin-left: 10px;
}

.btn-delete {
  position: absolute;
  right: 10px;
  width: 50px;
  line-height: 30px;
  border: 1px solid #f2f2f2; /* 添加黑色边框 */
  border-radius: 10px; /* 设置圆角 */
  text-align: center;
}

.btn-delete:active {
  background-color: #ffcccc;
}

.delSheetRow {
  vertical-align: top;

  button {
    height: 100%;
    vertical-align: top;
  }
}

.appendix-container {
  display: flex;
  flex-direction: column;
  margin-bottom: 10px;
}

.appendix_tip {
  font-size: 12px;
  color: #666;
}

.other_operate_content_left {
  display: flex;
  // margin: 30px 0;
  width: 70%;
  left: 0px;
  flex-direction: row;
  overflow-x: scroll;
  position: absolute;
}

.other_operate_content_left::-webkit-scrollbar {
  display: none;
}

.other_operate_content_right {
  width: 80px;
  position: fixed;

  right: 0px;
}

.appendixphoto-container {
  margin-left: -10px;
  border-radius: 50%;
  width: 60px;
  position: relative;
  margin-left: 10px;
  height: 60px;
  position: relative;

  .remove-icon {
    position: absolute;
    top: -7px;
    margin-left: 51px;
    color: #f31010;
    font-size: 16px;
    float: right;
  }
}

.clientInfo {
  margin-top: 10px;
  display: flex;
  // font-family: font;
  font-size: 14px;
  color: #f56c6c;
  padding: 8px 5px;

  span {
    // font-family: font;
    font-size: 14px;
  }

  .prepayInfoSpan {
    text-align: left;
    // font-family: font;
    font-size: 14px;
  }

  .arrearsInfoSpan {
    border-bottom: 0.5px solid rgb(248, 198, 198);
    margin-right: 20px;
    // font-family: font;
    font-size: 14px;
  }

  .acct-cust-name {
    margin-right: 20px;
  }
}

.sales_more {
  background-color: #fff;
  position: absolute;
  top: 0px;
  width: 100%;
  height: 100%;
  left: 0px;

  .print-template-wrapper {
    height: 250px;
  }

  .select-printer {
    display: flex;
    width: 100%;
    min-height: 45px;

    /deep/.van-cell-group.van-cell-group--inset.van-hairline--top-bottom {
      margin: 0px;
    }
  }

  /deep/.van-checkbox-group {
    font-size: 16px;
  }

  .print-count {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  .print-btns {
    height: 45px;
    border-radius: 12px;
    width: calc(45% - 5px);
  }

  .other_payway {
    // margin: 15px 0;
    .payway-minHeight {
      min-height: 40px;
    }

    .payway {
      height: 30px;

      font-size: 15px;
      @flex_a_j();
      margin: 5px;

      .van-col {
        height: inherit;
        @flex_a_j();
        color: #000000;
      }

      input {
        width: 90px;
        height: 100%;
        border: none;
        outline: none;
        border-bottom: 0.5px solid #eee;
        text-align: center;
      }

      .payway_add {
        font-size: 20px;
        color: #ccc;
        margin-left: 10px;
        margin-top: 10px;
      }

      .arrow {
        line-height: 17px;
      }
    }

    .payway::after {
      position: absolute;
      content: "";
      border-bottom: 1px solid #444;
    }
  }

  /deep/.van-field {
    height: calc(100% - 46px);
    color: #000000;
  }

  /deep/.van-radio-group {
    margin-top: 5px;
  }

  /deep/.van-radio {
    margin: 0 0 10px 0;
  }

  /deep/.van-divider {
    margin: 10px 0 3px;
    color: #ddd;
  }

  h4 {
    font-size: 16px;
    text-align: left;
    height: 35px;
    line-height: 35px;
    padding-left: 10px;
    color: #333333;
    font-weight: normal;
  }
}
.red {
  color: red;
}
.title {
  font-size: 16px;
  background: #ffffff;
  border-bottom: 1px solid #f2f2f2;
  // padding: 5px 15px;

  svg {
    width: 24px;
    height: 18px;
    margin-top: 1px;
  }

  position:relative .van-row {
    height: 100%;

    .van-col {
      height: 100%;
      @flex_a_j();
    }

    .van-col:first-child {
      justify-content: flex-start;
    }

    .van-col:last-child {
      justify-content: flex-end;
    }
  }
}

.sales_list {
  height: 100%;

  .sales_list_boxs {
    height: calc(100% - 40px);
    // overflow-y: auto;
    // overflow-x: hidden;
  }

  .sales_list_other {
    height: calc(100% - 40px);
    overflow-y: auto;
    overflow-x: hidden;
  }
}

.sales_ul {
  height: auto;
  overflow: hidden;
  color: #000;
  background: #ffffff;
  padding-left: 8px;

  // .sales_img {
  //   width: 50px;
  //   height: 50px;
  // }

  // .sales_img_wrapper {
  //   width: 63px;
  //   height: 63px;
  // }

  .sales_ul_li {
    width: 100%;
    min-height: 30px;
    height: auto;
    font-size: 15px;
    // padding: 10px 15px 10px 5px;
    box-sizing: border-box;
    border-style: none;
    border-bottom-style: solid;
    border-width: 1px;
    border-color: #f2f2f2;

    .van-row {
      height: auto;

      .van-col {
        height: 25px;
        line-height: 25px;
        @flex_a_j();
      }

      .van-col:first-child {
        justify-content: flex-start;
        text-align: left;
      }

      .van-col:last-child {
        justify-content: flex-end;
      }
    }
  }

  .total_msg {
    margin-top: 15px;
    //min-height: 80px;
    text-align: left;
    font-weight: normal;
    padding: 5px;
    color: #999;

    :first-child {
      margin-right: 5px;
    }

    .sheet-mark-brief-wrapper {
      margin-top: 15px;
      word-wrap: break-word;
      word-break: break-all;
    }
  }
}

.lowItem {
  height: auto;
  overflow: hidden;
  padding: 10px;

  h4 {
    height: 40px;
    font-size: 16px;
    font-weight: 500;
    color: #333333;
    @flex_a_j();
  }

  .lowItem_ul {
    height: auto;
    overflow: hidden;
    margin-bottom: 10px;

    li {
      height: auto;
      overflow: hidden;
      padding: 10px;
      padding-right: 80px;
      font-size: 14px;
      @flex_a_bw();
      border-bottom: 1px solid #f2f2f2;
    }

    li:last-child {
      border-bottom: none;
    }
  }
}

.other_operate {
  // display: flex;
  width: 100%;
  height: auto;

  .other_operate_content {
    vertical-align: top;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    height: 40px;
    width: 100%;
    @flex_a_j();

    .small-btn {
      margin-bottom: 20px;
      padding: 0;
      background-color: #fff;
      color: #5577bb;
      height: 30px;
      border: none;
    }

    .small-btn:disabled {
      color: #ddd;
    }

    button {
      padding: 0;
      width: 50px;
      height: 30px;
      vertical-align: top;
      margin: 0 15px;
      flex: 1;
      // height: 100%;
      // color: #eee;
    }
  }

  // .other_operate_content::after{
  //   content: "";
  //   flex: auto;
  //   width: 31px;
  // }

  // van-button {
  //   width: 100px;
  // }
}

.loading-msg {
  border-radius: 20px;
  width: 200px;
  height: 80px;
  background: #555;
  color: #fff;
  position: fixed;
  top: calc(50vh - 40px);
  left: calc(50vw - 100px);
  z-index: 99999999;
  display: flex;
  justify-content: center;
  align-items: center;

  /deep/.van-loading {
    font-size: 24px;
  }
}

.custom_h5 {
  height: 46px;
  line-height: 46px;
  font-size: 16px;
  color: steelblue;
  background: #f2f2f2;
  position: relative;

  .icon_h5 {
    position: absolute;
    height: 46px;
    width: 46px;
    right: 0;
    top: 0;
    display: block;
    @flex_a_j();
    font-size: 20px;
  }
}

.class_add_goods {
  height: calc(100% - 46px);
  background: #f2f2f2;
  overflow: auto;
}

.class_add_goods_m {
  height: 100%;
}

.submitSaleSheet {
  color: #000;
  font-size: 15px;
}

.van-radio-group {
  font-size: 12px;
  margin-left: 100px;
  margin-top: 20px;
}

.van-radio {
  margin-top: 10px;
}

.selectSheetRowStyle {
  border-width: 1px;
  border-style: solid;
  min-width: 20px;
  min-height: 20px;
  text-align: center;
  color: rgba(245, 108, 108, 0.8);
  font-size: 14px;
  line-height: 20px;
  font-weight: 500;
  border-radius: 6px;
  margin-right: 2px;
  margin-top: 2px;
}

//head
@flex_w: {
  display: flex;
  flex-wrap: wrap;
};

@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
};

@flex_a_bw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
};

.photo {
  margin-right: 6px;
  border-radius: 6px;
  width: 60px;
  height: 60px;
}

.iconfont {
  //margin-top: 20px;
  padding: 10px;
  width: 40px;
  height: 40px;
  font-size: 0.53333rem;
  color: #ccc;
  // border: 1px solid #ccc;
}

.public_query {
  //padding: 0 10px;
  .public_query_title {
    background: #ffffff;
    margin-bottom: 1px;

    .sheet_info {
      color: #888;
      // padding-top: 5px;
      // padding-left: 25px;
    }
  }

  .public_query_title_t {
    padding: 0 10px 5px;
    height: 25px;
    // line-height: 25px;
    font-size: 15px;
    color: #000000;
    @flex_a_bw();

    i {
      height: 18px;
      border: 1px solid red;
      display: inline-block;
      font-size: 12px;
      font-style: normal;
      line-height: 20px;
      padding: 0 5px;
      border-radius: 4px;
      margin-left: 10px;
    }
  }

  .public_query_titleSrc {
    padding: 10px 15px 3px 15px;

    display: flex;
    flex-direction: column;
    background: #ffffff;

    .public_query_wrapper {
      @flex_a_bw();

      // margin-top: 5px;
      .public_query_titleSrc_item {
        width: 50%;
        height: 100%;
        overflow: hidden;
        position: relative;
        vertical-align: top;
        border: none;
        padding: 10px 0 3px 0;

        div {
          height: 100%;
          width: calc(100% - 40px);
          padding: 0 30px 0 10px;
          border: none;
          font-size: 15px;
          line-height: 35px;
          color: #333333;
          text-align: left;
        }

        input,
        .selectOne {
          height: 100%;
          padding: 0 10px 4px 0px;
          border: none;
          font-size: 15px;
          line-height: 35px;
          color: #333333;
          vertical-align: top;
          border-bottom: 1px solid #eee;
          text-align: right;
        }

        .van-icon {
          position: absolute;
          left: 5px;
          top: 0;
          bottom: -10px;
          width: 30px;
          text-align: center;
          font-size: 22px;
          @flex_a_j();
          color: #aaa;
          background-color: #ffffff;
        }

        .query_icon {
          width: 22px;
          height: 22px;
          margin-left: 10px;
        }
      }
    }
  }

  .public_list_title {
    height: 40px;
    @flex_a_bw();
    margin-top: 5px;
    padding: 0 5px;

    div {
      height: 40px;
      line-height: 40px;
      font-size: 15px;
      text-align: center;
      width: calc(25% - 10px);
      padding: 0 5px;
      font-weight: 500;
      color: #333333;
    }

    div:first-child {
      width: calc(50% - 10px);
      text-align: left;
    }

    div:last-child {
      width: calc(25% - 10px);
      text-align: right;
    }
  }
}

.item_attr_qty_wrapper {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  align-items: center;

  .item_attr_title {
    color: #999;
    font-size: 14px;
    margin-right: 10px;
  }

  .item_attr_qty_content {
    font-size: 10px !important;
    color: black;
    border: 1px solid rgb(238, 42, 42);
    padding: 1px 2px;
    margin-right: 3px;
    border-radius: 5px;
    margin-bottom: 2px;
  }
}

.addSheetRowOne {
  background-color: transparent !important;
  height: 100%;
  width: 100%;
  overflow: hidden;

  .add_goods_wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: flex-end;

    .goods_attr_wrapper {
      background-color: transparent;
      display: flex;
      width: 100%;
      flex: 1;
      flex-direction: column;
      overflow-y: hidden;
      .goods_attr {
        background-color: #fff;
        height: 100%;
      }

      .layout_content {
        width: 100%;
        flex: 1;
      }
    }

    .goods_add_wrapper {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      justify-content: flex-end;

      .class_add_goods {
        width: 100%;
        height: 100%;
        background-color: #fff;
        //padding-top: 5px;
        padding: 5px 0 0;
      }
    }
  }
}

.myslide-right-enter-active,
.myslide-right-leave-active,
.myslide-left-enter-active,
.myslide-left-leave-active {
  transition: all 0.4s ease;
}

.myslide-right-enter {
  transform: translateX(-100%);
}

.myslide-right-leave-active {
  transform: translateX(0%);
}

.myslide-left-enter {
  transform: translateX(100%);
}

.myslide-left-leave-active {
  transform: translateX(100%);
}

.other-info {
  margin-right: 15px;
  color: #bbb;
  height: 10px;
  font-size: 13px;
  margin-bottom: 0px;
  text-align: left;
  height: auto;
}
.profit-info {
  color: #999;
  line-height: 1.5;
}
.content-code-wrapper {
  width: 100%;
  min-height: 400px;
  background-color: #eee;
  border-right: 1px solid #eee;
  display: flex;
  box-shadow: 3px 3px 17px #333333;
  flex-direction: column;
  box-sizing: border-box;

  .content-code-title {
    width: 100%;
    height: 60px;
    box-sizing: border-box;
    padding: 5px 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-bottom: 1px solid #eeeeee;
  }

  .content-code-qr-code {
    height: 300px;
    background-color: #fff;
    border-bottom: 1px solid #eee;
    box-sizing: border-box;
    padding: 10px;
    display: flex;
    flex-direction: column;

    img {
      width: 100%;
      height: 100%;
    }

    .content-wrapper {
      width: 100%;
      min-height: 100px;
      display: flex;
      flex-direction: column;
      margin-bottom: 40px;

      .content-item {
        width: 100%;
      }

      .content-illustrate {
        margin-top: 10px;
      }
    }
  }

  .content-code-btn {
    display: flex;
    height: 60px;
    background-color: #fff;
    justify-content: center;
    padding-top: 10px;

    button {
      width: 80%;
      background-color: #fde3e4;
      border-radius: 10px;
    }
  }
}

/deep/ .van-checkbox__icon--checked .van-icon {
  background-color: #8d8d8d !important;
  border-color: #8d8d8d;
}

.hide-show-div-enter-active {
  transition: opacity 0.5s;
}

.hide-show-div-enter {
  opacity: 0;
}

.hide-show-div-leave-active {
  transition: opacity 0.5s;
}

.hide-show-div-leave-to {
  opacity: 0;
}

.van-cell::after {
  border-bottom: 0.5px solid #ddd;
}

.payway-balance {
  font-size: 10px;
  text-align: left !important;
  color: #888;
  margin-left: 23px;
  margin-top: -10px;
}

.helper-class-slick-item {
  width: 100vw;
  background-color: #f2f2f2;
  .sales_ul_li {
    border-bottom-style: solid;
    border-width: 1px;
    border-color: #f2f2f2;
  }
}

.progress-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
  min-width: 280px;
  min-height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.van-progress {
  width: 100%;
}


</style>
<style>
.branch-selection-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 20px;
  background-color: white;
  border: 1px solid #ddd;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
</style>
