<template>
  <div class="visit-photo-wrapper">
    <div class="photo-title-tip">
      <div class="photo-title-tip-left">
        <div class="photo-title"> {{ photoAction.name }} </div>
        <div class="photo-tip">
          {{ photoAction.tip }}
        </div>
      </div>
      <div class="photo-title-tip-right">
        <van-popover
            v-model="showPopover"
            trigger="click"
            placement="left"
        >
          <div class="action-wrapper">
            <div class="action-item" @click="showPopover = false" v-for="(item, index) in tipsActions" :key="'action_' + index">
              {{ item.text }}
            </div>
          </div>
          <template #reference>
            <van-icon name="info-o" />
          </template>
        </van-popover>

      </div>
    </div>
    <div class="photo-content">
      <div v-for="(item, index) in photoAction.items" :key="'photo-item-' + item.id" class="photo-content-item">
        <div class="photo-item-img">
          <div v-if="photoList.mandatory[index] === ''" class="photo-add" @click="handlePhotoClick(true, index)">
            <van-icon name="plus" />
          </div>
          <div v-else class="photo-img">
            <img :src="photoList.mandatory[index]" alt="" @click="handleShowPreView(photoList.mandatory, index)"/>
            <div class="photo-img-close" v-if="!isRecordVisit" @click="handlePhotoListSplice(photoList.mandatory, index, true)">
              <van-icon name="close" />
            </div>
          </div>
        </div>
        <div class="photo-item-title">
          {{ item.title }}
        </div>
      </div>
      <template v-if="calcPhotoOptionalNum > 0">
        <div v-for="(item, index) in photoList.optional" :key="'photo-optional-' + item" class="photo-content-item">
          <div class="photo-item-img">
            <div class="photo-img">
              <img :src="item" alt="" @click="handleShowPreView(photoList.optional, index)"/>
              <div class="photo-img-close"  v-if="!isRecordVisit" @click="handlePhotoListSplice(photoList.optional, index, false)">
                <van-icon name="close" />
              </div>
            </div>
          </div>
        </div>
      </template>
      <template v-if="calcOptionalNum - calcPhotoOptionalNum > 0">
        <div class="photo-content-item photo-opt">
          <div class="photo-item-img">
            <div class="photo-add photo-add-opt"  @click="handlePhotoClick(false, -1)">
              <van-icon name="plus" />
            </div>
          </div>
          <div class="photo-item-title">
            可选
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import {Icon, ImagePreview, Popover, Toast} from "vant";
import globalVars from "../../../static/global-vars";
import {SaveSingleImage} from "../../../api/api";
import ImageUtil from '../../service/Image';

export default {
  name: "VisitTemplatePhoto",
  components: {
    "van-icon": Icon,
    "van-popover": Popover
  },
  computed: {
    calcOptionalNum() {
      return this.photoAction.maxNum - this.photoAction.minNum
    },
    calcPhotoOptionalNum() {
      const tempArr = this.photoList.optional.filter(item => {
        return item !== ""
      })
      return tempArr.length
    },
    isRecordVisit() {
      console.log('this.isRecordVisit', this.workContent)
      return JSON.stringify(this.workContent) !== '{}' && this.workContent !== undefined
    }
  },
  props: {
    visit_info: {
      type: Object,
      default: () => {}
    },
    photoAction: {
      type: Object,
      default: function () {
        return {
          id: '',
          tip: '',
          name: '',
          type: '',
          items: [],
          maxNum: 0,
          minNum: 0,
        }
      }
    },
    workContent: {
      type: Object,
      default: () => {}
    },
    TakePhotoCallBack: {
      type: Function,
      default() {
        return null
      }
    }
  },
  data() {
    return {
      showPopover: false,
      tipsActions: [],
      photoList: {
        mandatory: [],  // 必选列表
        mandatoryName: [],
        optional: [],   // 可选列表
        optionalName: [],
      }
    }
  },
  mounted() {
    this.handleTipsActions()
    this.handlePhotoList()
    this.handleWorkContent()
  },
  methods: {
    // 图片提示
    handleTipsActions() {
      if(this.photoAction.items.length < this.photoAction.minNum) {
        const length = this.photoAction.minNum - this.photoAction.items.length
        for (let i = 0 ; i < length ; i ++) {
          this.photoAction.items.push({
            "id": this.photoAction.items.length > 0 ? this.photoAction.items[this.photoAction.items.length - 1] .id + 1 : 1,
            "tip": "暂无要求",
            "title": "必选"
          })
        }
      }
      this.photoAction.items.forEach(item => {
        this.tipsActions.push({text : item.title + "： " +item.tip})
      })

    },
    // 初始化列表
    handlePhotoList() {
      for (let i = 0; i < this.photoAction.items.length; i++) {
        this.photoList.mandatory[i] = ''
      }
    },
    // 拍照
    // mandatoryOrOptionalFlag true 必选， false可选
    handlePhotoClick(mandatoryOrOptionalFlag, index) {
      let that = this;
      let faceImageUrl = "";
      this.onTakePhotoAndCallBackImageUrl(async (res) => {
        faceImageUrl = res;
        const waterMakerCompressImage = await that.compressImageWithWaterMark(faceImageUrl);
        SaveSingleImage({ operKey: this.$store.state.operKey, imageBase64: waterMakerCompressImage }).then((res) => {
          if (res?.result === 'OK' && res?.data) {
            const image = res.data
            Toast.success({ message: '上传成功', duration: 500 })
            resolveImage(that, image)
          } else {
            // const msg = res.msg ?? '图片上传失败'
            // Toast.fail(msg)
            console.warn('上传失败', res)
            resolveImage(that, waterMakerCompressImage)
          }
        }).catch((err) => {
          console.error('上传失败(通信错误)', err)
          resolveImage(that, waterMakerCompressImage)
        })
      });
      function resolveImage(_this, image) {
        if(mandatoryOrOptionalFlag) {
          _this.$set(_this.photoList.mandatory, index, image)
          _this.$set(_this.photoList.mandatoryName, index, 'uploads/' + _this.nanoid() + '.' + _this.photoList.mandatory[index].split(';')[0].split('/')[1])
        } else {
          _this.photoList.optional.push(image)
          _this.photoList.optionalName.push('templateVisit/' + _this.nanoid() + '.' + _this.photoList.optional[_this.photoList.optional.length - 1].split(';')[0].split('/')[1])
        }
        _this.$forceUpdate()
        _this.TakePhotoCallBack()
      }
    },
    //时间，公司名称，地址
    //公共拍照函数
    //param list
    onTakePhotoAndCallBackImageUrl(callback) {
      if (this.visit_info.visit_id) {
        let CameraOptions = {
          // eslint-disable-next-line no-undef
          destinationType: Camera.DestinationType.DATA_URL, //返回FILE_URI类型
          // eslint-disable-next-line no-undef
          sourceType: Camera.PictureSourceType.CAMERA, //返回FILE_URI类型
        };
        navigator.camera.getPicture(cameraSuccess, cameraError, CameraOptions);
        // eslint-disable-next-line no-inner-declarations
        function cameraSuccess(data) {
          // //原图
          let imgUrl = "data:image/jpeg;base64," + data;
          callback(imgUrl);
        }
        // eslint-disable-next-line no-inner-declarations
        function cameraError() {}
      } else {
        Toast.fail("请先签到");
      }
    },
    async compressImageWithWaterMark(origenBaseImg) {
      console.log('origenBaseImg', origenBaseImg)
      var distance = await this.getCurPositionToSupcustDistance(
          this.visit_info.supcust_lat,
          this.visit_info.supcust_lng
      );
      const operName = this.$store.state.operInfo.oper_name;
      let that_ = this;
      const compressImage = await ImageUtil.compress(origenBaseImg, 720);
      return new Promise((resolve, reject) => {
        var newImage = new Image();
        newImage.src = compressImage;
        newImage.setAttribute("crossOrigin", "Anonymous"); //url为外域时需要
        var imgWidth, imgHeight;
        newImage.onload = function () {
          imgWidth = this.width;
          imgHeight = this.height;
          var waterCanvas = document.createElement("canvas");
          var ctx = waterCanvas.getContext("2d");
          //Canvas实现水印
          waterCanvas.width = imgWidth;
          waterCanvas.height = imgHeight;
          ctx.clearRect(0, 0, waterCanvas.width, waterCanvas.height);
          ctx.drawImage(this, 0, 0, waterCanvas.width, waterCanvas.height);
          ctx.fillStyle = "rgba(0, 0, 0, 0.5)";
          ctx.fillRect(0, waterCanvas.height - 176, waterCanvas.width, 176);
          ctx.fillStyle = "#ddd";
          ctx.font = "20px Arial";
          ctx.textBaseline = "middle";
          ctx.fillText(that_.visit_info.shop_name, 20, waterCanvas.height - 152);
          if (typeof that_.visit_info.address != 'undefined') {
            if (that_.visit_info.address.indexOf("(") !== -1) {
              const addrArr = that_.visit_info.address.split("(");
              ctx.fillText(addrArr[0], 20, waterCanvas.height - 120);
              ctx.fillText(addrArr[1], 20, waterCanvas.height - 88);
            } else {
              ctx.fillText(that_.visit_info.address, 20, waterCanvas.height - 120);
            }
          }
          ctx.fillText("业务员: " + operName + " " + that_.getCurrentTime(), 20, waterCanvas.height - 56);
          if (distance && distance >= 0)
            ctx.fillText("签到距离:" + parseInt(distance) + "m", 20, waterCanvas.height - 24);
          console.log("canvas高", waterCanvas.height);
          var newBaseImage = waterCanvas.toDataURL("image/jpeg"); //压缩语句
          console.log("水印marker", newBaseImage);
          resolve(newBaseImage);
        };
      });
    },
    async getCurPositionToSupcustDistance(sup_lat, sup_lng) {
      var res = null;
      if (this.lastPosition && this.lastPosition.result ===  "OK") {
        var now = new Date();
        var passSeconds = now.getTime() - this.lastPosition.time.getTime();
        if (passSeconds < 30000) res = this.lastPosition;
      }
      if (!res) res = await this.getCurPosition();
      if (res.result !== "OK") {
        this.$toast(res.msg);
        return null;
      }
      return this.getDistance(
          res.latitude,
          res.longitude,
          sup_lat,
          sup_lng
      );
    },
    async compressImage(origenBaseImg, w) {
      var newImage = new Image();
      var quality = 0.5; //压缩系数0-1之间
      return new Promise((resolve, reject) => {
        newImage.src = origenBaseImg;
        newImage.setAttribute("crossOrigin", "Anonymous"); //url为外域时需要
        var imgWidth, imgHeight;
        newImage.onload = function () {
          imgWidth = this.width;
          imgHeight = this.height;
          var canvas = document.createElement("canvas");
          var ctx = canvas.getContext("2d");
          if (Math.max(imgWidth, imgHeight) > w) {
            if (imgWidth > imgHeight) {
              canvas.width = w;
              canvas.height = (w * imgHeight) / imgWidth;
            } else {
              canvas.height = w;
              canvas.width = (w * imgWidth) / imgHeight;
            }
          } else {
            canvas.width = imgWidth;
            canvas.height = imgHeight;
            quality = 0.8;
          }
          ctx.clearRect(0, 0, canvas.width, canvas.height);
          ctx.drawImage(this, 0, 0, canvas.width, canvas.height);
          var newBaseImage = canvas.toDataURL("image/jpeg", quality); //压缩语句
          // 如想确保图片压缩到自己想要的尺寸,如要求在50-150kb之间，请加以下语句，quality初始值根据情况自定
          while (newBaseImage.length / 1024 > 150) {
            quality -= 0.001;
            newBaseImage = canvas.toDataURL("image/jpeg", quality);
          }
          // 防止最后一次压缩低于最低尺寸，只要quality递减合理，无需考虑
          // while (newBaseImage.length / 1024 < 50) {
          //     quality += 0.001;
          //     newBaseImage = canvas.toDataURL("image/jpeg", quality);
          // }
          console.log("压缩", newBaseImage);
          // console.log("data:image/jpeg;base64,"+newBaseImage)
          resolve(newBaseImage);
        };
      });
    },
    getCurrentTime() {
      let myDate = new Date();
      let moths = (myDate.getMonth() + 101 + "").substring(1);
      let getHours = myDate.getHours();
      let getMinutes = (myDate.getMinutes() + 100 + "").substring(1);
      return myDate.getFullYear() +
          "-" +
          moths +
          "-" +
          myDate.getDate() +
          " " +
          getHours +
          ":" +
          getMinutes +
          ":" +
          myDate.getSeconds();
    },
    handleShowPreView(list,index) {
      const showList = list.filter(item => {
        return item !== ''
      })
      if (showList.length > 0) {
        ImagePreview({
          images: showList,
          startPosition: index,
          closeable: true,
        });
      }

    },
    handlePhotoListSplice(list,index,mandatoryOrOptionalFlag) {
      if (mandatoryOrOptionalFlag) {
        this.$set(list, index, "")
        this.$set(this.photoList.mandatoryName, index, "")
      } else {
        list.splice(index,1)
        this.photoList.optionalName.splice(index,1)
      }
    },
    handleCheckResult() {
      let messageError = "";
      // 判断是否存在0
      let mandatoryNum = []
      mandatoryNum = this.photoList.mandatory.filter(item => item !== '')
      if(this.photoAction.minNum === 0 || mandatoryNum.length >= this.photoAction.minNum) {
        return messageError
      }
      for (let i = 0; i < this.photoList.mandatory.length; i++) {
        if (this.photoList.mandatory[i] === '') {
            console.log('VisitTemplatePhoto.vue,this.photoAction.items.length', this.photoAction)
            if (this.photoAction.items.length > 0) {
                if (i < this.photoAction.items.length) { // 兼容没有提示的情况
                    messageError = this.photoAction.name + ':' + this.photoAction.items[i].title + "未拍照";
                } else {
                    messageError = this.photoAction.name + "未拍照";
                }
            } else {
                messageError = this.photoAction.name + "未拍照";
            }

          return messageError
        }
      }
      return messageError
    },
    nanoid (t=21) {
      return crypto.getRandomValues(new Uint8Array(t)).reduce(((t,e)=>t+=(e&=63)<36?e.toString(36):e<62?(e-26).toString(36).toUpperCase():e>62?"-":"_"),"")
    },
    handleWorkContent() {
      if (this.isRecordVisit) {
        for (let i = 0; i < this.workContent.mandatory.length; i++) {
          if (this.workContent.mandatory[i].indexOf('data:') === 0 || this.workContent.mandatory[i].indexOf(globalVars.obs_server_uri) === 0) {
            this.$set(this.photoList.mandatory, i,  this.workContent.mandatory[i])
          } else {
            this.$set(this.photoList.mandatory, i, globalVars.obs_server_uri + '/' + this.workContent.mandatory[i])
          }
        }
        for (let i = 0; i < this.workContent.optional.length; i++) {
          if (this.workContent.optional[i].indexOf('data:') === 0 || this.workContent.optional[i].indexOf(globalVars.obs_server_uri) === 0) {
            this.$set(this.photoList.optional, i, this.workContent.optional[i])
          } else {
            this.$set(this.photoList.optional, i, globalVars.obs_server_uri + '/' + this.workContent.optional[i])
          }
        }
      }
    },
    handleLoadStore(i) {
      this.$store.state.unSubmitVisitTemplate[i].actionResult.mandatory.forEach((value,index) => {
        this.$set(this.photoList.mandatory, index, value)
      })
      this.$store.state.unSubmitVisitTemplate[i].actionResult.mandatoryName.forEach((value,index) => {
        this.$set(this.photoList.mandatoryName, index, value)
      })
      this.$store.state.unSubmitVisitTemplate[i].actionResult.optional.forEach((value,index) => {
        this.$set(this.photoList.optional, index, value)
      })
      this.$store.state.unSubmitVisitTemplate[i].actionResult.optionalName.forEach((value,index) => {
        this.$set(this.photoList.optionalName, index, value)
      })
    }
  }
}
</script>

<style scoped lang="less">
.action-wrapper {
  min-width: 200px;
  display: flex;
  flex-direction: column;
  border-radius: 5px;
  padding: 0 10px;
  .action-item {
    display: flex;
    align-items: center;
    height: 35px;
    box-sizing: border-box;
    margin: 2px 5px;
  }
  .action-item +.action-item {
    border-top: 1px solid #eee;
  }
}
.visit-photo-wrapper {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding: 10px ;
  .photo-title-tip {
    display: flex;
    .photo-title-tip-left {
      flex: 1;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .photo-title {
        font-size: 18px;
        font-weight: bolder;
      }
      .photo-tip {
        flex: 1;
        height: 100%;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-left: 10px;
        color: #aaaaaa;
        font-size: 14px;
        text-align: left;
      }
    }
    .photo-title-tip-right {
      font-size: 16px;
      color: #aaaaaa;
    }

  }
  .photo-content {
    margin-top: 10px;
    display: flex;
    overflow-x: auto;
    .photo-content-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 65px;
      margin: 0 10px;
      .photo-item-img {
        width: 50px;
        height: 50px;
        .photo-add{
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          border: 1px dotted #ddd;
          border-radius: 10px;
        }
        .photo-img{
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          border: 1px dotted #ddd;
          border-radius: 10px;
          position: relative;
          img {
            width: 100%;
            height: 100%;
          }
          .photo-img-close {
            position: absolute;
            right: -8px;
            top: -2px;
            color: #ee0a24;
            font-size: 20px;
          }
        }
      }
      .photo-item-title {
        margin-top: 10px;
        color: #aaa
      }
    }
    .photo-opt {
      color: #aaaaaa;
      .photo-add-opt {
        border: none !important;
      }
      .photo-item-title {
        font-size: 14px;
      }
    }
    .photo-content-item  + .photo-content-item {
      margin-left: 15px;
    }

  }
}
</style>
