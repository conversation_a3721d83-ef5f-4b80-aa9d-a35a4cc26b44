<template>
  <div ref="pages" class="pages" id="pages" style="">
    <my-preview-image v-if="showImagePreviewFlag" @closePreviewEvent="showImagePreviewFlag = false"
      :images="[previewItemImageUrl]"></my-preview-image>
    <!-- 标题栏start -->
    <div ref="publicBox2" @click='title_click'>
      <van-nav-bar left-arrow safe-area-inset-top title="标签打印单" @click-left.stop="stayOrLeave">
        <template #right>
          <div>
            <yj-dot-menu @menuGo="menuGo" :menuVals="[{name:'历史单据',url:'/ViewSheetAll?queryParams='+JSON.stringify({startDate:calcThreeMonthAgo,endDate:calcNowDate,sheetType: 'BQ'})+'&&sheetTabType=BQ&timestamp='+Date.now()}]"></yj-dot-menu>
          </div>
          <svg style="margin:2px 2px 0 20px " @click.stop="submitLabelPrintSheet" width="30px" height="30px" stroke-width="1.3" class="black">
            <use :xlink:href="'#icon-plane'"></use>
          </svg>
        </template>
      </van-nav-bar>
    </div>

    <!-- 标题栏end -->
    <!-- 中间内容 -->
    <div ref="publicBox3" class="public_box3">
      <!-- 单据头部信息 -->
      <div class="public_query">
        <div class="public_query_title" v-if="sheet.sheet_no">
          <div style="padding-top: 5px;padding-left: 25px;" class="public_query_title_t sheet_info">
            <span>{{ sheet.sheet_no }}</span>
          </div>
          <div style="padding-top: 5px;padding-left: 25px;text-align: left;" v-if="sheet.happen_time"
            class="public_query_title_t sheet_info">
            <span>交易时间: {{ sheet.happen_time }}</span>
          </div>
          <div style="padding-top: 5px;padding-left: 25px;text-align: left;" v-if="sheet.make_time"
            class="public_query_title_t sheet_info">
            <span>制单:{{ sheet.maker_name }} {{ sheet.make_time }}</span>
          </div>
          <div style="padding-top: 5px;padding-left: 25px;text-align: left;" v-if="sheet.approve_time"
            class="public_query_title_t sheet_info">
            <span>审核:{{ sheet.approver_name }} {{ sheet.approve_time }}</span>
          </div>
        </div>
        
        <!-- 简化的单据头部信息，只保留默认单位 -->
        <div class="public_query_titleSrc">
          <div class="public_query_wrapper">
            <!-- 默认单位 -->
            <div class="public_query_titleSrc_item" style="width:100%">
              <van-icon name="wap-home-o" />
              <input type="text" style=" width: calc(100% - 60px);" v-model="sheet.branch_name" placeholder="默认单位" readonly />
            </div>
          </div>
        </div>
      </div>

      <div v-if="sheet.make_brief" class="sheet-mark-brief-wrapper">
        注: {{ sheet.make_brief }}
      </div>
      
      <ConcaveDottedCenter />
      <div class="approved_reded">
        <div class="sheet_state approved" v-if="sheet.approve_time && (sheet.red_flag == '' || sheet.red_flag == '0')">
          <img src="../../assets/images/approved.png" />
        </div>
        <div class="sheet_state reded" v-if="sheet.red_flag == '1'">
          <img src="../../assets/images/reded.png" />
        </div>
      </div>

      <!-- 商品列表 -->
      <div style="padding:0 10px">
        <div class="title" style="margin-top: 6px; color: #aaa">
          <van-row>
            <van-col style="text-align: left;display:flex;justify-content:space-between;" span="12">
              <div>品名</div>
              <div>
                <svg width="24px" height="18px" fill="#aaa" style="margin-top:1px;" @click="onSortByItemName">
                  <use xlink:href="#icon-sort"></use>
                </svg>
              </div>
            </van-col>
            <van-col style="text-align: right;" span="12">数量</van-col>
          </van-row>
        </div>
        <div :class="sheet.sheet_no ? 'sales_box_list_big' : 'sales_box_list'">
          <div class="sales_list">
            <div class="sales_list_boxs">
              <ul class="sales_ul">
                <SlickList axis="y" v-model="sheet.sheetRows" :pressDelay="500" helperClass="helper-class-slick-item"
                  :disabled="sheet.approve_time || sheet.red_flag === '1'">
                  <SlickItem v-for="(item, index) in sheet.sheetRows" :key="index" :index="index">
                    <van-swipe-cell>
                      <div style="display:flex;flex-direction:row;">
                        <div v-if="!isNoImagesInAllItems" class="sales_img_wrapper" style="width: 63px;height: 63px;">
                          <img @click="()=>{
                            showImagePreviewFlag = true
                            previewItemImageUrl = (item.showImages && item.showImages.main !== '') ? item.showImages.main : require('@/assets/images/default_good_img.png')
                            }" width="50" height="50" class="sales_img_wrapper" :src="(item.showImages && item.showImages.tiny !== '') ? item.showImages.tiny : require('@/assets/images/default_good_img.png')" fit="resize" />
                        </div>
                        <div @click="onRowEdit(item, index)" style="display: flex;align-items: center; width: 100%">
                          <div class="sales_ul_li"
                            style="border-bottom-style: solid;display: flex;flex-direction: column;justify-content: center;height: fix-content;">
                            <van-row style="width: 100%;">
                              <van-col span="24" style="justify-content:flex-start;height:100%;">
                                <span>{{ index + 1 }}、</span>
                                <span>{{ item.item_name }}
                                  <template v-if="item.attr_qty">
                                    {{ HandleNoStockAttrSplitShow(item.attr_qty) }}
                                  </template>
                                </span>
                              </van-col>
                            </van-row>
                            <div style="display:flex;width:100%;flex-wrap:wrap">
                              <div v-if="appSheetShowItemSpec && item.item_spec" class="other-info">{{ item.item_spec }}</div>
                              <div class="other-info"
                                v-if="item.s_barcode && (appShowBarcode == 'sbarcode' || (appShowBarcode == 'ubarcode' && item.unit_no == item.s_unit_no))">
                                {{ "小:" + item.s_barcode }}
                              </div>
                              <div class="other-info"
                                v-else-if="item.b_barcode && (appShowBarcode == 'bbarcode' || (appShowBarcode == 'ubarcode' && item.unit_no == item.b_unit_no))">
                                {{ "大:" + item.b_barcode }}
                              </div>
                              <div class="other-info"
                                v-else-if="item.m_barcode && (appShowBarcode == 'mbarcode' || (appShowBarcode == 'ubarcode' && item.unit_no == item.m_unit_no))">
                                {{ "中:" + item.m_barcode }}
                              </div>
                            </div>

                            <van-row style="width: 100%;flex-wrap: wrap;">
                              <van-col span="12" style="display:flex;">{{ toMoney(item.quantity, 3) }}<span style="width:1px"></span>{{ item.unit_no }}</van-col>
                              <van-col span="12" style="text-align:right;">
                                <span v-if="item.remark" style="color:#f88;">{{ item.remark }}</span>
                              </van-col>
                            </van-row>

                            <!-- 不区分库存信息的展示 -->
                            <div v-if="item.attr_qty && !item.item_id.startsWith('nanoid')" class="item_attr_qty_wrapper">
                              <div v-for="(attr, attrIndex) in  handleToJSON(item)" :key="attrIndex"
                                class="item_attr_qty_content">
                                {{ handleAttrNameShow(attr) }}:
                                <span style="font-family: numfont;">{{ attr.qty }}</span>{{ item.unit_no }}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <template #right>
                        <van-button v-if="!sheet.approve_time" class="delSheetRow" square type="danger" text="删除"
                          @click="btnRemoveRow_click(index)" />
                      </template>
                    </van-swipe-cell>
                  </SlickItem>
                </SlickList>
                <div class="total_msg">
                  <span v-if="sheetRowCount > 0">共 {{ sheetRowCount }} 行</span>
                  <span v-if="sheetRowCount > 0">{{ sumQuantityUnitConv }}</span>
                </div>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 中间内容 -->
    <!-- 底部合计 -->
    <div ref="publicBox4" class="total_money">
      <footer ref="salesListFooter" class="sales_list_footer">
        <div>
          <span style="color:#555;padding-left:15px">合计: </span><span class="sales_list_span">{{ sheetRowCount }} 行</span>
          <span v-if="sheetRowCount > 0" style="color:#555;padding-left:15px">{{ sumQuantityUnitConv }}</span>
        </div>
      </footer>
      <div class="sales_footer" style="padding: 0 15px 10px 10px;">
        <div class="footer_input">
          <input ref="inputCodesRef" id="codes" type="text" style="padding-left: 3px;" v-model="searchStr" enterkeyhint = "go"
               @keydown="onSearchInputKeyDown($event)" placeholder="名称/简拼/条码/货号"
            :disabled="sheet.approve_time ? true : false"
            :style="{ width: getSearchInputWidth(), backgroundColor: sheet.approve_time ? '#f2f2f2' : '' }" />
        </div>
        <div class="footer_iconBt" :disabled="sheet.approve_time !== '' || IsSubmiting" @click="btnClassView_click">
          <svg width="35px" height="35px" fill="#F56C6C">
            <use xlink:href="#icon-add"></use>
          </svg>
        </div>
        <div class="footer_iconBt" type="info" @click="btnScanBarcode_click">
          <svg width="30px" height="30px" fill="#555">
            <use xlink:href="#icon-barcodeScan"></use>
          </svg>
        </div>
      </div>
    </div>

    <!-- 弹窗模块 -->
    <van-popup v-model="popupSubmitPannel" :style="{ height: '100%', width: '80%', overflowY: 'auto' }" class="van_popup"
      position="right">
      <div style="height:30px;border-top:1px solid #ccc"></div>
      
      <!-- 审核界面 -->
      <div class="sales_more">
        <van-field v-model="sheet.make_brief" label="备注" :disabled="sheet.approve_time ? true : false" />
        
        <div class="sales_more_btn">
          <van-button v-if="!sheet.approve_time" @click="btnSave_click" :loading="IsSaving" loading-text="保存中..."
            type="info" size="large" style="margin-bottom:10px;">保存</van-button>
          <van-button v-if="!sheet.approve_time" @click="btnDelete_click" :loading="IsDeleting" loading-text="删除中..."
            type="danger" size="large" style="margin-bottom:10px;">删除</van-button>
          <van-button v-if="canPrint" @click="btnPrint_click" :loading="IsPrinting" loading-text="打印中..."
            type="primary" size="large" style="margin-bottom:10px;">打印</van-button>
        </div>
      </div>
    </van-popup>

    <!-- 添加商品弹窗 -->
    <van-popup v-model="popupAddSheetRow" :style="{ height: '100%', width: '100%' }" class="van_popup" position="bottom">
      <AddSheetRow v-if="popupAddSheetRow" ref="AddSheetRowRef" @popupAddSheetRowFalse="popupAddSheetRowFalse"
        @addSheetRowToSheet="addSheetRowToSheet" :sheet="sheet" :queryCondition="queryCondition"
        :onload_remarks="onload_remarks" :canSeeStock="canSeeStock" />
    </van-popup>

    <!-- 编辑商品弹窗 -->
    <van-popup v-model="popupEditSheetRow" :style="{ height: '100%', width: '100%' }" class="van_popup" position="bottom">
      <EditSheetRow v-if="popupEditSheetRow" ref="EditSheetRowRef" @popupEditSheetRowFalse="popupEditSheetRowFalse"
        @editSheetRowToSheet="editSheetRowToSheet" :sheet="sheet" :editSheetRowIndex="editSheetRowIndex"
        :editSheetRowItem="editSheetRowItem" />
    </van-popup>

    <!-- 选择商品弹窗 -->
    <van-popup v-model="popupSelectItems" :style="{ height: '100%', width: '100%' }" class="van_popup" position="bottom">
      <SelectItems v-if="popupSelectItems" ref="SelectItemsRef" @closeSelectItems="closeSelectItems"
        @addSheetRowToSheet="addSheetRowToSheet" :sheet="sheet" :queryCondition="queryCondition"
        :onload_remarks="onload_remarks" :canSeeStock="canSeeStock" />
    </van-popup>

    <!-- 多选商品弹窗 -->
    <van-popup v-model="popupMultiSelectItems" :style="{ height: '100%', width: '100%' }" class="van_popup" position="bottom">
      <SaleSheetMultiSelect v-if="popupMultiSelectItems" ref="SaleSheetMultiSelectRef" @closeMultiSelectItems="closeMultiSelectItems"
        @addSheetRowToSheet="addSheetRowToSheet" :sheet="sheet" :queryCondition="queryCondition"
        :onload_remarks="onload_remarks" :canSeeStock="canSeeStock" />
    </van-popup>
  </div>
</template>
