<template>
  <div ref="pages" class="pages" id="pages" style="">
    <my-preview-image v-if="showImagePreviewFlag" @closePreviewEvent="showImagePreviewFlag = false"
      :images="[previewItemImageUrl]"></my-preview-image>
    <!-- 标题栏start -->
    <div ref="publicBox2" @click='title_click'>
      <van-nav-bar left-arrow safe-area-inset-top title="标签打印单" @click-left.stop="stayOrLeave">
        <template #right>
          <div>
            <yj-dot-menu @menuGo="menuGo" :menuVals="[{name:'历史单据',url:'/ViewSheetAll?queryParams='+JSON.stringify({startDate:calcThreeMonthAgo,endDate:calcNowDate,sheetType: 'BQ'})+'&&sheetTabType=BQ&timestamp='+Date.now()}]"></yj-dot-menu>
          </div>
          <svg style="margin:2px 2px 0 20px " @click.stop="submitLabelPrintSheet" width="30px" height="30px" stroke-width="1.3" class="black">
            <use :xlink:href="'#icon-plane'"></use>
          </svg>
        </template>
      </van-nav-bar>
    </div>

    <!-- 标题栏end -->
    <!-- 中间内容 -->
    <div ref="publicBox3" class="public_box3">
      <!-- 单据头部信息 -->
      <div class="public_query">
        <div class="public_query_title" v-if="sheet.sheet_no">
          <div style="padding-top: 5px;padding-left: 25px;" class="public_query_title_t sheet_info">
            <span>{{ sheet.sheet_no }}</span>
          </div>
          <div style="padding-top: 5px;padding-left: 25px;text-align: left;" v-if="sheet.happen_time"
            class="public_query_title_t sheet_info">
            <span>交易时间: {{ sheet.happen_time }}</span>
          </div>
          <div style="padding-top: 5px;padding-left: 25px;text-align: left;" v-if="sheet.make_time"
            class="public_query_title_t sheet_info">
            <span>制单:{{ sheet.maker_name }} {{ sheet.make_time }}</span>
          </div>
          <div style="padding-top: 5px;padding-left: 25px;text-align: left;" v-if="sheet.approve_time"
            class="public_query_title_t sheet_info">
            <span>审核:{{ sheet.approver_name }} {{ sheet.approve_time }}</span>
          </div>
        </div>

        <!-- 简化的单据头部信息，只保留默认单位 -->
        <div class="public_query_titleSrc">
          <div class="public_query_wrapper">
            <!-- 默认单位 -->
            <div class="public_query_titleSrc_item" style="width:100%">
              <van-icon name="wap-home-o" />
              <input type="text" style=" width: calc(100% - 60px);" v-model="sheet.branch_name" placeholder="默认单位" readonly />
            </div>
          </div>
        </div>
      </div>

      <div v-if="sheet.make_brief" class="sheet-mark-brief-wrapper">
        注: {{ sheet.make_brief }}
      </div>

      <ConcaveDottedCenter />
      <div class="approved_reded">
        <div class="sheet_state approved" v-if="sheet.approve_time && (sheet.red_flag == '' || sheet.red_flag == '0')">
          <img src="../../assets/images/approved.png" />
        </div>
        <div class="sheet_state reded" v-if="sheet.red_flag == '1'">
          <img src="../../assets/images/reded.png" />
        </div>
      </div>

      <!-- 商品列表 -->
      <div style="padding:0 10px">
        <div class="title" style="margin-top: 6px; color: #aaa">
          <van-row>
            <van-col style="text-align: left;display:flex;justify-content:space-between;" span="12">
              <div>品名</div>
              <div>
                <svg width="24px" height="18px" fill="#aaa" style="margin-top:1px;" @click="onSortByItemName">
                  <use xlink:href="#icon-sort"></use>
                </svg>
              </div>
            </van-col>
            <van-col style="text-align: right;" span="12">数量</van-col>
          </van-row>
        </div>
        <div :class="sheet.sheet_no ? 'sales_box_list_big' : 'sales_box_list'">
          <div class="sales_list">
            <div class="sales_list_boxs">
              <ul class="sales_ul">
                <SlickList axis="y" v-model="sheet.sheetRows" :pressDelay="500" helperClass="helper-class-slick-item"
                  :disabled="sheet.approve_time || sheet.red_flag === '1'">
                  <SlickItem v-for="(item, index) in sheet.sheetRows" :key="index" :index="index">
                    <van-swipe-cell>
                      <div style="display:flex;flex-direction:row;">
                        <div v-if="!isNoImagesInAllItems" class="sales_img_wrapper" style="width: 63px;height: 63px;">
                          <img @click="()=>{
                            showImagePreviewFlag = true
                            previewItemImageUrl = (item.showImages && item.showImages.main !== '') ? item.showImages.main : require('@/assets/images/default_good_img.png')
                            }" width="50" height="50" class="sales_img_wrapper" :src="(item.showImages && item.showImages.tiny !== '') ? item.showImages.tiny : require('@/assets/images/default_good_img.png')" fit="resize" />
                        </div>
                        <div @click="onRowEdit(item, index)" style="display: flex;align-items: center; width: 100%">
                          <div class="sales_ul_li"
                            style="border-bottom-style: solid;display: flex;flex-direction: column;justify-content: center;height: fix-content;">
                            <van-row style="width: 100%;">
                              <van-col span="24" style="justify-content:flex-start;height:100%;">
                                <span>{{ index + 1 }}、</span>
                                <span>{{ item.item_name }}
                                  <template v-if="item.attr_qty">
                                    {{ HandleNoStockAttrSplitShow(item.attr_qty) }}
                                  </template>
                                </span>
                              </van-col>
                            </van-row>
                            <div style="display:flex;width:100%;flex-wrap:wrap">
                              <div v-if="appSheetShowItemSpec && item.item_spec" class="other-info">{{ item.item_spec }}</div>
                              <div class="other-info"
                                v-if="item.s_barcode && (appShowBarcode == 'sbarcode' || (appShowBarcode == 'ubarcode' && item.unit_no == item.s_unit_no))">
                                {{ "小:" + item.s_barcode }}
                              </div>
                              <div class="other-info"
                                v-else-if="item.b_barcode && (appShowBarcode == 'bbarcode' || (appShowBarcode == 'ubarcode' && item.unit_no == item.b_unit_no))">
                                {{ "大:" + item.b_barcode }}
                              </div>
                              <div class="other-info"
                                v-else-if="item.m_barcode && (appShowBarcode == 'mbarcode' || (appShowBarcode == 'ubarcode' && item.unit_no == item.m_unit_no))">
                                {{ "中:" + item.m_barcode }}
                              </div>
                            </div>

                            <van-row style="width: 100%;flex-wrap: wrap;">
                              <van-col span="12" style="display:flex;">{{ toMoney(item.quantity, 3) }}<span style="width:1px"></span>{{ item.unit_no }}</van-col>
                              <van-col span="12" style="text-align:right;">
                                <span v-if="item.remark" style="color:#f88;">{{ item.remark }}</span>
                              </van-col>
                            </van-row>

                            <!-- 不区分库存信息的展示 -->
                            <div v-if="item.attr_qty && !item.item_id.startsWith('nanoid')" class="item_attr_qty_wrapper">
                              <div v-for="(attr, attrIndex) in  handleToJSON(item)" :key="attrIndex"
                                class="item_attr_qty_content">
                                {{ handleAttrNameShow(attr) }}:
                                <span style="font-family: numfont;">{{ attr.qty }}</span>{{ item.unit_no }}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <template #right>
                        <van-button v-if="!sheet.approve_time" class="delSheetRow" square type="danger" text="删除"
                          @click="btnRemoveRow_click(index)" />
                      </template>
                    </van-swipe-cell>
                  </SlickItem>
                </SlickList>
                <div class="total_msg">
                  <span v-if="sheetRowCount > 0">共 {{ sheetRowCount }} 行</span>
                  <span v-if="sheetRowCount > 0">{{ sumQuantityUnitConv }}</span>
                </div>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 中间内容 -->

    <!-- 中间内容 -->
    <!-- 底部合计 -->
    <div ref="publicBox4" class="total_money">
      <footer ref="salesListFooter" class="sales_list_footer">
        <div>
          <span style="color:#555;padding-left:15px">合计: </span><span class="sales_list_span">{{ sheetRowCount }} 行</span>
          <span v-if="sheetRowCount > 0" style="color:#555;padding-left:15px">{{ sumQuantityUnitConv }}</span>
        </div>
      </footer>
      <div class="sales_footer" style="padding: 0 15px 10px 10px;">
        <div class="footer_input">
          <input ref="inputCodesRef" id="codes" type="text" style="padding-left: 3px;" v-model="searchStr" enterkeyhint = "go"
               @keydown="onSearchInputKeyDown($event)" placeholder="名称/简拼/条码/货号"
            :disabled="sheet.approve_time ? true : false"
            :style="{ width: getSearchInputWidth(), backgroundColor: sheet.approve_time ? '#f2f2f2' : '' }" />
        </div>
        <div class="footer_iconBt" :disabled="sheet.approve_time !== '' || IsSubmiting" @click="btnClassView_click">
          <svg width="35px" height="35px" fill="#F56C6C">
            <use xlink:href="#icon-add"></use>
          </svg>
        </div>
        <div class="footer_iconBt" type="info" @click="btnScanBarcode_click">
          <svg width="30px" height="30px" fill="#555">
            <use xlink:href="#icon-barcodeScan"></use>
          </svg>
        </div>
      </div>
    </div>

    <!-- 弹窗模块 -->
    <van-popup v-model="popupSubmitPannel" :style="{ height: '100%', width: '80%', overflowY: 'auto' }" class="van_popup"
      position="right">
      <div style="height:30px;border-top:1px solid #ccc"></div>

      <!-- 审核界面 -->
      <div class="sales_more">
        <van-field v-model="sheet.make_brief" label="备注" :disabled="sheet.approve_time ? true : false" />

        <div class="sales_more_btn">
          <van-button v-if="!sheet.approve_time" @click="btnSave_click" :loading="IsSaving" loading-text="保存中..."
            type="info" size="large" style="margin-bottom:10px;">保存</van-button>
          <van-button v-if="!sheet.approve_time" @click="btnDelete_click" :loading="IsDeleting" loading-text="删除中..."
            type="danger" size="large" style="margin-bottom:10px;">删除</van-button>
          <van-button v-if="canPrint" @click="btnPrint_click" :loading="IsPrinting" loading-text="打印中..."
            type="primary" size="large" style="margin-bottom:10px;">打印</van-button>
        </div>
      </div>
    </van-popup>

    <!-- 添加商品弹窗 -->
    <van-popup v-model="popupAddSheetRow" :style="{ height: '100%', width: '100%' }" class="van_popup" position="bottom">
      <AddSheetRow v-if="popupAddSheetRow" ref="AddSheetRowRef" @popupAddSheetRowFalse="popupAddSheetRowFalse"
        @addSheetRowToSheet="addSheetRowToSheet" :sheet="sheet" :queryCondition="queryCondition"
        :onload_remarks="onload_remarks" :canSeeStock="canSeeStock" />
    </van-popup>

    <!-- 编辑商品弹窗 -->
    <van-popup v-model="popupEditSheetRow" :style="{ height: '100%', width: '100%' }" class="van_popup" position="bottom">
      <EditSheetRow v-if="popupEditSheetRow" ref="EditSheetRowRef" @popupEditSheetRowFalse="popupEditSheetRowFalse"
        @editSheetRowToSheet="editSheetRowToSheet" :sheet="sheet" :editSheetRowIndex="editSheetRowIndex"
        :editSheetRowItem="editSheetRowItem" />
    </van-popup>

    <!-- 选择商品弹窗 -->
    <van-popup v-model="popupSelectItems" :style="{ height: '100%', width: '100%' }" class="van_popup" position="bottom">
      <SelectItems v-if="popupSelectItems" ref="SelectItemsRef" @closeSelectItems="closeSelectItems"
        @addSheetRowToSheet="addSheetRowToSheet" :sheet="sheet" :queryCondition="queryCondition"
        :onload_remarks="onload_remarks" :canSeeStock="canSeeStock" />
    </van-popup>

    <!-- 多选商品弹窗 -->
    <van-popup v-model="popupMultiSelectItems" :style="{ height: '100%', width: '100%' }" class="van_popup" position="bottom">
      <SaleSheetMultiSelect v-if="popupMultiSelectItems" ref="SaleSheetMultiSelectRef" @closeMultiSelectItems="closeMultiSelectItems"
        @addSheetRowToSheet="addSheetRowToSheet" :sheet="sheet" :queryCondition="queryCondition"
        :onload_remarks="onload_remarks" :canSeeStock="canSeeStock" />
    </van-popup>

    <!-- 扫码功能直接集成在组件内部，不需要单独弹窗 -->
  </div>
</template>

<script>
import {
  NavBar,
  Field,
  Col,
  Row,
  DatetimePicker,
  Popup,
  Button,
  Icon,
  Picker,
  Popover,
  Toast,
  Dialog,
  SwipeCell,
  Image,
  Tag,
  NumberKeyboard,
  Checkbox
} from "vant";

import {
  AppSheetLabelPrintLoad,
  AppSheetLabelPrintSave,
  AppSheetLabelPrintDelete,
  AppCloudPrint_sheetTmp,
  AppCloudPrint_escCmd,
  AppSheetToEsc,
  AppSheetToImages,
  AppGetSheetToPrint,
  AppGetSheetToPrint_Post,
  AppGetTemplate,
  ApiPrintMark
} from "../../api/api";

import Printing from "../Printing/Printing";
import EditSheetRow from "./EditSheetRow";
import AddSheetRow from "./AddSheetRow";
import SelectItems from "./SelectItems";
import SaleSheetMultiSelect from "./SaleSheetMultiSelect";
// import ScanBarcode from "../components/ScanBarcode"; // 不需要单独的扫码组件
import mixins from './sheetMixin/mixin';
import sheetCache from "../../util/sheetCache";
import ConcaveDottedCenter from "../components/ConcaveDottedCenter";
import YJDotMenu from '../components/YJDotMenu.vue';
import MyPreviewImage from '../VisitRecord/MyPreviewImage.vue';
import { SlickList, SlickItem } from 'vue-slicksort';

export default {
  name: "LabelPrintSheet",
  mixins: [mixins],
  components: {
    NavBar,
    Field,
    Col,
    Row,
    DatetimePicker,
    Popup,
    Button,
    Icon,
    Picker,
    Popover,
    SwipeCell,
    Image,
    Tag,
    NumberKeyboard,
    Checkbox,
    EditSheetRow,
    AddSheetRow,
    SelectItems,
    SaleSheetMultiSelect,
    // ScanBarcode, // 不需要单独的扫码组件
    ConcaveDottedCenter,
    YJDotMenu,
    MyPreviewImage,
    SlickList,
    SlickItem
  },
  data() {
    return {
      // 单据数据
      sheet: {
        sheetType: 'BQ',
        sheet_id: '',
        sheet_no: '',
        branch_id: '',
        branch_name: '',
        make_brief: '',
        maker_name: '',
        make_time: '',
        approver_name: '',
        approve_time: '',
        happen_time: '',
        red_flag: '',
        sheetRows: []
      },

      // 弹窗状态
      popupSubmitPannel: false,
      popupAddSheetRow: false,
      popupEditSheetRow: false,
      popupSelectItems: false,
      popupMultiSelectItems: false,
      // popupScanBarcode: false, // 不需要扫码弹窗

      // 编辑状态
      editSheetRowIndex: -1,
      editSheetRowItem: {},

      // 搜索相关
      searchStr: '',
      queryCondition: {},
      onload_remarks: [],

      // 状态标志
      IsSubmiting: false,
      IsSaving: false,
      IsDeleting: false,
      IsPrinting: false,

      // 图片预览
      showImagePreviewFlag: false,
      previewItemImageUrl: '',

      // 其他
      canSeeStock: true,
      canPrint: true
    };
  },
  computed: {
    // 计算总行数
    sheetRowCount() {
      return this.sheet.sheetRows ? this.sheet.sheetRows.length : 0;
    },

    // 计算总数量
    sumQuantityUnitConv() {
      if (!this.sheet.sheetRows || this.sheet.sheetRows.length === 0) {
        return '';
      }

      let totalQty = 0;
      this.sheet.sheetRows.forEach(row => {
        totalQty += parseFloat(row.quantity || 0);
      });

      return `总数量: ${this.toMoney(totalQty, 3)}`;
    },

    // 计算三个月前日期
    calcThreeMonthAgo() {
      const date = new Date();
      date.setMonth(date.getMonth() - 3);
      return date.toISOString().split('T')[0];
    },

    // 计算当前日期
    calcNowDate() {
      return new Date().toISOString().split('T')[0];
    },

    // 判断是否所有商品都没有图片
    isNoImagesInAllItems() {
      if (!this.sheet.sheetRows || this.sheet.sheetRows.length === 0) {
        return true;
      }
      return this.sheet.sheetRows.every(item =>
        !item.showImages || !item.showImages.tiny
      );
    },

    // 应用设置
    appSheetShowItemSpec() {
      return window.getSettingValue('appSheetShowItemSpec') === 'true';
    },

    appShowBarcode() {
      return window.getSettingValue('appShowBarcode') || 'sbarcode';
    }
  },

  mounted() {
    this.initSheet();
  },

  methods: {
    // 初始化单据
    async initSheet() {
      try {
        const sheetID = this.$route.query.sheetID;
        if (sheetID && sheetID !== 'new') {
          // 加载现有单据
          await this.loadSheet(sheetID);
        } else {
          // 创建新单据
          await this.createNewSheet();
        }
      } catch (error) {
        console.error('初始化单据失败:', error);
        Toast('初始化单据失败');
      }
    },

    // 加载现有单据
    async loadSheet(sheetID) {
      try {
        const response = await AppSheetLabelPrintLoad({ sheet_id: sheetID });
        if (response.result === 'OK') {
          this.sheet = response.sheet;
          this.processSheetRows();
        } else {
          Toast(response.msg || '加载单据失败');
        }
      } catch (error) {
        console.error('加载单据失败:', error);
        Toast('加载单据失败');
      }
    },

    // 创建新单据
    async createNewSheet() {
      try {
        const response = await AppSheetLabelPrintLoad({ sheet_id: 'new' });
        if (response.result === 'OK') {
          this.sheet = {
            ...response.sheet,
            sheetType: 'BQ',
            sheetRows: []
          };
        } else {
          Toast(response.msg || '创建新单据失败');
        }
      } catch (error) {
        console.error('创建新单据失败:', error);
        Toast('创建新单据失败');
      }
    },

    // 处理单据行数据
    processSheetRows() {
      if (this.sheet.sheetRows && this.sheet.sheetRows.length > 0) {
        this.sheet.sheetRows.forEach(row => {
          // 处理图片显示
          if (row.item_images) {
            try {
              row.showImages = typeof row.item_images === 'string'
                ? JSON.parse(row.item_images)
                : row.item_images;
            } catch (e) {
              row.showImages = { tiny: '', main: '' };
            }
          } else {
            row.showImages = { tiny: '', main: '' };
          }
        });
      }
    },

    // 提交标签打印单
    async submitLabelPrintSheet() {
      if (this.IsSubmiting) return;

      if (!this.sheet.sheetRows || this.sheet.sheetRows.length === 0) {
        Toast('请先添加商品');
        return;
      }

      try {
        this.IsSubmiting = true;

        // 先保存单据
        const saveResult = await this.saveSheet();
        if (!saveResult) {
          return;
        }

        // 这里可以添加提交逻辑，目前标签打印单主要是保存和打印
        Toast('提交成功');

        // 可以选择跳转到历史单据页面或关闭当前页面
        this.stayOrLeave();

      } catch (error) {
        console.error('提交失败:', error);
        Toast('提交失败');
      } finally {
        this.IsSubmiting = false;
      }
    },

    // 保存单据
    async saveSheet() {
      if (this.IsSaving) return false;

      try {
        this.IsSaving = true;

        const sheetData = {
          ...this.sheet,
          sheetRows: this.sheet.sheetRows.map(row => ({
            ...row,
            // 确保必要字段存在
            quantity: row.quantity || 1,
            unit_no: row.unit_no || row.sunit || '个',
            real_price: 0, // 标签打印单不需要价格
            sub_amount: 0  // 标签打印单不需要金额
          }))
        };

        const response = await AppSheetLabelPrintSave(sheetData);
        if (response.result === 'OK') {
          this.sheet = response.sheet;
          Toast('保存成功');
          return true;
        } else {
          Toast(response.msg || '保存失败');
          return false;
        }
      } catch (error) {
        console.error('保存失败:', error);
        Toast('保存失败');
        return false;
      } finally {
        this.IsSaving = false;
      }
    },

    // 保存按钮点击
    async btnSave_click() {
      await this.saveSheet();
    },

    // 删除单据
    async btnDelete_click() {
      if (this.IsDeleting) return;

      if (!this.sheet.sheet_id) {
        Toast('无法删除未保存的单据');
        return;
      }

      try {
        await Dialog.confirm({
          title: '确认删除',
          message: '确定要删除这张标签打印单吗？'
        });

        this.IsDeleting = true;

        const response = await AppSheetLabelPrintDelete({ sheet_id: this.sheet.sheet_id });
        if (response.result === 'OK') {
          Toast('删除成功');
          this.stayOrLeave();
        } else {
          Toast(response.msg || '删除失败');
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除失败:', error);
          Toast('删除失败');
        }
      } finally {
        this.IsDeleting = false;
      }
    },

    // 打印单据
    async btnPrint_click() {
      if (this.IsPrinting) return;

      if (!this.sheet.sheet_id) {
        Toast('请先保存单据');
        return;
      }

      if (!this.sheet.sheetRows || this.sheet.sheetRows.length === 0) {
        Toast('请先添加商品');
        return;
      }

      try {
        this.IsPrinting = true;

        // 使用标签打印功能
        await this.printLabelSheet();

      } catch (error) {
        console.error('打印失败:', error);
        Toast('打印失败');
      } finally {
        this.IsPrinting = false;
      }
    },

    // 标签打印
    async printLabelSheet() {
      try {
        // 这里调用标签打印相关的API
        const printData = {
          sheet: this.sheet,
          sheetType: 'BQ'
        };

        // 调用打印API - 使用标签打印功能
        if (Printing.printSaleSheet) {
          Printing.printSaleSheet(printData, (result) => {
            if (result.result === 'OK') {
              Toast('打印成功');
            } else {
              Toast(result.msg || '打印失败');
            }
          });
        } else {
          // 如果没有专门的标签打印方法，使用通用打印
          Toast('打印功能待完善');
        }

      } catch (error) {
        console.error('标签打印失败:', error);
        Toast('标签打印失败');
      }
    },

    // 添加商品到单据
    addSheetRowToSheet(sheetRow) {
      if (!this.sheet.sheetRows) {
        this.sheet.sheetRows = [];
      }

      // 处理图片显示
      if (sheetRow.item_images) {
        try {
          sheetRow.showImages = typeof sheetRow.item_images === 'string'
            ? JSON.parse(sheetRow.item_images)
            : sheetRow.item_images;
        } catch (e) {
          sheetRow.showImages = { tiny: '', main: '' };
        }
      } else {
        sheetRow.showImages = { tiny: '', main: '' };
      }

      // 设置默认值
      sheetRow.quantity = sheetRow.quantity || 1;
      sheetRow.real_price = 0; // 标签打印单不需要价格
      sheetRow.sub_amount = 0; // 标签打印单不需要金额

      this.sheet.sheetRows.push(sheetRow);
      this.closeAllPopups();
    },

    // 编辑单据行
    editSheetRowToSheet(sheetRow, index) {
      if (index >= 0 && index < this.sheet.sheetRows.length) {
        // 处理图片显示
        if (sheetRow.item_images) {
          try {
            sheetRow.showImages = typeof sheetRow.item_images === 'string'
              ? JSON.parse(sheetRow.item_images)
              : sheetRow.item_images;
          } catch (e) {
            sheetRow.showImages = { tiny: '', main: '' };
          }
        } else {
          sheetRow.showImages = { tiny: '', main: '' };
        }

        // 设置默认值
        sheetRow.real_price = 0; // 标签打印单不需要价格
        sheetRow.sub_amount = 0; // 标签打印单不需要金额

        this.sheet.sheetRows.splice(index, 1, sheetRow);
      }
      this.closeAllPopups();
    },

    // 删除单据行
    btnRemoveRow_click(index) {
      if (index >= 0 && index < this.sheet.sheetRows.length) {
        this.sheet.sheetRows.splice(index, 1);
      }
    },

    // 编辑行点击
    onRowEdit(item, index) {
      if (this.sheet.approve_time) return; // 已审核不能编辑

      this.editSheetRowItem = { ...item };
      this.editSheetRowIndex = index;
      this.popupEditSheetRow = true;
    },

    // 排序商品
    onSortByItemName() {
      if (this.sheet.sheetRows && this.sheet.sheetRows.length > 0) {
        this.sheet.sheetRows.sort((a, b) => {
          return a.item_name.localeCompare(b.item_name, 'zh-CN');
        });
      }
    },

    // 搜索输入键盘事件
    onSearchInputKeyDown(event) {
      if (event.key === 'Enter') {
        this.searchItems();
      }
    },

    // 搜索商品
    searchItems() {
      if (!this.searchStr.trim()) {
        Toast('请输入搜索内容');
        return;
      }

      this.queryCondition = {
        searchStr: this.searchStr.trim()
      };

      this.popupSelectItems = true;
    },

    // 添加商品按钮点击
    btnClassView_click() {
      if (this.sheet.approve_time) return; // 已审核不能添加

      this.popupSelectItems = true;
    },

    // 扫码按钮点击
    async btnScanBarcode_click() {
      if (this.sheet.approve_time) return; // 已审核不能扫码

      try {
        const result = await this.getScanBarResult();
        this.searchStr = result.code;
        this.searchItems();
      } catch (error) {
        console.error('扫码失败:', error);
      }
    },

    // 获取扫码结果
    getScanBarResult(unit_type = '') {
      return new Promise((resolve, reject) => {
        const supportFormat = {
          Code128: true,
          Code39: true,
          Code93: true,
          CodaBar: true,
          DataMatrix: true,
          EAN13: true,
          EAN8: true,
          ITF: true,
          QRCode: false,
          UPCA: true,
          UPCE: true,
          PDF417: true,
          Aztec: true,
        };

        const config = {
          barcodeFormats: supportFormat,
          detectorSize: 0.5,
          beepOnSuccess: true,
          vibrateOnSuccess: true,
          torchOn: false,
          resultDisplayDuration: 500
        };

        if (window.cordova && window.cordova.plugins) {
          const plugin = window.cordova.plugins.mlkit?.barcodeScanner || window.cordova.plugins.barcodeScanner;

          if (plugin) {
            const useOldPlugin = typeof window.cordova.plugins.mlkit?.barcodeScanner == 'undefined';

            if (useOldPlugin) {
              plugin.scan(
                async (result) => {
                  const res = { unit_type, code: result.text, format: result.format };
                  resolve(res);
                },
                async (res) => {
                  reject(res);
                },
                config
              );
            } else {
              plugin.scan(
                config,
                async (result) => {
                  const res = { unit_type, code: result.text, format: result.format };
                  resolve(res);
                },
                async (res) => {
                  reject(res);
                }
              );
            }
          } else {
            reject('扫码插件未找到');
          }
        } else {
          // 开发环境模拟
          setTimeout(() => {
            resolve({ unit_type, code: '1234567890123', format: 'EAN13' });
          }, 1000);
        }
      });
    },

    // 关闭所有弹窗
    closeAllPopups() {
      this.popupAddSheetRow = false;
      this.popupEditSheetRow = false;
      this.popupSelectItems = false;
      this.popupMultiSelectItems = false;
      // this.popupScanBarcode = false; // 不需要扫码弹窗
      this.popupSubmitPannel = false;
    },

    // 弹窗关闭事件
    popupAddSheetRowFalse() {
      this.popupAddSheetRow = false;
    },

    popupEditSheetRowFalse() {
      this.popupEditSheetRow = false;
    },

    closeSelectItems() {
      this.popupSelectItems = false;
    },

    closeMultiSelectItems() {
      this.popupMultiSelectItems = false;
    },

    // closeScanBarcode() {
    //   this.popupScanBarcode = false;
    // },

    // 标题点击
    title_click() {
      this.popupSubmitPannel = !this.popupSubmitPannel;
    },

    // 离开页面确认
    stayOrLeave() {
      this.$router.go(-1);
    },

    // 菜单跳转
    menuGo(url) {
      this.$router.push(url);
    },

    // 获取搜索输入框宽度
    getSearchInputWidth() {
      return 'calc(100% - 120px)';
    },

    // 处理属性数量显示
    HandleNoStockAttrSplitShow(attrQty) {
      if (!attrQty) return '';

      try {
        const attrs = typeof attrQty === 'string' ? JSON.parse(attrQty) : attrQty;
        if (Array.isArray(attrs) && attrs.length > 0) {
          return attrs.map(attr => {
            const keys = Object.keys(attr).filter(key => key.startsWith('optName'));
            const names = keys.map(key => attr[key]).join(',');
            return `(${names})`;
          }).join('');
        }
      } catch (e) {
        console.error('解析属性数量失败:', e);
      }

      return '';
    },

    // 处理JSON数据
    handleToJSON(item) {
      if (!item.attr_qty) return [];

      try {
        return typeof item.attr_qty === 'string' ? JSON.parse(item.attr_qty) : item.attr_qty;
      } catch (e) {
        console.error('解析JSON失败:', e);
        return [];
      }
    },

    // 处理属性名称显示
    handleAttrNameShow(attr) {
      if (!attr) return '';

      const keys = Object.keys(attr).filter(key => key.startsWith('optName'));
      return keys.map(key => attr[key]).join(',');
    },

    // 金额格式化（从mixin继承，这里重新定义以确保可用）
    toMoney(value, precision = 2) {
      if (value === null || value === undefined || value === '') {
        return '0.00';
      }

      const num = parseFloat(value);
      if (isNaN(num)) {
        return '0.00';
      }

      return num.toFixed(precision);
    }
  }
};
</script>


.sheet-mark-brief-wrapper {
  width: 95%;
  color: #888;
  font-size: 14px;
  padding-left: 20px;
  padding-right: 5px;
  text-align: left;
  margin-top: 10px;
}

.sales_more_btn {
  padding: 20px;
}

.sales_more_btn .van-button {
  width: 100%;
  margin-bottom: 10px;
}

.other-info {
  font-size: 12px;
  color: #666;
  margin-right: 10px;
  margin-top: 5px;
}

.item_attr_qty_wrapper {
  display: flex;
  flex-wrap: wrap;
  margin-top: 5px;
}

.item_attr_qty_content {
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 3px;
  padding: 2px 5px;
  margin: 2px;
  font-size: 12px;
}

.sales_img_wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.sales_img_wrapper img {
  border-radius: 4px;
  object-fit: cover;
}
</style>